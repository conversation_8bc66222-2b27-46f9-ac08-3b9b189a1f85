@tailwind base;
@tailwind components;
@tailwind utilities;

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom CSS Variables */
:root {
  --primary-color: #6A11CB;
  --secondary-color: #0f0f0f;
  --accent-color: #f9f9f9;
  --text-primary: #333333;
  --text-secondary: #666666;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(-45deg, #6A11CB, #2575FC, #667eea, #764ba2);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Glass Effect */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-button {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Instra Theme Colors */
.instra-purple {
  color: #6A11CB;
}

.bg-instra-purple {
  background-color: #6A11CB;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #6A11CB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass-card {
    margin: 10px;
    border-radius: 12px;
  }
}

/* Button Styles */
.btn-primary {
  @apply bg-instra-purple text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-purple-700 hover:transform hover:scale-105;
}

.btn-secondary {
  @apply bg-transparent border-2 border-white text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-white hover:text-gray-900;
}

.btn-disabled {
  @apply bg-gray-500 text-gray-300 px-6 py-3 rounded-lg font-semibold cursor-not-allowed opacity-50;
  pointer-events: none;
  user-select: none;
}

.btn-success {
  @apply bg-green-500 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-green-600 hover:transform hover:scale-105;
}

/* Processing state for buttons */
.btn-processing {
  @apply cursor-not-allowed opacity-75;
  pointer-events: none;
  user-select: none;
}

/* Form Styles */
.form-input {
  @apply w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-instra-purple focus:ring-2 focus:ring-purple-200 transition-all duration-300 bg-white text-gray-900 placeholder-gray-500;
}

/* Password input with eye icon */
.form-input.pr-12 {
  padding-right: 3rem;
}

/* Eye icon button */
.password-toggle-btn {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-gray-800 transition-all duration-200 focus:outline-none focus:text-gray-800 rounded-full hover:bg-gray-100;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  cursor: pointer;
}

.password-toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.password-toggle-btn i {
  font-size: 14px;
}

/* Card Styles */
.feature-card {
  @apply glass-card p-6 text-center transition-all duration-300 hover:transform hover:scale-105;
}

/* Navigation Styles */
.nav-link {
  @apply glass-button px-4 py-2 text-white font-medium transition-all duration-300;
}

/* Video Protection Overlay */
.video-protection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 10;
  cursor: not-allowed;
  user-select: none;
  pointer-events: all;
}

.video-protection-overlay:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* Video container styles */
.video-container {
  position: relative;
  overflow: hidden;
}

.video-container iframe {
  pointer-events: none;
}

.video-container.watching iframe {
  pointer-events: none;
}

.video-container:not(.watching) iframe {
  pointer-events: auto;
}

/* Modal Scrolling */
.modal-scrollable {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.modal-scrollable::-webkit-scrollbar {
  width: 6px;
}

.modal-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.modal-scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.modal-scrollable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}
