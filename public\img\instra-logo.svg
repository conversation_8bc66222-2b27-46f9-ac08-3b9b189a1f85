<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="purpleGradient" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6A11CB"/>
      <stop offset="1" stop-color="#2575FC"/>
    </linearGradient>
  </defs>

  <!-- White circle background -->
  <circle cx="100" cy="100" r="95" fill="white" stroke="#6A11CB" stroke-width="3"/>

  <!-- Translation Symbol Background -->
  <rect x="40" y="40" width="120" height="120" rx="25" fill="url(#purpleGradient)"/>

  <!-- Language/Translation Icon -->
  <g transform="translate(100, 100)">
    <!-- Globe outline -->
    <circle cx="0" cy="0" r="35" fill="none" stroke="white" stroke-width="3"/>
    <!-- Language lines -->
    <path d="M-35 0 Q0 -20 35 0" stroke="white" stroke-width="2" fill="none"/>
    <path d="M-35 0 Q0 20 35 0" stroke="white" stroke-width="2" fill="none"/>
    <path d="M0 -35 L0 35" stroke="white" stroke-width="2"/>
    <!-- Translation arrows -->
    <path d="M-15 -15 L-5 -15 L-10 -10 Z" fill="white"/>
    <path d="M15 15 L5 15 L10 10 Z" fill="white"/>
  </g>

  <!-- "IG" Text for Instra Global -->
  <g transform="translate(85, 175)">
    <text font-family="Arial, sans-serif" font-weight="bold" font-size="20" fill="#6A11CB">IG</text>
  </g>
</svg>
