(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4317],{1451:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var r=t(5155),s=t(2115),o=t(3004),n=t(5317),i=t(6104),l=t(3592);function c(){let[e,a]=(0,s.useState)(""),[t,c]=(0,s.useState)(!1),[u,d]=(0,s.useState)({name:"Test User",email:"",mobile:"9876543210",password:"test123456",confirmPassword:"test123456",referralCode:""}),m=e=>{a(a=>a+e+"\n"),console.log(e)},D=e=>{let{name:a,value:t}=e.target;d(e=>({...e,[a]:t}))},E=async()=>{a(""),c(!0);try{var e,t,r;let a,s="test".concat(Date.now(),"@example.com"),c="Test Registration User",u="9876543210";m("\uD83D\uDE80 Starting registration test..."),m("\uD83D\uDCE7 Email: ".concat(s)),m("\uD83D\uDC64 Name: ".concat(c)),m("\uD83D\uDCF1 Mobile: ".concat(u)),m("\uD83D\uDD27 Firebase Project: ".concat("instra-global")),m("\n=== STEP 1: Creating Firebase Auth User ===");try{let e=(await (0,o.eJ)(i.j2,s,"test123456")).user;m("✅ Auth user created successfully!"),m("\uD83C\uDD94 UID: ".concat(e.uid)),m("\uD83D\uDCE7 Email: ".concat(e.email)),m("✅ Email Verified: ".concat(e.emailVerified))}catch(e){throw m("❌ Auth creation failed: ".concat(e.message)),m("❌ Auth error code: ".concat(e.code)),e}m("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,2e3)),m("✅ Auth state propagated"),m("Current auth user: ".concat(null==(e=i.j2.currentUser)?void 0:e.uid)),m("Auth state matches: ".concat((null==(t=i.j2.currentUser)?void 0:t.uid)===(null==(r=i.j2.currentUser)?void 0:r.uid))),m("\n=== STEP 3: Generating Referral Code ===");try{a=await (0,l.x4)(),m("✅ Generated referral code: ".concat(a))}catch(e){throw m("❌ Referral code generation failed: ".concat(e.message)),e}m("\n=== STEP 4: Preparing User Data ===");let d={[l.FIELD_NAMES.name]:c,[l.FIELD_NAMES.email]:s.toLowerCase(),[l.FIELD_NAMES.mobile]:u,[l.FIELD_NAMES.referralCode]:a,[l.FIELD_NAMES.referredBy]:"",[l.FIELD_NAMES.referralBonusCredited]:!1,[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:1,[l.FIELD_NAMES.joinedDate]:n.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalTranslations]:0,[l.FIELD_NAMES.todayTranslations]:0,[l.FIELD_NAMES.lastTranslationDate]:null,status:"active"};m("✅ User data prepared"),m("\uD83D\uDCCA Data keys: ".concat(Object.keys(d).join(", "))),m("\n=== STEP 5: Creating Firestore Document ===");let D=i.j2.currentUser;if(!D)throw m("❌ No current user found"),Error("No current user found");let E=(0,n.H9)(i.db,l.COLLECTIONS.users,D.uid);m("\uD83D\uDCCD Document path: ".concat(E.path)),m("\uD83D\uDD10 Current user UID: ".concat(D.uid)),m("\uD83D\uDCE7 Current user email: ".concat(D.email));try{await (0,n.BN)(E,d),m("✅ Firestore document created successfully!")}catch(e){throw m("❌ Firestore creation failed: ".concat(e.message)),m("❌ Firestore error code: ".concat(e.code)),m("❌ Full error: ".concat(JSON.stringify(e,null,2))),e}m("\n=== STEP 6: Verifying Document ===");try{let e=await (0,n.x7)(E);if(e.exists()){let a=e.data();m("✅ Document verification successful!"),m("\uD83D\uDCCA Document data keys: ".concat(Object.keys(a).join(", "))),m("\uD83D\uDC64 Name: ".concat(a[l.FIELD_NAMES.name])),m("\uD83D\uDCE7 Email: ".concat(a[l.FIELD_NAMES.email])),m("\uD83C\uDFAF Referral Code: ".concat(a[l.FIELD_NAMES.referralCode]))}else throw m("❌ Document was not created properly"),Error("Document verification failed")}catch(e){throw m("❌ Document verification failed: ".concat(e.message)),e}m("\n\uD83C\uDF89 Registration test completed successfully!")}catch(e){m("\n❌ Registration test failed!"),m("Error: ".concat(e.message)),m("Code: ".concat(e.code)),m("Stack: ".concat(e.stack)),console.error("Registration test error:",e)}finally{c(!1)}},p=async e=>{e.preventDefault(),a(""),c(!0);try{let e=u.email||"test".concat(Date.now(),"@example.com");if(m("\uD83D\uDE80 Starting FORM registration test..."),m("\uD83D\uDCE7 Email: ".concat(e)),m("\uD83D\uDC64 Name: ".concat(u.name)),m("\uD83D\uDCF1 Mobile: ".concat(u.mobile)),!u.name||!e||!u.mobile||!u.password)throw Error("Please fill in all required fields");if(u.password!==u.confirmPassword)throw Error("Passwords do not match");let a=(await (0,o.eJ)(i.j2,e,u.password)).user;m("✅ Auth user created: ".concat(a.uid));let t=await (0,l.x4)();m("✅ Referral code: ".concat(t));let r={[l.FIELD_NAMES.name]:u.name.trim(),[l.FIELD_NAMES.email]:e.toLowerCase(),[l.FIELD_NAMES.mobile]:u.mobile,[l.FIELD_NAMES.referralCode]:t,[l.FIELD_NAMES.referredBy]:u.referralCode||"",[l.FIELD_NAMES.referralBonusCredited]:!1,[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:1,[l.FIELD_NAMES.joinedDate]:n.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalTranslations]:0,[l.FIELD_NAMES.todayTranslations]:0,[l.FIELD_NAMES.lastTranslationDate]:null,status:"active"},s=(0,n.H9)(i.db,l.COLLECTIONS.users,a.uid);await (0,n.BN)(s,r),m("✅ Document created successfully!"),m("\n\uD83C\uDF89 FORM registration test completed successfully!")}catch(e){m("\n❌ FORM registration test failed!"),m("Error: ".concat(e.message)),m("Code: ".concat(e.code))}finally{c(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"glass-card p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Automated Test"}),(0,r.jsx)("button",{onClick:E,disabled:t,className:"btn-primary mb-6 w-full",children:t?"Testing Registration...":"Test Registration Process"}),(0,r.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Click the button to test registration process..."})]}),(0,r.jsxs)("div",{className:"glass-card p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Form Test"}),(0,r.jsxs)("form",{onSubmit:p,className:"space-y-4 mb-6",children:[(0,r.jsx)("input",{type:"text",name:"name",value:u.name,onChange:D,placeholder:"Full Name",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"email",name:"email",value:u.email,onChange:D,placeholder:"Email (leave empty for auto-generated)",className:"form-input"}),(0,r.jsx)("input",{type:"tel",name:"mobile",value:u.mobile,onChange:D,placeholder:"Mobile Number",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"password",name:"password",value:u.password,onChange:D,placeholder:"Password",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"password",name:"confirmPassword",value:u.confirmPassword,onChange:D,placeholder:"Confirm Password",className:"form-input",required:!0}),(0,r.jsx)("input",{type:"text",name:"referralCode",value:u.referralCode,onChange:D,placeholder:"Referral Code (Optional)",className:"form-input"}),(0,r.jsx)("button",{type:"submit",disabled:t,className:"btn-primary w-full",children:t?"Testing...":"Test Form Registration"})]}),(0,r.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Fill the form and submit to test..."})]})]})})})}},5804:(e,a,t)=>{Promise.resolve().then(t.bind(t,1451))},6104:(e,a,t)=>{"use strict";t.d(a,{db:()=>c,j2:()=>l});var r=t(3915),s=t(3004),o=t(5317),n=t(858);let i=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,s.xI)(i),c=(0,o.aU)(i);(0,n.c7)(i)}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>a(5804)),_N_E=e.O()}]);