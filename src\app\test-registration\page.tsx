'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS, generateSequentialReferralCode } from '@/lib/dataService'

export default function TestRegistrationPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const testReferralCodeGeneration = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      setResult(prev => prev + 'Testing referral code generation...\n')
      
      const referralCode = await generateSequentialReferralCode()
      setResult(prev => prev + `✓ Referral code generated: ${referralCode}\n`)
      
      // Test multiple generations
      for (let i = 0; i < 3; i++) {
        const code = await generateSequentialReferralCode()
        setResult(prev => prev + `✓ Code ${i + 2}: ${code}\n`)
      }
      
    } catch (error: any) {
      setResult(prev => prev + `✗ Error: ${error.message}\n`)
      console.error('Test error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const testFirestoreWrite = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      setResult(prev => prev + 'Testing Firestore write...\n')
      
      const testData = {
        name: 'Test User',
        email: '<EMAIL>',
        mobile: '9876543210',
        referralCode: 'TEST001',
        referredBy: '',
        plan: 'Trial',
        planExpiry: null,
        activeDays: 2,
        joinedDate: Timestamp.now(),
        wallet: 0,
        totalVideos: 0,
        todayVideos: 0,
        lastVideoDate: null,
        status: 'active'
      }
      
      const testDocId = `test_${Date.now()}`
      const testDoc = doc(db, COLLECTIONS.users, testDocId)
      
      setResult(prev => prev + `Creating document: ${testDoc.path}\n`)
      await setDoc(testDoc, testData)
      setResult(prev => prev + '✓ Document created successfully\n')
      
      // Verify document
      const docSnap = await getDoc(testDoc)
      if (docSnap.exists()) {
        setResult(prev => prev + '✓ Document verified successfully\n')
        setResult(prev => prev + `Data: ${JSON.stringify(docSnap.data(), null, 2)}\n`)
      } else {
        setResult(prev => prev + '✗ Document not found after creation\n')
      }
      
    } catch (error: any) {
      setResult(prev => prev + `✗ Error: ${error.message}\n`)
      console.error('Test error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const testFullRegistration = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      setResult(prev => prev + 'Testing full registration flow...\n')
      
      // Step 1: Create Firebase Auth user
      const testEmail = `test_${Date.now()}@example.com`
      const testPassword = 'test123456'
      
      setResult(prev => prev + `Creating auth user: ${testEmail}\n`)
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      const user = userCredential.user
      setResult(prev => prev + `✓ Auth user created: ${user.uid}\n`)
      
      // Step 2: Generate referral code
      setResult(prev => prev + 'Generating referral code...\n')
      const referralCode = await generateSequentialReferralCode()
      setResult(prev => prev + `✓ Referral code: ${referralCode}\n`)
      
      // Step 3: Create Firestore document
      const userData = {
        [FIELD_NAMES.name]: 'Test Registration User',
        [FIELD_NAMES.email]: testEmail,
        [FIELD_NAMES.mobile]: '9876543210',
        [FIELD_NAMES.referralCode]: referralCode,
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 1, // Start with 1 active day on registration
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalTranslations]: 0,
        [FIELD_NAMES.todayTranslations]: 0,
        [FIELD_NAMES.lastTranslationDate]: null,
        status: 'active'
      }
      
      setResult(prev => prev + 'Creating Firestore document...\n')
      const userDoc = doc(db, COLLECTIONS.users, user.uid)
      await setDoc(userDoc, userData)
      setResult(prev => prev + '✓ Firestore document created\n')
      
      // Step 4: Verify document
      const docSnap = await getDoc(userDoc)
      if (docSnap.exists()) {
        setResult(prev => prev + '✓ Document verified successfully\n')
        setResult(prev => prev + `✓ Full registration test completed successfully!\n`)
      } else {
        setResult(prev => prev + '✗ Document verification failed\n')
      }
      
      // Clean up - delete the test user
      setResult(prev => prev + 'Cleaning up test user...\n')
      await user.delete()
      setResult(prev => prev + '✓ Test user deleted\n')
      
    } catch (error: any) {
      setResult(prev => prev + `✗ Error: ${error.message}\n`)
      console.error('Test error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="glass-card p-6 mb-6">
          <h1 className="text-2xl font-bold text-white mb-4">Registration Debug Tests</h1>
          
          <div className="flex flex-wrap gap-4 mb-6">
            <button
              onClick={testReferralCodeGeneration}
              disabled={isLoading}
              className="btn-primary"
            >
              Test Referral Code Generation
            </button>
            
            <button
              onClick={testFirestoreWrite}
              disabled={isLoading}
              className="btn-primary"
            >
              Test Firestore Write
            </button>
            
            <button
              onClick={testFullRegistration}
              disabled={isLoading}
              className="btn-primary"
            >
              Test Full Registration
            </button>
          </div>
          
          {isLoading && (
            <div className="flex items-center mb-4">
              <div className="spinner mr-2"></div>
              <span className="text-white">Running test...</span>
            </div>
          )}
          
          <div className="bg-black/50 rounded-lg p-4 min-h-[200px]">
            <pre className="text-green-400 text-sm whitespace-pre-wrap font-mono">
              {result || 'Click a test button to start...'}
            </pre>
          </div>
        </div>
        
        <div className="glass-card p-6">
          <h2 className="text-xl font-bold text-white mb-4">Debug Information</h2>
          <div className="text-white/80 space-y-2">
            <p><strong>Firebase Project:</strong> {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}</p>
            <p><strong>Auth Domain:</strong> {process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}</p>
            <p><strong>Collections.users:</strong> {COLLECTIONS.users}</p>
            <p><strong>Field Names:</strong> {JSON.stringify(FIELD_NAMES, null, 2)}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
