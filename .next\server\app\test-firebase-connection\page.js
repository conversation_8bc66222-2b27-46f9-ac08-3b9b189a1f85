(()=>{var e={};e.id=6834,e.ids=[6834],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},16956:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),n=r(33784),o=r(63385),a=r(75535);function u(){let[e,t]=(0,i.useState)(""),[u,c]=(0,i.useState)(!1),l=async()=>{t(""),c(!0);try{t("\uD83D\uDD0D Testing Complete Registration Flow...\n"),t(e=>e+`Environment: ${window.location.origin}
`),t(e=>e+`Project ID: instra-global
`),t(e=>e+`Auth Domain: instra-global.firebaseapp.com

`),t(e=>e+"\uD83D\uDCE1 Test 1: Firebase Auth User Creation\n");let e=`regtest${Date.now()}@example.com`;try{t(t=>t+`Creating user: ${e}
`);let s=(await (0,o.eJ)(n.j2,e,"regtest123456")).user;t(e=>e+`✅ Auth user created: ${s.uid}
`),t(e=>e+`   Email: ${s.email}
`),t(e=>e+`   Email verified: ${s.emailVerified}
`),await new Promise(e=>setTimeout(e,1e3)),t(e=>e+`   Auth state: ${n.j2.currentUser?"authenticated":"not authenticated"}
`),t(e=>e+"\n\uD83D\uDCE1 Test 2: Creating User Document (Registration Style)\n");let{FIELD_NAMES:i,COLLECTIONS:u}=await r.e(3582).then(r.bind(r,3582)),c=Date.now().toString().slice(-4),l=Math.random().toString(36).substring(2,4).toUpperCase(),d=`MY${c}${l}`;t(e=>e+`Generated referral code: ${d}
`);let p={[i.name]:"Registration Test User",[i.email]:e.toLowerCase(),[i.mobile]:"9876543210",[i.referralCode]:d,[i.referredBy]:"",[i.referralBonusCredited]:!1,[i.plan]:"Trial",[i.planExpiry]:null,[i.activeDays]:0,[i.joinedDate]:new Date,[i.wallet]:0,[i.totalTranslations]:0,[i.todayTranslations]:0,[i.lastTranslationDate]:null,status:"active"};t(e=>e+`Document path: ${u.users}/${s.uid}
`),t(e=>e+`Field count: ${Object.keys(p).length}
`),t(e=>e+`Current auth UID: ${n.j2.currentUser?.uid}
`),t(e=>e+`Target UID: ${s.uid}
`),t(e=>e+`UIDs match: ${n.j2.currentUser?.uid===s.uid}
`);let x=(0,a.H9)(n.db,u.users,s.uid);try{t(e=>e+"\nAttempting setDoc...\n"),await (0,a.BN)(x,p),t(e=>e+"✅ User document created successfully!\n");let e=await (0,a.x7)(x);if(e.exists()){let r=e.data();t(e=>e+"✅ Document verification successful\n"),t(e=>e+`   Name: ${r[i.name]}
`),t(e=>e+`   Email: ${r[i.email]}
`),t(e=>e+`   Plan: ${r[i.plan]}
`),t(e=>e+`   Referral Code: ${r[i.referralCode]}
`),t(e=>e+"\n\uD83C\uDF89 REGISTRATION TEST SUCCESSFUL!\n"),t(e=>e+"The registration flow works perfectly.\n"),t(e=>e+"If registration is failing, check for:\n"),t(e=>e+"- Form validation errors\n"),t(e=>e+"- Network connectivity issues\n"),t(e=>e+"- Browser console errors\n")}else t(e=>e+"❌ Document verification failed\n")}catch(e){t(t=>t+`❌ setDoc failed: ${e.message}
`),t(t=>t+`   Error code: ${e.code}
`),t(t=>t+`   Full error: ${JSON.stringify(e,null,2)}
`),"permission-denied"===e.code&&(t(e=>e+"\n\uD83D\uDD27 PERMISSION ISSUE DETECTED:\n"),t(e=>e+"   - Check Firestore security rules\n"),t(e=>e+"   - Ensure rules allow authenticated users to write their own documents\n"),t(e=>e+"   - Verify the user is properly authenticated\n"))}try{await s.delete(),t(e=>e+"✅ Test user deleted\n")}catch(e){t(t=>t+`⚠️ User deletion failed: ${e.message}
`)}}catch(e){t(t=>t+`❌ Auth user creation failed: ${e.message}
`),t(t=>t+`   Code: ${e.code}
`),"auth/network-request-failed"===e.code&&(t(e=>e+"\n\uD83D\uDD27 NETWORK ISSUE DETECTED:\n"),t(e=>e+"   - Check your internet connection\n"),t(e=>e+"   - Try disabling VPN/proxy\n"),t(e=>e+"   - Check if firewall is blocking Firebase\n"),t(e=>e+"   - Try testing on a different network\n"))}}catch(e){t(t=>t+`❌ Test failed: ${e.message}
`),t(t=>t+`   Code: ${e.code}
`)}finally{c(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Registration Flow Test"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("button",{onClick:l,disabled:u,className:"btn-primary mb-4",children:u?"Testing Registration...":"Test Complete Registration Flow"}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap",children:e||'Click "Test Complete Registration Flow" to start...'})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19681:(e,t,r)=>{Promise.resolve().then(r.bind(r,22690))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-firebase-connection\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32833:(e,t,r)=>{Promise.resolve().then(r.bind(r,16956))},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>u});var s=r(67989),i=r(63385),n=r(75535),o=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),u=(0,i.xI)(a),c=(0,n.aU)(a);(0,o.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let c={children:["",{children:["test-firebase-connection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22690)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-firebase-connection\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-firebase-connection\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-firebase-connection/page",pathname:"/test-firebase-connection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,5901],()=>r(44116));module.exports=s})();