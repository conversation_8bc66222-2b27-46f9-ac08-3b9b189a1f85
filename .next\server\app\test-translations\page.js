(()=>{var e={};e.id=118,e.ids=[118],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8213:(e,t,s)=>{Promise.resolve().then(s.bind(s,53538))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28879:(e,t,s)=>{"use strict";s.d(t,{Dt:()=>l,PO:()=>c,Qy:()=>o,bQ:()=>d,cb:()=>r,jQ:()=>h});let a={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},r=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}];function n(e){let t=function(e){try{let t=localStorage.getItem(`${a.BATCH_PREFIX}${e}`);if(!t)return null;let s=JSON.parse(t);if(Date.now()-s.lastUpdated>864e5)return localStorage.removeItem(`${a.BATCH_PREFIX}${e}`),null;return s}catch(t){return console.error(`Error loading translation batch ${e}:`,t),null}}(e);return t?t.translations:[]}function i(){return n(parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0"))}function l(){let e=parseInt(localStorage.getItem(a.TOTAL_TRANSLATIONS)||"0"),t=parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0"),s=Math.ceil(e/100),r=n(t);return{totalTranslations:e,currentBatch:t,totalBatches:s,translationsInCurrentBatch:r.length}}function o(){Object.keys(localStorage).forEach(e=>{(e.startsWith(a.BATCH_PREFIX)||Object.values(a).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function d(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error(`Failed to load translations: ${e.statusText}`);let t=await e.json();console.log("Raw translation data loaded:",t.length,"entries");let s=[];return Array.isArray(t)&&t.forEach((e,t)=>{e.english&&s.push({id:`translation_${t}_${Date.now()}`,english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian,category:"General",batchIndex:Math.floor(s.length/100)})}),s}catch(e){throw console.error("Error loading translations from file:",e),e}}async function c(){try{if(!function(){let e=localStorage.getItem(a.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached translation data..."),i();{console.log("Loading fresh translation data...");let e=await d();return!function(e){let t=Math.ceil(e.length/100);for(let r=0;r<t;r++){let t=100*r,n=Math.min(t+100,e.length),i=e.slice(t,n);var s=r;try{let e={batchNumber:s,translations:i,totalTranslations:i.length,lastUpdated:Date.now()};localStorage.setItem(`${a.BATCH_PREFIX}${s}`,JSON.stringify(e))}catch(e){console.error(`Error saving translation batch ${s}:`,e)}}localStorage.setItem(a.TOTAL_TRANSLATIONS,e.length.toString()),localStorage.setItem(a.CURRENT_BATCH,"0"),localStorage.setItem(a.LAST_PROCESSED,Date.now().toString()),console.log(`Saved ${e.length} translations in ${t} batches`)}(e),i()}}catch(t){console.error("Error initializing translation system:",t);let e=i();if(e.length>0)return console.log("Using cached translations as fallback"),e;throw t}}function h(){let e=Math.floor(Math.random()*r.length);return r[e].code}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50664:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-translations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-translations\\page.tsx","default")},53538:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(60687),r=s(43210),n=s(28879);function i(){let[e,t]=(0,r.useState)([]),[s,i]=(0,r.useState)({totalTranslations:0,currentBatch:0,totalBatches:0,translationsInCurrentBatch:0}),[l,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)(null),h=()=>{i((0,n.Dt)())},x=async()=>{o(!0),c(null);try{let e=await (0,n.bQ)();t(e.slice(0,10)),h()}catch(e){c(e.message)}finally{o(!1)}},m=async()=>{o(!0),c(null);try{let e=await (0,n.PO)();t(e.slice(0,10)),h()}catch(e){c(e.message)}finally{o(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white mb-4",children:"Translation System Test"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:s.totalTranslations}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Total Translations"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:s.currentBatch}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Current Batch"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:s.totalBatches}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Total Batches"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-400",children:s.translationsInCurrentBatch}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"In Current Batch"})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("button",{onClick:x,disabled:l,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:l?"Loading...":"Load from File"}),(0,a.jsx)("button",{onClick:m,disabled:l,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:l?"Loading...":"Initialize System"}),(0,a.jsx)("button",{onClick:()=>{(0,n.Qy)(),t([]),h()},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Clear Storage"})]}),d&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:(0,a.jsxs)("p",{className:"text-red-400",children:["Error: ",d]})})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-white mb-4",children:["Loaded Translations (",e.length,")"]}),0===e.length?(0,a.jsx)("p",{className:"text-white/60 text-center py-8",children:"No translations loaded. Click a button above to test."}):(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("h3",{className:"text-white font-medium text-sm mb-2",children:"English Text:"}),(0,a.jsx)("p",{className:"text-white/90 bg-white/5 p-3 rounded border-l-4 border-blue-400",children:e.english})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-3",children:[e.hindi&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Hindi:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.hindi})]}),e.spanish&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Spanish:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.spanish})]}),e.french&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"French:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.french})]}),e.german&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"German:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.german})]}),e.italian&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Italian:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.italian})]}),e.portuguese&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Portuguese:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.portuguese})]})]}),(0,a.jsxs)("div",{className:"text-xs text-white/60 mt-3 space-y-1",children:[(0,a.jsxs)("p",{children:["ID: ",e.id]}),(0,a.jsxs)("p",{children:["Category: ",e.category]}),(0,a.jsxs)("p",{children:["Batch: ",e.batchIndex]})]})]},e.id))})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64506:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["test-translations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,50664)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-translations\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-translations\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-translations/page",pathname:"/test-translations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94645:(e,t,s)=>{Promise.resolve().then(s.bind(s,50664))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6204,5901],()=>s(64506));module.exports=a})();