(()=>{var e={};e.id=2475,e.ids=[2475],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33091:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\upload-users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\upload-users\\page.tsx","default")},33784:(e,s,t)=>{"use strict";t.d(s,{db:()=>d,j2:()=>n});var a=t(67989),r=t(63385),i=t(75535),l=t(70146);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),n=(0,r.xI)(o),d=(0,i.aU)(o);(0,l.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},36663:(e,s,t)=>{Promise.resolve().then(t.bind(t,45500))},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},45500:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(60687),r=t(43210),i=t(85814),l=t.n(i),o=t(65773),n=t(87979),d=t(63385),c=t(75535),u=t(33784),m=t(3582);function p(e){let s=[];if((!e.name||e.name.trim().length<2)&&s.push("Name is required and must be at least 2 characters"),e.email&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||s.push("Valid email address is required"),e.mobile&&/^[6-9]\d{9}$/.test(e.mobile)||s.push("Valid 10-digit mobile number is required"),(!e.password||e.password.length<6)&&s.push("Password is required and must be at least 6 characters"),e.plan&&!["Trial","Starter","Basic","Premium","Gold","Platinum","Diamond"].includes(e.plan)&&s.push("Plan must be one of: Trial, Starter, Basic, Premium, Gold, Platinum, Diamond"),e.activeDays&&(e.activeDays<0||e.activeDays>365)&&s.push("Active days must be between 0 and 365"),e.wallet&&e.wallet<0&&s.push("Wallet balance cannot be negative"),e.totalVideos&&e.totalVideos<0&&s.push("Total videos cannot be negative"),e.referralCode&&(/^MYN\d+$/.test(e.referralCode)||s.push("Referral code must follow format MYN0001, MYN0002, etc.")),void 0!==e.quickVideoAdvantage&&"boolean"!=typeof e.quickVideoAdvantage&&s.push("Quick video advantage must be true or false"),e.quickVideoAdvantageDays&&(e.quickVideoAdvantageDays<1||e.quickVideoAdvantageDays>365)&&s.push("Quick video advantage days must be between 1 and 365"),e.quickVideoAdvantageSeconds&&(e.quickVideoAdvantageSeconds<1||e.quickVideoAdvantageSeconds>420)&&s.push("Quick video advantage seconds must be between 1 and 420 (7 minutes)"),!0!==e.quickVideoAdvantage||e.quickVideoAdvantageDays||s.push("Quick video advantage days must be provided when quick advantage is enabled"),!0===e.quickVideoAdvantage&&e.quickVideoAdvantageSeconds){let s=[1,10,30,60,120,180,240,300,360,420];s.includes(e.quickVideoAdvantageSeconds)||console.warn(`Quick video advantage seconds ${e.quickVideoAdvantageSeconds} is not a common duration. Valid options: ${s.join(", ")}`)}return s}async function h(e,s){try{let t=(0,c.P)((0,c.collection)(u.db,m.COLLECTIONS.users),(0,c._M)(m.FIELD_NAMES.email,"==",e));if(!(await (0,c.getDocs)(t)).empty)return console.log(`Email ${e} already exists in Firestore`),!0;let a=(0,c.P)((0,c.collection)(u.db,m.COLLECTIONS.users),(0,c._M)(m.FIELD_NAMES.mobile,"==",s));if(!(await (0,c.getDocs)(a)).empty)return console.log(`Mobile ${s} already exists in Firestore`),!0;return!1}catch(e){return console.error("Error checking user existence:",e),!1}}async function x(e){try{let s=p(e);if(s.length>0)return{success:!1,error:s.join(", ")};if(await h(e.email,e.mobile))return{success:!1,error:"User with this email or mobile already exists (duplicate)"};let t=(await (0,d.eJ)(u.j2,e.email,e.password)).user,a=e.referralCode;if(a){let e=(0,c.P)((0,c.collection)(u.db,m.COLLECTIONS.users),(0,c._M)(m.FIELD_NAMES.referralCode,"==",a));if(!(await (0,c.getDocs)(e)).empty)throw Error(`Referral code ${a} already exists. Please use a unique referral code.`)}else a=await (0,m.x4)();let r=null;if(e.quickVideoAdvantage&&e.quickVideoAdvantageDays){let s=new Date;r=c.Dc.fromDate(new Date(s.getTime()+24*e.quickVideoAdvantageDays*36e5))}let i={[m.FIELD_NAMES.name]:e.name.trim(),[m.FIELD_NAMES.email]:e.email.toLowerCase(),[m.FIELD_NAMES.mobile]:e.mobile,[m.FIELD_NAMES.referralCode]:a,[m.FIELD_NAMES.referredBy]:e.referredBy||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:e.plan||"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:e.activeDays||1,[m.FIELD_NAMES.joinedDate]:e.joinedDate?new Date(e.joinedDate):c.Dc.now(),[m.FIELD_NAMES.wallet]:e.wallet||0,[m.FIELD_NAMES.totalVideos]:e.totalVideos||0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.videoDuration]:300,status:"active",[m.FIELD_NAMES.quickVideoAdvantage]:e.quickVideoAdvantage||!1,[m.FIELD_NAMES.quickVideoAdvantageExpiry]:r,[m.FIELD_NAMES.quickVideoAdvantageDays]:e.quickVideoAdvantageDays||0,[m.FIELD_NAMES.quickVideoAdvantageSeconds]:e.quickVideoAdvantageSeconds||30,[m.FIELD_NAMES.quickVideoAdvantageGrantedBy]:e.quickVideoAdvantageGrantedBy||"",[m.FIELD_NAMES.quickVideoAdvantageGrantedAt]:e.quickVideoAdvantage?c.Dc.now():null};return await (0,c.BN)((0,c.H9)(u.db,m.COLLECTIONS.users,t.uid),i),{success:!0}}catch(s){console.error("Error creating user:",s);let e="Unknown error occurred";return"auth/email-already-in-use"===s.code?e="Email address is already in use (duplicate)":"auth/invalid-email"===s.code?e="Invalid email address":"auth/weak-password"===s.code?e="Password is too weak":s.message&&(e=s.message),{success:!1,error:e}}}async function g(e){let s={success:0,failed:0,errors:[],duplicates:0};try{let t=(await e.text()).split("\n").filter(e=>e.trim());if(t.length<2)throw Error("CSV file must have at least a header row and one data row");let a=t[0],r=a.includes("	")?"	":",",i=a.split(r).map(e=>e.trim().replace(/"/g,"")),l=[];for(let e=1;e<t.length;e++){let s=t[e].split(r).map(e=>e.trim().replace(/"/g,"")),a={};i.forEach((e,t)=>{a[e]=s[t]||""}),a.activeDays&&(a.activeDays=parseInt(a.activeDays)||0),a.wallet&&(a.wallet=parseFloat(a.wallet)||0),a.totalVideos&&(a.totalVideos=parseInt(a.totalVideos)||0),a.quickVideoAdvantage&&(a.quickVideoAdvantage="true"===a.quickVideoAdvantage.toLowerCase()),a.quickVideoAdvantageDays&&(a.quickVideoAdvantageDays=parseInt(a.quickVideoAdvantageDays)||0),a.quickVideoAdvantageSeconds&&(a.quickVideoAdvantageSeconds=parseInt(a.quickVideoAdvantageSeconds)||30),l.push(a)}for(let e=0;e<l.length;e++){let t=l[e],a=`Processing user ${e+1} of ${l.length}: ${t.name||t.email}`;console.log(a);try{let a=await x(t);a.success?(s.success++,console.log(`✅ Created user: ${t.email}`)):(s.failed++,a.error?.includes("already exists")||a.error?.includes("duplicate")?(s.duplicates++,console.log(`⚠️ Skipped duplicate: ${t.email}`)):console.log(`❌ Failed to create: ${t.email} - ${a.error}`),s.errors.push(`Row ${e+2}: ${a.error}`))}catch(a){s.failed++,console.log(`❌ Error creating: ${t.email} - ${a.message}`),s.errors.push(`Row ${e+2}: ${a.message||"Unknown error"}`)}e%5==0?await new Promise(e=>setTimeout(e,500)):await new Promise(e=>setTimeout(e,200))}return s}catch(e){throw console.error("Error uploading users from CSV:",e),Error(`Failed to process CSV file: ${e.message}`)}}async function f(e){let s={success:0,failed:0,errors:[],duplicates:0};try{let t=await e.text(),a=JSON.parse(t);if(!Array.isArray(a))throw Error("JSON file must contain an array of user objects");for(let e=0;e<a.length;e++){let t=a[e];try{let a=await x(t);a.success?s.success++:(s.failed++,(a.error?.includes("already exists")||a.error?.includes("duplicate"))&&s.duplicates++,s.errors.push(`User ${e+1} (${t.email}): ${a.error}`))}catch(a){s.failed++,s.errors.push(`User ${e+1} (${t.email}): ${a.message||"Unknown error"}`)}e%5==0?await new Promise(e=>setTimeout(e,500)):await new Promise(e=>setTimeout(e,200))}return s}catch(e){throw console.error("Error uploading users from JSON:",e),Error(`Failed to process JSON file: ${e.message}`)}}var v=t(77567);function w(){let{user:e,loading:s,isAdmin:t}=(0,n.wC)();(0,o.useRouter)();let[i,d]=(0,r.useState)(!1),[c,u]=(0,r.useState)(null),[m,h]=(0,r.useState)(null),[x,w]=(0,r.useState)("csv"),[b,j]=(0,r.useState)([]),[y,N]=(0,r.useState)(!1),[S,A]=(0,r.useState)(""),k=async()=>{if(m)try{d(!0);let e=await m.text(),s=[];if("csv"===x){let t=e.split("\n").filter(e=>e.trim());if(t.length<2)throw Error("CSV file must have at least a header row and one data row");let a=t[0],r=a.includes("	")?"	":",",i=a.split(r).map(e=>e.trim().replace(/"/g,""));s=t.slice(1).map(e=>{let s=e.split(r).map(e=>e.trim().replace(/"/g,"")),t={};return i.forEach((e,a)=>{t[e]=s[a]||""}),t})}else if(s=JSON.parse(e),!Array.isArray(s))throw Error("JSON file must contain an array of user objects");let t=s.slice(0,5),a=[];t.forEach((e,s)=>{let t=p(e);t.length>0&&a.push(`Row ${s+1}: ${t.join(", ")}`)}),j(t),N(!0),a.length>0&&v.A.fire({icon:"warning",title:"Validation Issues Found",html:`<div class="text-left"><p>Issues found in preview data:</p><ul>${a.map(e=>`<li>${e}</li>`).join("")}</ul></div>`,confirmButtonText:"Continue Anyway",showCancelButton:!0,cancelButtonText:"Fix Data First"})}catch(e){console.error("Error previewing file:",e),v.A.fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{d(!1)}},q=async()=>{if(m&&(await v.A.fire({icon:"question",title:"Confirm User Upload",html:`
        <div class="text-left">
          <p><strong>Are you sure you want to upload users from this file?</strong></p>
          <br>
          <p>This will:</p>
          <ul>
            <li>Create Firebase Authentication accounts</li>
            <li>Create user documents in Firestore</li>
            <li>Use provided referral codes or assign new sequential ones</li>
            <li>Set up wallet and transaction data</li>
            <li>Apply quick video advantage if specified</li>
            <li>Validate referral code uniqueness</li>
          </ul>
          <br>
          <p class="text-red-600"><strong>Warning:</strong> This action cannot be undone!</p>
        </div>
      `,showCancelButton:!0,confirmButtonText:"Yes, Upload Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{let e;d(!0),u(null),A("Starting upload..."),v.A.fire({title:"Uploading Users",html:`
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p id="upload-progress">Starting upload...</p>
            <p class="text-sm text-gray-600 mt-2">Please do not close this page or navigate away.</p>
          </div>
        `,allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{window.updateUploadProgress=e=>{let s=document.getElementById("upload-progress");s&&(s.textContent=e)}}}),e="csv"===x?await g(m):await f(m),v.A.close(),u(e),e.success>0?v.A.fire({icon:e.failed>0?"warning":"success",title:"Upload Complete",html:`
            <div class="text-left">
              <p><strong>Upload Summary:</strong></p>
              <ul>
                <li class="text-green-600">✓ Successfully created: ${e.success} users</li>
                ${e.duplicates>0?`<li class="text-yellow-600">⚠ Skipped duplicates: ${e.duplicates} users</li>`:""}
                ${e.failed>0?`<li class="text-red-600">✗ Failed: ${e.failed} users</li>`:""}
              </ul>
              ${e.errors.length>0?`<br><p><strong>Errors:</strong></p><ul>${e.errors.slice(0,5).map(e=>`<li class="text-red-600">${e}</li>`).join("")}</ul>`:""}
            </div>
          `,timer:e.failed>0?void 0:5e3,showConfirmButton:e.failed>0}):v.A.fire({icon:"error",title:"Upload Failed",text:"No users were successfully created. Please check your data and try again."})}catch(e){console.error("Error uploading users:",e),v.A.fire({icon:"error",title:"Upload Failed",text:e.message||"Failed to upload users. Please try again."})}finally{d(!1)}};return s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Upload Users"}),(0,a.jsx)("p",{className:"text-white/80",children:"Transfer existing users from old platform"})]}),i?(0,a.jsxs)("button",{disabled:!0,className:"btn-secondary opacity-50 cursor-not-allowed",title:"Upload in progress - navigation disabled",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]}):(0,a.jsxs)(l(),{href:"/admin/users",className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload User Data"]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Upload Format"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"csv",checked:"csv"===x,onChange:e=>w(e.target.value),className:"mr-2"}),(0,a.jsx)("span",{className:"text-white",children:"CSV/TSV File"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"json",checked:"json"===x,onChange:e=>w(e.target.value),className:"mr-2"}),(0,a.jsx)("span",{className:"text-white",children:"JSON File"})]})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample Files"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("button",{onClick:()=>{let e=new Blob(["name,email,mobile,password,plan,activeDays,wallet,totalVideos,referredBy,referralCode,quickVideoAdvantage,quickVideoAdvantageDays,quickVideoAdvantageSeconds,quickVideoAdvantageGrantedBy\nJohn Doe,<EMAIL>,9876543210,password123,Basic,30,2000,100,MYN0001,MYN1001,true,7,10,<EMAIL>\nJane Smith,<EMAIL>,9876543211,password456,Premium,25,5000,150,MYN0002,MYN1002,false,,,,,\nMike Johnson,<EMAIL>,9876543212,password789,Starter,30,1000,50,,MYN1003,true,14,30,<EMAIL>\nSarah Wilson,<EMAIL>,9876543213,password321,Gold,20,8000,200,MYN0001,MYN1004,true,3,1,<EMAIL>"],{type:"text/csv"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="sample-users.csv",t.click(),URL.revokeObjectURL(s)},className:"btn-secondary text-sm",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify([{name:"John Doe",email:"<EMAIL>",mobile:"9876543210",password:"password123",plan:"Basic",activeDays:30,wallet:2e3,totalTranslations:100,referredBy:"IGN0001",referralCode:"IGN1001",quickTranslationAdvantage:!0,quickTranslationAdvantageDays:7,quickTranslationAdvantageSeconds:10,quickTranslationAdvantageGrantedBy:"<EMAIL>"},{name:"Jane Smith",email:"<EMAIL>",mobile:"9876543211",password:"password456",plan:"Premium",activeDays:25,wallet:5e3,totalTranslations:150,referredBy:"IGN0002",referralCode:"IGN1002",quickTranslationAdvantage:!1},{name:"Mike Johnson",email:"<EMAIL>",mobile:"9876543212",password:"password789",plan:"Starter",activeDays:30,wallet:1e3,totalVideos:50,referralCode:"MYN1003",quickVideoAdvantage:!0,quickVideoAdvantageDays:14,quickVideoAdvantageSeconds:30,quickVideoAdvantageGrantedBy:"<EMAIL>"},{name:"Sarah Wilson",email:"<EMAIL>",mobile:"9876543213",password:"password321",plan:"Gold",activeDays:20,wallet:8e3,totalVideos:200,referredBy:"MYN0001",referralCode:"MYN1004",quickVideoAdvantage:!0,quickVideoAdvantageDays:3,quickVideoAdvantageSeconds:1,quickVideoAdvantageGrantedBy:"<EMAIL>"}],null,2)],{type:"application/json"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="sample-users.json",t.click(),URL.revokeObjectURL(s)},className:"btn-secondary text-sm",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample JSON"]})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select File"}),(0,a.jsx)("input",{type:"file",accept:"csv"===x?".csv,.tsv,.txt":".json",onChange:e=>{let s=e.target.files?.[0];s&&(h(s),j([]),N(!1),u(null))},className:"form-input"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("button",{onClick:k,disabled:!m||i,className:"btn-secondary",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,a.jsx)("button",{onClick:q,disabled:!m||i||!y,className:"btn-primary",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]})})]}),i&&(0,a.jsx)("div",{className:"bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center text-yellow-300",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Upload in progress!"})," Please do not close this page or navigate away until the upload is complete."]})]})})]})]}),y&&b.length>0&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 5 Records)"]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-white",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-white/20",children:[(0,a.jsx)("th",{className:"text-left p-2",children:"Name"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Mobile"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Plan"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Active Days"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Wallet"}),(0,a.jsx)("th",{className:"text-left p-2",children:"Total Videos"})]})}),(0,a.jsx)("tbody",{children:b.map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-white/10",children:[(0,a.jsx)("td",{className:"p-2",children:e.name||"N/A"}),(0,a.jsx)("td",{className:"p-2",children:e.email||"N/A"}),(0,a.jsx)("td",{className:"p-2",children:e.mobile||"N/A"}),(0,a.jsx)("td",{className:"p-2",children:e.plan||"Trial"}),(0,a.jsx)("td",{className:"p-2",children:e.activeDays||0}),(0,a.jsxs)("td",{className:"p-2",children:["₹",e.wallet||0]}),(0,a.jsx)("td",{className:"p-2",children:e.totalVideos||0})]},s))})]})})]}),c&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Upload Results"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:c.success}),(0,a.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Created"})]}),c.duplicates>0&&(0,a.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:c.duplicates}),(0,a.jsx)("div",{className:"text-yellow-300 text-sm",children:"Skipped (Duplicates)"})]}),c.failed>0&&(0,a.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:c.failed}),(0,a.jsx)("div",{className:"text-red-300 text-sm",children:"Failed"})]})]}),c.errors.length>0&&(0,a.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,a.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[c.errors.slice(0,10).map((e,s)=>(0,a.jsxs)("li",{children:["• ",e]},s)),c.errors.length>10&&(0,a.jsxs)("li",{className:"text-red-400",children:["... and ",c.errors.length-10," more errors"]})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Upload Instructions"]}),(0,a.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-white mb-2",children:"Required Fields:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"name:"})," User's full name"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"email:"})," Valid email address (must be unique)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"mobile:"})," 10-digit mobile number (must be unique)"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"password:"})," Password for the user account (min 6 characters)"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-white mb-2",children:"Optional Fields:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"plan:"})," User's plan (Trial, Starter, Basic, Premium, Gold, Platinum, Diamond) - defaults to Trial"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"activeDays:"})," Number of active days remaining - defaults to 1"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"wallet:"})," Wallet balance in rupees - defaults to 0"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"totalVideos:"})," Total videos watched - defaults to 0"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"referredBy:"})," Referral code of the person who referred this user"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"referralCode:"})," User's own referral code (MYN0001, MYN0002, etc.) - auto-generated if not provided"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"quickVideoAdvantage:"})," Whether user has quick video advantage (true/false) - defaults to false"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"quickVideoAdvantageDays:"})," Number of days for quick advantage (1-365) - only if quickVideoAdvantage is true"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"quickVideoAdvantageSeconds:"})," Video duration in seconds during advantage (1-420) - defaults to 30"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"quickVideoAdvantageGrantedBy:"})," Admin who granted the advantage - optional"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"Each user will get a sequential referral code (MYN0001, MYN0002, etc.)"}),(0,a.jsx)("li",{children:"Firebase Authentication accounts will be created automatically"}),(0,a.jsx)("li",{children:"Duplicate emails or mobile numbers will be skipped"}),(0,a.jsx)("li",{children:"Users can login immediately with their email and password"}),(0,a.jsx)("li",{children:"All wallet balances and video counts will be preserved"})]})]})]})]})]})})}},51278:(e,s,t)=>{"use strict";t.d(s,{M4:()=>o,_f:()=>l});var a=t(33784),r=t(77567);function i(e){try{Object.keys(localStorage).forEach(s=>{(s.includes(e)||s.startsWith("video_session_")||s.startsWith("watch_times_")||s.startsWith("video_refresh_")||s.startsWith("video_change_notification_")||s.startsWith("leave_")||s.includes("mytube_")||s.includes("user_"))&&localStorage.removeItem(s)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e,s="/login"){try{if((await r.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await a.j2.signOut(),r.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=s}),!0;return!1}catch(e){return console.error("Logout error:",e),r.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,s="/login"){try{e&&i(e),await a.j2.signOut(),window.location.href=s}catch(e){console.error("Quick logout error:",e),window.location.href=s}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},62642:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let d={children:["",{children:["admin",{children:["upload-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,33091)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\upload-users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\upload-users\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/upload-users/page",pathname:"/admin/upload-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,s,t)=>{"use strict";t.d(s,{Nu:()=>l,hD:()=>i,wC:()=>o});var a=t(43210);t(63385),t(33784);var r=t(51278);function i(){let[e,s]=(0,a.useState)(null),[t,i]=(0,a.useState)(!0),l=async()=>{try{await (0,r.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:l}}function l(){let{user:e,loading:s}=i();return{user:e,loading:s}}function o(){let{user:e,loading:s}=i(),[t,r]=(0,a.useState)(!1),[l,o]=(0,a.useState)(!0);return{user:e,loading:s||l,isAdmin:t}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99711:(e,s,t)=>{Promise.resolve().then(t.bind(t,33091))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[6204,2756,7567,5901,3582],()=>t(62642));module.exports=a})();