'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, collection, query, where, getDocs, getCountFromServer } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'

export default function DebugRegistrationPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const testStep1 = async () => {
    addToResult('=== STEP 1: Testing Collection Access ===')
    try {
      const usersRef = collection(db, COLLECTIONS.users)
      addToResult('✅ Collection reference created')
      
      // Test query
      const emailQuery = query(usersRef, where(FIELD_NAMES.email, '==', '<EMAIL>'))
      addToResult('✅ Query created')
      
      const emailSnapshot = await getDocs(emailQuery)
      addToResult(`✅ Query executed, found ${emailSnapshot.size} documents`)
    } catch (error: any) {
      addToResult(`❌ Collection access failed: ${error.message}`)
      addToResult(`❌ Error code: ${error.code}`)
    }
  }

  const testStep2 = async () => {
    addToResult('\n=== STEP 2: Testing Count Operation ===')
    try {
      const usersCollection = collection(db, COLLECTIONS.users)
      const snapshot = await getCountFromServer(usersCollection)
      const count = snapshot.data().count
      addToResult(`✅ Count operation successful: ${count} users`)
    } catch (error: any) {
      addToResult(`❌ Count operation failed: ${error.message}`)
      addToResult(`❌ Error code: ${error.code}`)
    }
  }

  const testStep3 = async () => {
    addToResult('\n=== STEP 3: Testing Auth User Creation ===')
    try {
      const testEmail = `test${Date.now()}@example.com`
      const testPassword = 'test123456'

      addToResult(`Creating auth user with email: ${testEmail}`)
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      const user = userCredential.user
      addToResult(`✅ Auth user created: ${user.uid}`)
      addToResult(`   Email: ${user.email}`)
      addToResult(`   Email verified: ${user.emailVerified}`)
      addToResult(`   Auth token available: ${!!auth.currentUser}`)

      // Wait a moment for auth to propagate
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Test document creation with exact same data as registration
      addToResult('\n=== STEP 4: Testing Document Creation ===')

      // Generate referral code exactly like registration
      const timestamp = Date.now().toString().slice(-4)
      const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
      const userReferralCode = `MY${timestamp}${randomPart}`
      addToResult(`Generated referral code: ${userReferralCode}`)

      const userData = {
        [FIELD_NAMES.name]: 'Test User',
        [FIELD_NAMES.email]: testEmail,
        [FIELD_NAMES.mobile]: '9876543210',
        [FIELD_NAMES.referralCode]: userReferralCode,
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.referralBonusCredited]: false,
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 1,
        [FIELD_NAMES.joinedDate]: new Date(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalTranslations]: 0,
        [FIELD_NAMES.todayTranslations]: 0,
        [FIELD_NAMES.lastTranslationDate]: null,
        status: 'active'
      }

      addToResult(`Document path: ${COLLECTIONS.users}/${user.uid}`)
      addToResult(`Current auth user: ${auth.currentUser?.uid}`)
      addToResult(`Auth state: ${auth.currentUser ? 'authenticated' : 'not authenticated'}`)

      const userDocRef = doc(db, COLLECTIONS.users, user.uid)

      try {
        addToResult('Attempting setDoc...')
        await setDoc(userDocRef, userData)
        addToResult('✅ User document created successfully')

        // Verify document
        const verifyDoc = await getDoc(userDocRef)
        if (verifyDoc.exists()) {
          addToResult('✅ Document verification successful')
        } else {
          addToResult('❌ Document verification failed')
        }
      } catch (setDocError: any) {
        addToResult(`❌ setDoc failed: ${setDocError.message}`)
        addToResult(`❌ setDoc error code: ${setDocError.code}`)
        addToResult(`❌ Full setDoc error: ${JSON.stringify(setDocError, null, 2)}`)
      }

      // Clean up - delete the test user
      try {
        await user.delete()
        addToResult('✅ Test user deleted')
      } catch (deleteError: any) {
        addToResult(`⚠️ User deletion failed: ${deleteError.message}`)
      }

    } catch (error: any) {
      addToResult(`❌ Auth/Document creation failed: ${error.message}`)
      addToResult(`❌ Error code: ${error.code}`)
      addToResult(`❌ Full error: ${JSON.stringify(error, null, 2)}`)
    }
  }

  const runAllTests = async () => {
    setIsLoading(true)
    setResult('')
    
    try {
      await testStep1()
      await testStep2()
      await testStep3()
      addToResult('\n=== ALL TESTS COMPLETED ===')
    } catch (error: any) {
      addToResult(`\n❌ Test suite failed: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Debug Registration</h1>
        
        <div className="glass-card p-6 mb-6">
          <button
            onClick={runAllTests}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Running Tests...' : 'Run Registration Debug Tests'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap">
              {result || 'Click "Run Registration Debug Tests" to start...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}
