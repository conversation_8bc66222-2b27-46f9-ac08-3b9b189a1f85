'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { useLeaveMonitor } from '@/hooks/useLeaveMonitor'
import { getVideoCountData as getTranslationCountData, updateVideoCount as updateTranslationCount, addTransaction, updateWalletBalance, getUserVideoSettings as getUserTranslationSettings, getUserData, isUserPlanExpired, updateUserActiveDays } from '@/lib/dataService'
import { isWorkBlocked } from '@/lib/leaveService'
import {
  initializeTranslationSystem,
  getRandomTargetLanguage,
  AVAILABLE_LANGUAGES
} from '@/lib/translationManager'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import Swal from 'sweetalert2'

interface TranslationItem {
  english: string
  hindi?: string
  spanish?: string
  french?: string
  german?: string
  italian?: string
  portuguese?: string
  russian?: string
  japanese?: string
  korean?: string
  chinese?: string
  arabic?: string
  dutch?: string
  swedish?: string
  norwegian?: string
}

interface TranslationStep {
  id: string
  englishText: string
  targetLanguage: string
  targetLanguageName: string
  targetTranslation: string
  userTypedText: string
  selectedLanguage: string
  isTypingComplete: boolean
  isLanguageSelected: boolean
  isConverted: boolean
  isSubmitted: boolean
}



export default function WorkPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const { isBlocked: isLeaveBlocked, leaveStatus } = useLeaveMonitor({
    userId: user?.uid || null,
    checkInterval: 30000,
    enabled: !!user
  })
  
  // New Translation Workflow State
  const [currentStep, setCurrentStep] = useState<TranslationStep | null>(null)
  const [todayTranslations, setTodayTranslations] = useState(0)
  const [totalTranslationsCompleted, setTotalTranslationsCompleted] = useState(0)
  const [localTranslationCount, setLocalTranslationCount] = useState(0)
  const [canSubmitBatch, setCanSubmitBatch] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [translationItems, setTranslationItems] = useState<TranslationItem[]>([])
  const [isLoadingTranslations, setIsLoadingTranslations] = useState(true)

  // Typing validation state
  const [userTypedText, setUserTypedText] = useState('')
  const [typingErrors, setTypingErrors] = useState<number[]>([])
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  const [hasQuickAdvantage, setHasQuickAdvantage] = useState(false)

  // Language selection state
  const [selectedLanguage, setSelectedLanguage] = useState('')
  const [isLanguageCorrect, setIsLanguageCorrect] = useState(false)
  const [showTranslation, setShowTranslation] = useState(false)

  // Paste detection state
  const [lastInputTime, setLastInputTime] = useState(0)
  const [pasteDetected, setPasteDetected] = useState(false)

  const [translationSettings, setTranslationSettings] = useState({
    earningPerBatch: 10,
    plan: 'Trial'
  })
  const [userData, setUserData] = useState<any>(null)
  const [daysLeft, setDaysLeft] = useState(0)
  const [activeDays, setActiveDays] = useState(0)

  useEffect(() => {
    if (user) {
      checkWorkAccess()
    }
  }, [user])

  useEffect(() => {
    setCanSubmitBatch(localTranslationCount >= 50)
  }, [localTranslationCount])

  const checkWorkAccess = async () => {
    try {
      console.log('🔍 Checking work access for user:', user!.uid)

      // Check plan expiry first
      const planStatus = await isUserPlanExpired(user!.uid)
      console.log('📅 Plan status result:', planStatus)

      if (planStatus.expired) {
        console.log('🚫 Work access blocked - Plan expired:', planStatus.reason)
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-3">${planStatus.reason}</p>
              <p class="text-sm text-gray-600">
                Active Days: ${planStatus.activeDays || 0} | Days Left: ${planStatus.daysLeft || 0}
              </p>
            </div>
          `,
          confirmButtonText: 'Upgrade Plan',
          showCancelButton: true,
          cancelButtonText: 'Go to Dashboard'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          } else {
            window.location.href = '/dashboard'
          }
        })
        return
      }

      // Check if user has already completed their daily session (50 translations)
      const translationData = await getTranslationCountData(user!.uid)
      console.log('📊 Translation data check:', translationData)

      if (translationData.todayTranslations >= 50) {
        console.log('🚫 Work access blocked - Daily session completed')
        Swal.fire({
          icon: 'info',
          title: 'Daily Session Completed',
          html: `
            <div class="text-center">
              <p class="mb-3">You have already completed your daily session of 50 translations!</p>
              <p class="text-sm text-gray-600">
                Translations completed today: ${translationData.todayTranslations}/50
              </p>
              <p class="text-sm text-green-600 mt-2">
                Come back tomorrow for your next session.
              </p>
            </div>
          `,
          confirmButtonText: 'Go to Dashboard',
          allowOutsideClick: false,
          allowEscapeKey: false
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      const workStatus = await isWorkBlocked(user!.uid)
      console.log('📊 Work status result:', workStatus)

      if (workStatus.blocked) {
        console.log('🚫 Work access blocked:', workStatus.reason)
        Swal.fire({
          icon: 'warning',
          title: 'Work Not Available',
          text: workStatus.reason || 'Work is currently blocked.',
          confirmButtonText: 'Go to Dashboard'
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      console.log('✅ Work access allowed, proceeding with normal loading')
      loadTranslationData()
      loadTranslationSettings()
      loadUserData()
      initializeTranslations()
      initializeSession()
    } catch (error) {
      console.error('❌ Error checking work access (allowing work to proceed):', error)
      loadTranslationData()
      loadTranslationSettings()
      loadUserData()
      initializeTranslations()
      initializeSession()
    }
  }

  const loadTranslationData = async () => {
    try {
      console.log('📊 Loading translation data for user:', user!.uid)
      const data = await getTranslationCountData(user!.uid)
      console.log('📊 Translation data loaded:', data)
      setTodayTranslations(data.todayTranslations)
      setTotalTranslationsCompleted(data.totalTranslations)
    } catch (error) {
      console.error('Error loading translation data:', error)
    }
  }

  const loadTranslationSettings = async () => {
    try {
      const settings = await getUserTranslationSettings(user!.uid)
      setTranslationSettings({
        earningPerBatch: settings.earningPerBatch,
        plan: settings.plan
      })

      // Check if user has quick translation advantage
      setHasQuickAdvantage(settings.hasQuickAdvantage || false)
    } catch (error) {
      console.error('Error loading translation settings:', error)
    }
  }

  const loadUserData = async () => {
    try {
      const data = await getUserData(user!.uid)
      setUserData(data)

      if (data) {
        try {
          await updateUserActiveDays(user!.uid)
        } catch (error) {
          console.error('Error updating active days:', error)
        }

        const planStatus = await isUserPlanExpired(user!.uid)
        setDaysLeft(planStatus.daysLeft || 0)
        setActiveDays(planStatus.activeDays || 0)

        console.log('📊 Plan status loaded:', {
          plan: data.plan,
          expired: planStatus.expired,
          daysLeft: planStatus.daysLeft,
          activeDays: planStatus.activeDays,
          reason: planStatus.reason
        })
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }

  const initializeSession = () => {
    const today = new Date().toDateString()
    const sessionKey = `translation_session_${user!.uid}_${today}`

    const savedCount = localStorage.getItem(sessionKey)

    if (savedCount) {
      const count = parseInt(savedCount)
      setLocalTranslationCount(count)
    }
  }

  const initializeTranslations = async () => {
    try {
      setIsLoadingTranslations(true)

      // Initialize translation system with batching
      const currentBatchTranslations = await initializeTranslationSystem()

      // Convert TranslationData to TranslationItem format
      const translationItems: TranslationItem[] = currentBatchTranslations.map(item => ({
        english: item.english,
        hindi: item.hindi,
        spanish: item.spanish,
        french: item.french,
        german: item.german,
        italian: item.italian,
        portuguese: item.portuguese,
        russian: item.russian,
        japanese: item.japanese,
        korean: item.korean,
        chinese: item.chinese,
        arabic: item.arabic,
        dutch: item.dutch,
        swedish: item.swedish,
        norwegian: item.norwegian
      }))

      setTranslationItems(translationItems)

      // Initialize first translation step
      generateNewTranslationStep(translationItems)

    } catch (error) {
      console.error('Error loading translations:', error)
      Swal.fire({
        icon: 'error',
        title: 'Loading Error',
        text: 'Failed to load translation data. Please refresh the page.',
      })
    } finally {
      setIsLoadingTranslations(false)
    }
  }

  // Generate a new translation step
  const generateNewTranslationStep = (items: TranslationItem[] = translationItems) => {
    if (items.length === 0) return

    const randomIndex = Math.floor(Math.random() * items.length)
    const selectedItem = items[randomIndex]
    const randomTargetLang = getRandomTargetLanguage()
    const targetLangData = AVAILABLE_LANGUAGES.find(lang => lang.code === randomTargetLang)

    const newStep: TranslationStep = {
      id: `step_${Date.now()}_${Math.random()}`,
      englishText: selectedItem.english,
      targetLanguage: randomTargetLang,
      targetLanguageName: targetLangData?.name || 'Unknown',
      targetTranslation: selectedItem[randomTargetLang as keyof TranslationItem] || 'Translation not available',
      userTypedText: '',
      selectedLanguage: '',
      isTypingComplete: false,
      isLanguageSelected: false,
      isConverted: false,
      isSubmitted: false
    }

    setCurrentStep(newStep)
    setUserTypedText('')
    setTypingErrors([])
    setIsTypingComplete(false)
    setSelectedLanguage('')
    setIsLanguageCorrect(false)
    setShowTranslation(false)
    setPasteDetected(false)
  }

  // Typing validation with paste detection
  const handleTextInput = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!currentStep || isTypingComplete || pasteDetected) return

    const newValue = e.target.value
    const currentTime = Date.now()

    // Detect paste attempts
    if (detectPasteAttempt(newValue, currentTime)) {
      setPasteDetected(true)
      Swal.fire({
        icon: 'error',
        title: 'Paste Detected!',
        text: hasQuickAdvantage
          ? 'Copy-paste is allowed for your account.'
          : 'Copy-paste is not allowed. Please type the text manually.',
        timer: 3000
      })

      if (!hasQuickAdvantage) {
        e.target.value = userTypedText // Reset to previous valid state
        return
      }
    }

    // Validate character by character
    const errors = validateTyping(newValue, currentStep.englishText)
    setTypingErrors(errors)
    setUserTypedText(newValue)

    // Check if typing is complete and correct
    if (newValue === currentStep.englishText && errors.length === 0) {
      setIsTypingComplete(true)
      Swal.fire({
        icon: 'success',
        title: 'Perfect!',
        text: 'Text typed correctly. Now select the target language.',
        timer: 2000,
        showConfirmButton: false
      })
    }

    setLastInputTime(currentTime)
  }, [currentStep, isTypingComplete, pasteDetected, hasQuickAdvantage, userTypedText])

  // Enhanced paste detection with multiple methods
  const detectPasteAttempt = (newValue: string, currentTime: number): boolean => {
    const timeDiff = currentTime - lastInputTime
    const lengthDiff = newValue.length - userTypedText.length

    // Method 1: Multiple characters added at once (more than 3)
    if (lengthDiff > 3 && timeDiff < 100) {
      console.log('🚫 Paste detected: Multiple characters at once')
      return true
    }

    // Method 2: Very fast typing (unrealistic speed - more than 1 char per 50ms)
    if (lengthDiff > 1 && timeDiff < 50) {
      console.log('🚫 Paste detected: Unrealistic typing speed')
      return true
    }

    // Method 3: Large text block inserted instantly
    if (lengthDiff > 10) {
      console.log('🚫 Paste detected: Large text block')
      return true
    }

    // Method 4: Perfect text match (suggesting copy-paste from source)
    if (newValue.length > 20 && newValue === currentStep?.englishText.substring(0, newValue.length)) {
      const typingSpeed = newValue.length / (currentTime - (lastInputTime || currentTime))
      if (typingSpeed > 0.1) { // More than 6 characters per second
        console.log('🚫 Paste detected: Perfect match with high speed')
        return true
      }
    }

    return false
  }

  // Additional event handlers for paste detection
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!hasQuickAdvantage) {
      // Detect Ctrl+V, Cmd+V
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        e.preventDefault()
        setPasteDetected(true)
        Swal.fire({
          icon: 'error',
          title: 'Paste Not Allowed!',
          text: 'Keyboard paste shortcuts are disabled. Please type manually.',
          timer: 2000
        })
      }

      // Detect long press (holding a key)
      if (e.repeat) {
        console.log('🚫 Long press detected')
        // Allow backspace and delete for corrections
        if (e.key !== 'Backspace' && e.key !== 'Delete') {
          e.preventDefault()
        }
      }
    }
  }, [hasQuickAdvantage])

  // Detect drag and drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    if (!hasQuickAdvantage) {
      e.preventDefault()
      setPasteDetected(true)
      Swal.fire({
        icon: 'error',
        title: 'Drag & Drop Not Allowed!',
        text: 'Please type the text manually.',
        timer: 2000
      })
    }
  }, [hasQuickAdvantage])

  // Detect right-click context menu
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    if (!hasQuickAdvantage) {
      e.preventDefault()
      Swal.fire({
        icon: 'warning',
        title: 'Context Menu Disabled',
        text: 'Right-click menu is disabled to prevent paste operations.',
        timer: 1500
      })
    }
  }, [hasQuickAdvantage])

  // Validate typing character by character
  const validateTyping = (userText: string, targetText: string): number[] => {
    const errors: number[] = []

    for (let i = 0; i < userText.length; i++) {
      if (i >= targetText.length || userText[i] !== targetText[i]) {
        errors.push(i)
      }
    }

    return errors
  }

  // Handle language selection
  const handleLanguageSelect = (languageCode: string) => {
    if (!currentStep || !isTypingComplete) return

    setSelectedLanguage(languageCode)

    if (languageCode === currentStep.targetLanguage) {
      setIsLanguageCorrect(true)
      Swal.fire({
        icon: 'success',
        title: 'Correct Language!',
        text: 'You selected the correct language. Click Convert to see the translation.',
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      setIsLanguageCorrect(false)
      Swal.fire({
        icon: 'error',
        title: 'Wrong Language!',
        text: `Please select ${currentStep.targetLanguageName} language.`,
        timer: 2000,
        showConfirmButton: false
      })
    }
  }

  // Handle convert button click
  const handleConvert = () => {
    if (!currentStep || !isLanguageCorrect) return

    setShowTranslation(true)
    setCurrentStep(prev => prev ? { ...prev, isConverted: true } : null)
  }

  // Handle submit translation
  const handleSubmitTranslation = () => {
    if (!currentStep || !showTranslation) return

    // Mark current step as submitted
    setCurrentStep(prev => prev ? { ...prev, isSubmitted: true } : null)

    // Increment local count
    const newLocalCount = localTranslationCount + 1
    setLocalTranslationCount(newLocalCount)

    // Save to localStorage
    const today = new Date().toDateString()
    const sessionKey = `translation_session_${user!.uid}_${today}`
    localStorage.setItem(sessionKey, newLocalCount.toString())

    // Show progress
    if (newLocalCount < 50) {
      Swal.fire({
        icon: 'success',
        title: 'Translation Submitted!',
        text: `Progress: ${newLocalCount}/50 translations completed.`,
        timer: 2000,
        showConfirmButton: false
      }).then(() => {
        // Generate next translation step
        generateNewTranslationStep()
      })
    } else {
      Swal.fire({
        icon: 'success',
        title: '🎉 All Translations Completed!',
        text: 'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',
        timer: 3000,
        showConfirmButton: false
      })
    }
  }

  const submitTranslations = async () => {
    if (!canSubmitBatch || isSubmitting || localTranslationCount < 50) return

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Submission Not Available',
        text: leaveStatus.reason || 'Translation submission is not available due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Calculate earning for the batch of 50 translations
      const batchEarningAmount = translationSettings.earningPerBatch

      // Update translation count in database (add 50 translations)
      for (let i = 0; i < 50; i++) {
        await updateTranslationCount(user!.uid)
      }

      // Add batch earning to wallet
      await updateWalletBalance(user!.uid, batchEarningAmount)

      // Add transaction record for the batch
      await addTransaction(user!.uid, {
        type: 'translation_earning',
        amount: batchEarningAmount,
        description: `Batch completion reward - 50 translations completed`
      })

      // Update local state
      const newTodayTranslations = Math.min(todayTranslations + 50, 50)
      setTodayTranslations(newTodayTranslations)
      setTotalTranslationsCompleted(totalTranslationsCompleted + 50)

      // Clear local session data
      const today = new Date().toDateString()
      const sessionKey = `translation_session_${user!.uid}_${today}`
      localStorage.removeItem(sessionKey)

      setLocalTranslationCount(0)
      setCanSubmitBatch(false)

      Swal.fire({
        icon: 'success',
        title: '🎉 Daily Session Completed!',
        html: `
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${batchEarningAmount} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,
        confirmButtonText: 'Go to Dashboard',
        timer: 6000,
        showConfirmButton: true
      }).then(() => {
        window.location.href = '/dashboard'
      })

    } catch (error) {
      console.error('Error submitting translations:', error)
      Swal.fire({
        icon: 'error',
        title: 'Submission Failed',
        text: 'There was an error submitting your translations. Please try again.',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading || isLoadingTranslations || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading translations...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">Translate Text & Earn</h1>
          <div className="text-white text-right">
            <p className="text-sm">Plan: {translationSettings.plan}</p>
            <p className="text-sm">₹{translationSettings.earningPerBatch}/batch (50 translations)</p>
          </div>
        </div>

        {/* Translation Statistics Header */}
        <div className="grid grid-cols-5 gap-2 text-center">
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-yellow-400">{daysLeft}</p>
            <p className="text-white/80 text-xs">days left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-blue-400">{todayTranslations}</p>
            <p className="text-white/80 text-xs">Today's Translations</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-green-400">{totalTranslationsCompleted}</p>
            <p className="text-white/80 text-xs">Total Translations</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-purple-400">{Math.max(0, 50 - localTranslationCount)}</p>
            <p className="text-white/80 text-xs">Translations Left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-orange-400">{activeDays}/{translationSettings.plan === 'Trial' ? '2' : '30'}</p>
            <p className="text-white/80 text-xs">Active Days</p>
          </div>
        </div>
      </header>

      {/* Translation Section */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">
            <i className="fas fa-language mr-2"></i>
            Translate Text & Earn
          </h2>
          <button
            onClick={() => window.location.reload()}
            className="glass-button px-3 py-1 text-white text-sm"
            title="Refresh to get new translation"
          >
            <i className="fas fa-sync-alt mr-1"></i>
            New Translation
          </button>
        </div>

        {currentStep && (
          <div className="space-y-6">
            {/* Step 1: English Text Typing */}
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-2">
                <i className="fas fa-keyboard mr-2"></i>
                Step 1: Type the English text below
              </h3>
              <div className="bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3">
                <p className="text-white text-lg font-mono">
                  {currentStep.englishText}
                </p>
              </div>

              <textarea
                value={userTypedText}
                onChange={handleTextInput}
                onKeyDown={handleKeyDown}
                onDrop={handleDrop}
                onContextMenu={handleContextMenu}
                disabled={isTypingComplete || pasteDetected}
                placeholder={hasQuickAdvantage ? "Type or paste the English text here..." : "Type the English text here (copy-paste not allowed)..."}
                className={`w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono ${
                  typingErrors.length > 0 ? 'border-red-500' : ''
                } ${isTypingComplete ? 'border-green-500 bg-green-500/10' : ''}`}
                onPaste={(e) => {
                  if (!hasQuickAdvantage) {
                    e.preventDefault()
                    setPasteDetected(true)
                    Swal.fire({
                      icon: 'error',
                      title: 'Paste Not Allowed!',
                      text: 'Please type the text manually.',
                      timer: 2000
                    })
                  }
                }}
                onDragOver={(e) => {
                  if (!hasQuickAdvantage) {
                    e.preventDefault()
                  }
                }}
                spellCheck={false}
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
              />

              {typingErrors.length > 0 && (
                <div className="mt-2 text-red-400 text-sm">
                  <i className="fas fa-exclamation-triangle mr-1"></i>
                  Typing errors detected. Please correct them to continue.
                </div>
              )}

              {isTypingComplete && (
                <div className="mt-2 text-green-400 text-sm">
                  <i className="fas fa-check-circle mr-1"></i>
                  Perfect! Text typed correctly.
                </div>
              )}

              {pasteDetected && !hasQuickAdvantage && (
                <div className="mt-2 text-red-400 text-sm">
                  <i className="fas fa-ban mr-1"></i>
                  Paste detected! Please type manually.
                  <button
                    onClick={() => {
                      setPasteDetected(false)
                      setUserTypedText('')
                    }}
                    className="ml-2 text-blue-400 underline"
                  >
                    Reset
                  </button>
                </div>
              )}
            </div>

            {/* Step 2: Language Selection */}
            {isTypingComplete && (
              <div className="bg-white/10 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-2">
                  <i className="fas fa-globe mr-2"></i>
                  Step 2: Select the target language - {currentStep.targetLanguageName}
                </h3>
                <select
                  value={selectedLanguage}
                  onChange={(e) => handleLanguageSelect(e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200"
                >
                  <option value="" className="bg-gray-800 text-white">Select target language...</option>
                  {AVAILABLE_LANGUAGES.map((lang) => (
                    <option key={lang.code} value={lang.code} className="bg-gray-800 text-white">
                      {lang.flag} {lang.name}
                    </option>
                  ))}
                </select>

                {selectedLanguage && !isLanguageCorrect && (
                  <div className="mt-2 text-red-400 text-sm">
                    <i className="fas fa-times-circle mr-1"></i>
                    Wrong language! Please select {currentStep.targetLanguageName}.
                  </div>
                )}

                {isLanguageCorrect && (
                  <div className="mt-2 text-green-400 text-sm">
                    <i className="fas fa-check-circle mr-1"></i>
                    Correct language selected!
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Convert Button */}
            {isLanguageCorrect && (
              <div className="text-center">
                <button
                  onClick={handleConvert}
                  disabled={showTranslation}
                  className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    showTranslation
                      ? 'btn-disabled cursor-not-allowed opacity-50'
                      : 'btn-primary hover:scale-105'
                  }`}
                >
                  <i className="fas fa-exchange-alt mr-2"></i>
                  Convert to {currentStep.targetLanguageName}
                </button>
              </div>
            )}

            {/* Step 4: Show Translation */}
            {showTranslation && (
              <div className="bg-white/10 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-2">
                  <i className="fas fa-language mr-2"></i>
                  {currentStep.targetLanguageName} Translation:
                </h3>
                <div className="bg-white/5 p-3 rounded border-l-4 border-green-400">
                  <p className="text-white text-lg">
                    {currentStep.targetTranslation}
                  </p>
                </div>

                <div className="text-center mt-4">
                  <button
                    onClick={handleSubmitTranslation}
                    className="btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                  >
                    <i className="fas fa-check mr-2"></i>
                    Submit Translation
                  </button>
                </div>
              </div>
            )}

            {/* Batch Submit Button */}
            {canSubmitBatch && (
              <div className="text-center">
                <button
                  onClick={submitTranslations}
                  disabled={isSubmitting}
                  className="btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105"
                >
                  <i className="fas fa-money-bill-wave mr-2"></i>
                  Submit All 50 Translations & Earn ₹{translationSettings.earningPerBatch}
                </button>
              </div>
            )}

            {/* Progress */}
            <div className="text-center">
              <p className="text-white/80">
                Progress: {localTranslationCount}/50 translations completed
              </p>
              <div className="w-full bg-white/20 rounded-full h-2 mt-2">
                <div
                  className="bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(localTranslationCount / 50) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
