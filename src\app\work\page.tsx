'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { useLeaveMonitor } from '@/hooks/useLeaveMonitor'
import { getVideoCountData as getTranslationCountData, updateVideoCount as updateTranslationCount, addTransaction, updateWalletBalance, getUserVideoSettings as getUserTranslationSettings, getUserData, isUserPlanExpired, updateUserActiveDays } from '@/lib/dataService'
import { isWorkBlocked } from '@/lib/leaveService'
import {
  initializeTranslationSystem,
  getRandomTargetLanguage
} from '@/lib/translationManager'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import Swal from 'sweetalert2'

interface TranslationItem {
  english: string
  hindi?: string
  spanish?: string
  french?: string
  german?: string
  italian?: string
  portuguese?: string
  russian?: string
  japanese?: string
  korean?: string
  chinese?: string
  arabic?: string
  dutch?: string
  swedish?: string
  norwegian?: string
}

export default function WorkPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const { isBlocked: isLeaveBlocked, leaveStatus } = useLeaveMonitor({
    userId: user?.uid || null,
    checkInterval: 30000,
    enabled: !!user
  })
  
  // Translation-focused state
  const [currentTranslation, setCurrentTranslation] = useState<TranslationItem | null>(null)
  const [todayTranslations, setTodayTranslations] = useState(0)
  const [totalTranslationsCompleted, setTotalTranslationsCompleted] = useState(0)
  const [remainingTranslations, setRemainingTranslations] = useState(50)
  const [userTranslation, setUserTranslation] = useState('')
  const [currentTargetLanguage, setCurrentTargetLanguage] = useState('hindi')
  const [localTranslationCount, setLocalTranslationCount] = useState(0)
  const [canSubmit, setCanSubmit] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [translationItems, setTranslationItems] = useState<TranslationItem[]>([])
  const [isLoadingTranslations, setIsLoadingTranslations] = useState(true)
  
  const [availableLanguages] = useState([
    { code: 'hindi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'spanish', name: 'Spanish', flag: '🇪🇸' },
    { code: 'french', name: 'French', flag: '🇫🇷' },
    { code: 'german', name: 'German', flag: '🇩🇪' },
    { code: 'italian', name: 'Italian', flag: '🇮🇹' },
    { code: 'portuguese', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'russian', name: 'Russian', flag: '🇷🇺' },
    { code: 'japanese', name: 'Japanese', flag: '🇯🇵' },
    { code: 'korean', name: 'Korean', flag: '🇰🇷' },
    { code: 'chinese', name: 'Chinese', flag: '🇨🇳' },
    { code: 'arabic', name: 'Arabic', flag: '🇸🇦' },
    { code: 'dutch', name: 'Dutch', flag: '🇳🇱' }
  ])
  
  const [translationSettings, setTranslationSettings] = useState({
    earningPerBatch: 10,
    plan: 'Trial'
  })
  const [userData, setUserData] = useState<any>(null)
  const [daysLeft, setDaysLeft] = useState(0)
  const [activeDays, setActiveDays] = useState(0)

  useEffect(() => {
    if (user) {
      checkWorkAccess()
    }
  }, [user])

  useEffect(() => {
    setCanSubmit(localTranslationCount >= 50)
  }, [localTranslationCount])

  const checkWorkAccess = async () => {
    try {
      console.log('🔍 Checking work access for user:', user!.uid)

      // Check plan expiry first
      const planStatus = await isUserPlanExpired(user!.uid)
      console.log('📅 Plan status result:', planStatus)

      if (planStatus.expired) {
        console.log('🚫 Work access blocked - Plan expired:', planStatus.reason)
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-3">${planStatus.reason}</p>
              <p class="text-sm text-gray-600">
                Active Days: ${planStatus.activeDays || 0} | Days Left: ${planStatus.daysLeft || 0}
              </p>
            </div>
          `,
          confirmButtonText: 'Upgrade Plan',
          showCancelButton: true,
          cancelButtonText: 'Go to Dashboard'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          } else {
            window.location.href = '/dashboard'
          }
        })
        return
      }

      // Check if user has already completed their daily session (50 translations)
      const translationData = await getTranslationCountData(user!.uid)
      console.log('📊 Translation data check:', translationData)

      if (translationData.todayTranslations >= 50) {
        console.log('🚫 Work access blocked - Daily session completed')
        Swal.fire({
          icon: 'info',
          title: 'Daily Session Completed',
          html: `
            <div class="text-center">
              <p class="mb-3">You have already completed your daily session of 50 translations!</p>
              <p class="text-sm text-gray-600">
                Translations completed today: ${translationData.todayTranslations}/50
              </p>
              <p class="text-sm text-green-600 mt-2">
                Come back tomorrow for your next session.
              </p>
            </div>
          `,
          confirmButtonText: 'Go to Dashboard',
          allowOutsideClick: false,
          allowEscapeKey: false
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      const workStatus = await isWorkBlocked(user!.uid)
      console.log('📊 Work status result:', workStatus)

      if (workStatus.blocked) {
        console.log('🚫 Work access blocked:', workStatus.reason)
        Swal.fire({
          icon: 'warning',
          title: 'Work Not Available',
          text: workStatus.reason || 'Work is currently blocked.',
          confirmButtonText: 'Go to Dashboard'
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      console.log('✅ Work access allowed, proceeding with normal loading')
      loadTranslationData()
      loadTranslationSettings()
      loadUserData()
      initializeTranslations()
      initializeSession()
    } catch (error) {
      console.error('❌ Error checking work access (allowing work to proceed):', error)
      loadTranslationData()
      loadTranslationSettings()
      loadUserData()
      initializeTranslations()
      initializeSession()
    }
  }

  const loadTranslationData = async () => {
    try {
      console.log('📊 Loading translation data for user:', user!.uid)
      const data = await getTranslationCountData(user!.uid)
      console.log('📊 Translation data loaded:', data)
      setTodayTranslations(data.todayTranslations)
      setTotalTranslationsCompleted(data.totalTranslations)
    } catch (error) {
      console.error('Error loading translation data:', error)
    }
  }

  const loadTranslationSettings = async () => {
    try {
      const settings = await getUserTranslationSettings(user!.uid)
      setTranslationSettings({
        earningPerBatch: settings.earningPerBatch,
        plan: settings.plan
      })
    } catch (error) {
      console.error('Error loading translation settings:', error)
    }
  }

  const loadUserData = async () => {
    try {
      const data = await getUserData(user!.uid)
      setUserData(data)

      if (data) {
        try {
          await updateUserActiveDays(user!.uid)
        } catch (error) {
          console.error('Error updating active days:', error)
        }

        const planStatus = await isUserPlanExpired(user!.uid)
        setDaysLeft(planStatus.daysLeft || 0)
        setActiveDays(planStatus.activeDays || 0)

        console.log('📊 Plan status loaded:', {
          plan: data.plan,
          expired: planStatus.expired,
          daysLeft: planStatus.daysLeft,
          activeDays: planStatus.activeDays,
          reason: planStatus.reason
        })
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }

  const initializeSession = () => {
    const today = new Date().toDateString()
    const sessionKey = `translation_session_${user!.uid}_${today}`

    const savedCount = localStorage.getItem(sessionKey)

    if (savedCount) {
      const count = parseInt(savedCount)
      setLocalTranslationCount(count)
      setRemainingTranslations(Math.max(0, 50 - count))
    } else {
      setRemainingTranslations(50)
    }
  }

  const initializeTranslations = async () => {
    try {
      setIsLoadingTranslations(true)

      // Initialize translation system with batching
      const currentBatchTranslations = await initializeTranslationSystem()

      // Convert TranslationData to TranslationItem format
      const translationItems: TranslationItem[] = currentBatchTranslations.map(item => ({
        english: item.english,
        hindi: item.hindi,
        spanish: item.spanish,
        french: item.french,
        german: item.german,
        italian: item.italian,
        portuguese: item.portuguese,
        russian: item.russian,
        japanese: item.japanese,
        korean: item.korean,
        chinese: item.chinese,
        arabic: item.arabic,
        dutch: item.dutch,
        swedish: item.swedish,
        norwegian: item.norwegian
      }))

      setTranslationItems(translationItems)

      // Get a random translation item
      if (translationItems.length > 0) {
        const randomIndex = Math.floor(Math.random() * translationItems.length)
        setCurrentTranslation(translationItems[randomIndex])

        // Get a random target language
        const randomTargetLang = getRandomTargetLanguage()
        setCurrentTargetLanguage(randomTargetLang)
      }

    } catch (error) {
      console.error('Error loading translations:', error)
      Swal.fire({
        icon: 'error',
        title: 'Loading Error',
        text: 'Failed to load translation data. Please refresh the page.',
      })
    } finally {
      setIsLoadingTranslations(false)
    }
  }

  const nextTranslation = () => {
    if (!userTranslation.trim() || localTranslationCount >= 50) return

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Work Suspended',
        text: leaveStatus.reason || 'Work has been suspended due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    // Save local count
    const newLocalCount = localTranslationCount + 1

    setLocalTranslationCount(newLocalCount)
    setRemainingTranslations(Math.max(0, 50 - newLocalCount))

    // Save to localStorage
    const today = new Date().toDateString()
    const sessionKey = `translation_session_${user!.uid}_${today}`
    localStorage.setItem(sessionKey, newLocalCount.toString())

    // Get next translation
    if (translationItems.length > 0) {
      const randomIndex = Math.floor(Math.random() * translationItems.length)
      setCurrentTranslation(translationItems[randomIndex])

      // Get a random target language
      const randomLangIndex = Math.floor(Math.random() * availableLanguages.length)
      setCurrentTargetLanguage(availableLanguages[randomLangIndex].code)
    }

    // Clear user input
    setUserTranslation('')

    // Show progress message
    if (newLocalCount < 50) {
      Swal.fire({
        icon: 'success',
        title: 'Translation Completed!',
        text: `Progress: ${newLocalCount}/50 translations completed. ${50 - newLocalCount} more to go!`,
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      Swal.fire({
        icon: 'success',
        title: '🎉 All Translations Completed!',
        text: 'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',
        timer: 3000,
        showConfirmButton: false
      })
    }
  }

  const submitTranslations = async () => {
    if (!canSubmit || isSubmitting || localTranslationCount < 50) return

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Submission Not Available',
        text: leaveStatus.reason || 'Translation submission is not available due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Calculate earning for the batch of 50 translations
      const batchEarningAmount = translationSettings.earningPerBatch

      // Update translation count in database (add 50 translations)
      for (let i = 0; i < 50; i++) {
        await updateTranslationCount(user!.uid)
      }

      // Add batch earning to wallet
      await updateWalletBalance(user!.uid, batchEarningAmount)

      // Add transaction record for the batch
      await addTransaction(user!.uid, {
        type: 'translation_earning',
        amount: batchEarningAmount,
        description: `Batch completion reward - 50 translations completed`
      })

      // Update local state
      const newTodayTranslations = Math.min(todayTranslations + 50, 50)
      setTodayTranslations(newTodayTranslations)
      setTotalTranslationsCompleted(totalTranslationsCompleted + 50)
      setRemainingTranslations(0)

      // Clear local session data
      const today = new Date().toDateString()
      const sessionKey = `translation_session_${user!.uid}_${today}`
      localStorage.removeItem(sessionKey)

      setLocalTranslationCount(0)
      setCanSubmit(false)
      setUserTranslation('')

      Swal.fire({
        icon: 'success',
        title: '🎉 Daily Session Completed!',
        html: `
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${batchEarningAmount} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,
        confirmButtonText: 'Go to Dashboard',
        timer: 6000,
        showConfirmButton: true
      }).then(() => {
        window.location.href = '/dashboard'
      })

    } catch (error) {
      console.error('Error submitting translations:', error)
      Swal.fire({
        icon: 'error',
        title: 'Submission Failed',
        text: 'There was an error submitting your translations. Please try again.',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading || isLoadingTranslations || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading translations...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">Translate Text & Earn</h1>
          <div className="text-white text-right">
            <p className="text-sm">Plan: {translationSettings.plan}</p>
            <p className="text-sm">₹{translationSettings.earningPerBatch}/batch (50 translations)</p>
          </div>
        </div>

        {/* Translation Statistics Header */}
        <div className="grid grid-cols-5 gap-2 text-center">
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-yellow-400">{daysLeft}</p>
            <p className="text-white/80 text-xs">days left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-blue-400">{todayTranslations}</p>
            <p className="text-white/80 text-xs">Today's Translations</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-green-400">{totalTranslationsCompleted}</p>
            <p className="text-white/80 text-xs">Total Translations</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-purple-400">{remainingTranslations}</p>
            <p className="text-white/80 text-xs">Translations Left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-orange-400">{activeDays}/{translationSettings.plan === 'Trial' ? '2' : '30'}</p>
            <p className="text-white/80 text-xs">Active Days</p>
          </div>
        </div>
      </header>

      {/* Translation Section */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">
            <i className="fas fa-language mr-2"></i>
            Translate Text & Earn
          </h2>
          <button
            onClick={() => window.location.reload()}
            className="glass-button px-3 py-1 text-white text-sm"
            title="Refresh to get new translation"
          >
            <i className="fas fa-sync-alt mr-1"></i>
            New Translation
          </button>
        </div>

        {currentTranslation && (
          <div className="space-y-6">
            {/* Source Text */}
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-2">
                <i className="fas fa-flag mr-2"></i>
                English Text:
              </h3>
              <p className="text-white text-lg bg-white/5 p-3 rounded border-l-4 border-blue-400">
                {currentTranslation.english}
              </p>
            </div>

            {/* Target Language Selection */}
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-2">
                <i className="fas fa-globe mr-2"></i>
                Translate to:
              </h3>
              <select
                value={currentTargetLanguage}
                onChange={(e) => setCurrentTargetLanguage(e.target.value)}
                className="w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200"
              >
                {availableLanguages.map((lang) => (
                  <option key={lang.code} value={lang.code} className="bg-gray-800 text-white">
                    {lang.flag} {lang.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Translation Input */}
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-2">
                <i className="fas fa-edit mr-2"></i>
                Your Translation:
              </h3>
              <textarea
                value={userTranslation}
                onChange={(e) => setUserTranslation(e.target.value)}
                placeholder={`Enter your ${availableLanguages.find(l => l.code === currentTargetLanguage)?.name} translation here...`}
                className="w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 justify-center">
              <button
                onClick={nextTranslation}
                disabled={!userTranslation.trim() || localTranslationCount >= 50}
                className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                  !userTranslation.trim() || localTranslationCount >= 50
                    ? 'btn-disabled cursor-not-allowed opacity-50'
                    : 'btn-primary hover:scale-105'
                }`}
              >
                <i className="fas fa-check mr-2"></i>
                Submit Translation
              </button>

              {canSubmit && (
                <button
                  onClick={submitTranslations}
                  disabled={isSubmitting}
                  className="btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                >
                  <i className="fas fa-money-bill-wave mr-2"></i>
                  Submit & Earn ₹{translationSettings.earningPerBatch}
                </button>
              )}
            </div>

            {/* Progress */}
            <div className="text-center">
              <p className="text-white/80">
                Progress: {localTranslationCount}/50 translations completed
              </p>
              <div className="w-full bg-white/20 rounded-full h-2 mt-2">
                <div 
                  className="bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(localTranslationCount / 50) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
