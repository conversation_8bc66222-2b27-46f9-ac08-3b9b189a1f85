(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[118],{3663:(e,t,a)=>{"use strict";a.d(t,{Dt:()=>i,PO:()=>d,Qy:()=>c,bQ:()=>o,cb:()=>n,jQ:()=>h});let s={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},n=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}];function l(e){let t=function(e){try{let t=localStorage.getItem("".concat(s.BATCH_PREFIX).concat(e));if(!t)return null;let a=JSON.parse(t);if(Date.now()-a.lastUpdated>864e5)return localStorage.removeItem("".concat(s.BATCH_PREFIX).concat(e)),null;return a}catch(t){return console.error("Error loading translation batch ".concat(e,":"),t),null}}(e);return t?t.translations:[]}function r(){return l(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"))}function i(){let e=parseInt(localStorage.getItem(s.TOTAL_TRANSLATIONS)||"0"),t=parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"),a=Math.ceil(e/100),n=l(t);return{totalTranslations:e,currentBatch:t,totalBatches:a,translationsInCurrentBatch:n.length}}function c(){Object.keys(localStorage).forEach(e=>{(e.startsWith(s.BATCH_PREFIX)||Object.values(s).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function o(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error("Failed to load translations: ".concat(e.statusText));let t=await e.json();console.log("Raw translation data loaded:",t.length,"entries");let a=[];return Array.isArray(t)&&t.forEach((e,t)=>{e.english&&a.push({id:"translation_".concat(t,"_").concat(Date.now()),english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian,category:"General",batchIndex:Math.floor(a.length/100)})}),a}catch(e){throw console.error("Error loading translations from file:",e),e}}async function d(){try{if(!function(){let e=localStorage.getItem(s.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached translation data..."),r();{console.log("Loading fresh translation data...");let e=await o();return!function(e){let t=Math.ceil(e.length/100);for(let n=0;n<t;n++){let t=100*n,l=Math.min(t+100,e.length),r=e.slice(t,l);var a=n;try{let e={batchNumber:a,translations:r,totalTranslations:r.length,lastUpdated:Date.now()};localStorage.setItem("".concat(s.BATCH_PREFIX).concat(a),JSON.stringify(e))}catch(e){console.error("Error saving translation batch ".concat(a,":"),e)}}localStorage.setItem(s.TOTAL_TRANSLATIONS,e.length.toString()),localStorage.setItem(s.CURRENT_BATCH,"0"),localStorage.setItem(s.LAST_PROCESSED,Date.now().toString()),console.log("Saved ".concat(e.length," translations in ").concat(t," batches"))}(e),r()}}catch(t){console.error("Error initializing translation system:",t);let e=r();if(e.length>0)return console.log("Using cached translations as fallback"),e;throw t}}function h(){let e=Math.floor(Math.random()*n.length);return n[e].code}},4176:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(5155),n=a(2115),l=a(3663);function r(){let[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)({totalTranslations:0,currentBatch:0,totalBatches:0,translationsInCurrentBatch:0}),[i,c]=(0,n.useState)(!1),[o,d]=(0,n.useState)(null),h=()=>{r((0,l.Dt)())},x=async()=>{c(!0),d(null);try{let e=await (0,l.bQ)();t(e.slice(0,10)),h()}catch(e){d(e.message)}finally{c(!1)}},m=async()=>{c(!0),d(null);try{let e=await (0,l.PO)();t(e.slice(0,10)),h()}catch(e){d(e.message)}finally{c(!1)}};return(0,n.useEffect)(()=>{h()},[]),(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-white mb-4",children:"Translation System Test"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:a.totalTranslations}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Total Translations"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-400",children:a.currentBatch}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Current Batch"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:a.totalBatches}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Total Batches"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-400",children:a.translationsInCurrentBatch}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"In Current Batch"})]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)("button",{onClick:x,disabled:i,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:i?"Loading...":"Load from File"}),(0,s.jsx)("button",{onClick:m,disabled:i,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:i?"Loading...":"Initialize System"}),(0,s.jsx)("button",{onClick:()=>{(0,l.Qy)(),t([]),h()},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Clear Storage"})]}),o&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:(0,s.jsxs)("p",{className:"text-red-400",children:["Error: ",o]})})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h2",{className:"text-lg font-bold text-white mb-4",children:["Loaded Translations (",e.length,")"]}),0===e.length?(0,s.jsx)("p",{className:"text-white/60 text-center py-8",children:"No translations loaded. Click a button above to test."}):(0,s.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsx)("h3",{className:"text-white font-medium text-sm mb-2",children:"English Text:"}),(0,s.jsx)("p",{className:"text-white/90 bg-white/5 p-3 rounded border-l-4 border-blue-400",children:e.english})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-3",children:[e.hindi&&(0,s.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,s.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Hindi:"}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:e.hindi})]}),e.spanish&&(0,s.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,s.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Spanish:"}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:e.spanish})]}),e.french&&(0,s.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,s.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"French:"}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:e.french})]}),e.german&&(0,s.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,s.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"German:"}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:e.german})]}),e.italian&&(0,s.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,s.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Italian:"}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:e.italian})]}),e.portuguese&&(0,s.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,s.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Portuguese:"}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:e.portuguese})]})]}),(0,s.jsxs)("div",{className:"text-xs text-white/60 mt-3 space-y-1",children:[(0,s.jsxs)("p",{children:["ID: ",e.id]}),(0,s.jsxs)("p",{children:["Category: ",e.category]}),(0,s.jsxs)("p",{children:["Batch: ",e.batchIndex]})]})]},e.id))})]})]})}},8451:(e,t,a)=>{Promise.resolve().then(a.bind(a,4176))}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(8451)),_N_E=e.O()}]);