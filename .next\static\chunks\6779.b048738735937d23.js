"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779],{6779:(t,e,a)=>{a.d(e,{CF:()=>c,Pn:()=>s,TK:()=>w,getWithdrawals:()=>u,hG:()=>h,lo:()=>i,nQ:()=>E,updateWithdrawalStatus:()=>D,x5:()=>l});var r=a(5317),o=a(6104),n=a(3592);let d=new Map;async function s(){let t="dashboard-stats",e=function(t){let e=d.get(t);return e&&Date.now()-e.timestamp<3e5?e.data:null}(t);if(e)return e;try{let e=new Date;e.setHours(0,0,0,0);let a=r.Dc.fromDate(e),s=await (0,r.getDocs)((0,r.collection)(o.db,n.COLLECTIONS.users)),i=s.size,l=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.users),(0,r._M)(n.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,r.getDocs)(l)).size,E=0,u=0,w=0,h=0;s.forEach(t=>{var a;let r=t.data();E+=r[n.FIELD_NAMES.totalVideos]||0,u+=r[n.FIELD_NAMES.wallet]||0;let o=null==(a=r[n.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();o&&o.toDateString()===e.toDateString()&&(w+=r[n.FIELD_NAMES.todayVideos]||0)});try{let t=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.transactions),(0,r._M)(n.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(t)).forEach(t=>{var a;let r=t.data(),o=null==(a=r[n.FIELD_NAMES.date])?void 0:a.toDate();o&&o>=e&&(h+=r[n.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let D=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),L=(await (0,r.getDocs)(D)).size,S=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),g=(await (0,r.getDocs)(S)).size,C={totalUsers:i,totalVideos:E,totalEarnings:u,pendingWithdrawals:L,todayUsers:c,todayVideos:w,todayEarnings:h,todayWithdrawals:g};return d.set(t,{data:C,timestamp:Date.now()}),C}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.users),(0,r.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(t));e&&(a=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.users),(0,r.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(e),(0,r.AB)(t)));let d=await (0,r.getDocs)(a);return{users:d.docs.map(t=>{var e,a;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[n.FIELD_NAMES.joinedDate])?void 0:e.toDate(),planExpiry:null==(a=t.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let e=t.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.users),(0,r.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(t=>{var e,a;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[n.FIELD_NAMES.joinedDate])?void 0:e.toDate(),planExpiry:null==(a=t.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(t=>{let a=String(t[n.FIELD_NAMES.name]||"").toLowerCase(),r=String(t[n.FIELD_NAMES.email]||"").toLowerCase(),o=String(t[n.FIELD_NAMES.mobile]||"").toLowerCase(),d=String(t[n.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(e)||r.includes(e)||o.includes(e)||d.includes(e)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.users),(0,r.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(t)).docs.map(t=>{var e,a;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[n.FIELD_NAMES.joinedDate])?void 0:e.toDate(),planExpiry:null==(a=t.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.users));return(await (0,r.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function u(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));e&&(a=(0,r.P)((0,r.collection)(o.db,n.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(e),(0,r.AB)(t)));let d=await (0,r.getDocs)(a);return{withdrawals:d.docs.map(t=>{var e;return{id:t.id,...t.data(),date:null==(e=t.data().date)?void 0:e.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function w(t,e){try{await (0,r.mZ)((0,r.H9)(o.db,n.COLLECTIONS.users,t),e),d.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function h(t){try{await (0,r.kd)((0,r.H9)(o.db,n.COLLECTIONS.users,t)),d.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function D(t,e,s){try{let i=await (0,r.x7)((0,r.H9)(o.db,n.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:E}=i.data(),u={status:e,updatedAt:r.Dc.now()};if(s&&(u.adminNotes=s),await (0,r.mZ)((0,r.H9)(o.db,n.COLLECTIONS.withdrawals,t),u),"approved"===e&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await t(l,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===e&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await t(l,c),await e(l,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}d.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}}]);