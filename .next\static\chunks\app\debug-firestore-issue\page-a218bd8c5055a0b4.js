(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2848],{5018:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var i=a(5155),s=a(2115),r=a(3004),c=a(5317),n=a(6104),o=a(3592);function l(){let[e,t]=(0,s.useState)(""),[a,l]=(0,s.useState)(!1),d=e=>{t(t=>t+e+"\n")},u=async()=>{t(""),l(!0);let e=null;try{var a,i,s;d("\uD83D\uDD0D Debugging Firestore Document Creation Issue...\n"),d("=== STEP 1: Firebase Configuration Test ==="),d("Auth instance: ".concat(n.j2?"✅ Initialized":"❌ Not initialized")),d("Firestore instance: ".concat(n.db?"✅ Initialized":"❌ Not initialized")),d("Current user: ".concat((null==(a=n.j2.currentUser)?void 0:a.uid)||"None")),d("\n=== STEP 2: Basic Firestore Write Test ===");try{let e=(0,c.H9)(n.db,"test_collection","test_".concat(Date.now()));await (0,c.BN)(e,{test:!0,timestamp:c.Dc.now(),message:"Basic write test"}),d("✅ Basic Firestore write works"),(await (0,c.x7)(e)).exists()?d("✅ Basic Firestore read works"):d("❌ Basic Firestore read failed")}catch(e){d("❌ Basic Firestore write failed: ".concat(e.message)),d("   Error code: ".concat(e.code))}d("\n=== STEP 3: Firebase Auth Test ===");let t="debug".concat(Date.now(),"@test.com");try{e=(await (0,r.eJ)(n.j2,t,"debug123456")).user,d("✅ Auth user created: ".concat(e.uid)),d("   Email: ".concat(e.email)),d("   Email verified: ".concat(e.emailVerified)),await new Promise(e=>setTimeout(e,1e3)),d("   Current auth user: ".concat(null==(i=n.j2.currentUser)?void 0:i.uid)),d("   Auth state matches: ".concat((null==(s=n.j2.currentUser)?void 0:s.uid)===e.uid))}catch(e){d("❌ Auth creation failed: ".concat(e.message)),d("   Error code: ".concat(e.code));return}d("\n=== STEP 4: Users Collection Write Test ===");try{let a=(0,c.H9)(n.db,o.COLLECTIONS.users,e.uid);d("   Document path: ".concat(a.path));let i={name:"Debug Test User",email:t,mobile:"9876543210",plan:"Trial",joinedDate:c.Dc.now(),status:"active"};d("   Attempting minimal user document creation..."),await (0,c.BN)(a,i),d("✅ Minimal user document created");let s=await (0,c.x7)(a);if(s.exists()){d("✅ User document verification successful");let e=s.data();d("   Name: ".concat(e.name)),d("   Email: ".concat(e.email)),d("   Plan: ".concat(e.plan))}else d("❌ User document verification failed")}catch(e){d("❌ User document creation failed: ".concat(e.message)),d("   Error code: ".concat(e.code)),d("   Error details: ".concat(JSON.stringify(e,null,2))),"permission-denied"===e.code&&(d("\n\uD83D\uDD27 PERMISSION DENIED ANALYSIS:"),d("   This indicates Firestore security rules are blocking the write"),d("   Possible causes:"),d("   1. Rules require authentication but user is not properly authenticated"),d("   2. Rules have specific conditions that are not met"),d("   3. Rules are too restrictive for user document creation"))}d("\n=== STEP 5: Full Registration Data Test ===");let l={[o.FIELD_NAMES.name]:"Debug Full Test User",[o.FIELD_NAMES.email]:t.toLowerCase(),[o.FIELD_NAMES.mobile]:"9876543210",[o.FIELD_NAMES.referralCode]:"MY".concat(Date.now().toString().slice(-4),"AB"),[o.FIELD_NAMES.referredBy]:"",[o.FIELD_NAMES.referralBonusCredited]:!1,[o.FIELD_NAMES.plan]:"Trial",[o.FIELD_NAMES.planExpiry]:null,[o.FIELD_NAMES.activeDays]:0,[o.FIELD_NAMES.joinedDate]:c.Dc.now(),[o.FIELD_NAMES.wallet]:0,[o.FIELD_NAMES.totalVideos]:0,[o.FIELD_NAMES.todayVideos]:0,[o.FIELD_NAMES.lastVideoDate]:null,[o.FIELD_NAMES.videoDuration]:30,status:"active"};try{let t=(0,c.H9)(n.db,o.COLLECTIONS.users,"".concat(e.uid,"_full"));d("   Attempting full registration data creation..."),await (0,c.BN)(t,l),d("✅ Full registration data document created");let a=await (0,c.x7)(t);if(a.exists()){d("✅ Full document verification successful");let e=a.data();d("   Fields count: ".concat(Object.keys(e).length)),d("   Referral code: ".concat(e[o.FIELD_NAMES.referralCode])),d("   Wallet: ".concat(e[o.FIELD_NAMES.wallet]))}}catch(t){d("❌ Full registration data creation failed: ".concat(t.message)),d("   Error code: ".concat(t.code)),d("\n   Testing individual fields...");let e=[];for(let[t,a]of Object.entries(l))try{let e=(0,c.H9)(n.db,"test_fields","field_".concat(t,"_").concat(Date.now()));await (0,c.BN)(e,{[t]:a})}catch(a){e.push("".concat(t,": ").concat(a.message))}e.length>0?d("   Problematic fields: ".concat(e.join(", "))):d("   All individual fields work fine")}d("\n=== STEP 6: Alternative Collection Test ===");try{let a=(0,c.collection)(n.db,"debug_users"),i=await (0,c.gS)(a,{name:"Alternative Test",email:t,createdAt:c.Dc.now(),userId:e.uid});d("✅ Alternative collection write works: ".concat(i.id))}catch(e){d("❌ Alternative collection failed: ".concat(e.message))}d("\n=== STEP 7: Summary and Recommendations ==="),d("Based on the test results above:"),d(""),d("If basic Firestore write works but user document fails:"),d("  → Check Firestore security rules for users collection"),d("  → Verify authentication state is properly propagated"),d(""),d("If permission denied errors occur:"),d("  → Review firestore.rules file"),d("  → Ensure rules allow authenticated users to create their own documents"),d(""),d("If specific fields cause issues:"),d("  → Check for reserved field names or invalid data types"),d("  → Verify FIELD_NAMES constants are correct")}catch(e){d("❌ Debug test failed: ".concat(e.message)),d("   Error code: ".concat(e.code))}finally{if(e)try{await (0,r.hG)(e),d("\n✅ Test user cleaned up")}catch(e){d("\n⚠️ Cleanup failed: ".concat(e.message))}try{await (0,r.CI)(n.j2),d("✅ Signed out")}catch(e){d("⚠️ Sign out failed: ".concat(e.message))}l(!1)}};return(0,i.jsx)("div",{className:"min-h-screen p-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Debug Firestore Document Creation Issue"}),(0,i.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,i.jsx)("button",{onClick:u,disabled:a,className:"btn-primary mb-4",children:a?"Running Diagnostic...":"Run Firestore Diagnostic"}),(0,i.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,i.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Run Firestore Diagnostic" to start...'})})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var i=a(3915),s=a(3004),r=a(5317),c=a(858);let n=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,s.xI)(n),l=(0,r.aU)(n);(0,c.c7)(n)},8765:(e,t,a)=>{Promise.resolve().then(a.bind(a,5018))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(8765)),_N_E=e.O()}]);