import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  startAfter,
  endBefore,
  limitToLast
} from 'firebase/firestore'
import { db } from './firebase'
import { COLLECTIONS, FIELD_NAMES } from './dataService'

// Cache for admin data
const adminCache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Helper function to check cache
function getCachedData(key: string) {
  const cached = adminCache.get(key)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }
  return null
}

// Helper function to set cache
function setCachedData(key: string, data: any) {
  adminCache.set(key, {
    data,
    timestamp: Date.now()
  })
}

// Get dashboard statistics
export async function getAdminDashboardStats() {
  const cacheKey = 'dashboard-stats'
  const cached = getCachedData(cacheKey)
  if (cached) return cached

  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayTimestamp = Timestamp.fromDate(today)

    // Get total users
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    const totalUsers = usersSnapshot.size

    // Get today's new users
    const todayUsersQuery = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.joinedDate, '>=', todayTimestamp)
    )
    const todayUsersSnapshot = await getDocs(todayUsersQuery)
    const todayUsers = todayUsersSnapshot.size

    // Calculate total translations and earnings from all users
    let totalTranslations = 0
    let totalEarnings = 0
    let todayTranslations = 0
    let todayEarnings = 0

    usersSnapshot.forEach(doc => {
      const data = doc.data()
      totalTranslations += data[FIELD_NAMES.totalTranslations] || 0
      totalEarnings += data[FIELD_NAMES.wallet] || 0

      // Check if last translation was today
      const lastTranslationDate = data[FIELD_NAMES.lastTranslationDate]?.toDate()
      if (lastTranslationDate && lastTranslationDate.toDateString() === today.toDateString()) {
        todayTranslations += data[FIELD_NAMES.todayTranslations] || 0
      }
    })

    // Get today's transactions for earnings - simplified to avoid index requirement
    try {
      const allTransactionsQuery = query(
        collection(db, COLLECTIONS.transactions),
        where(FIELD_NAMES.type, '==', 'translation_earning'),
        limit(1000)
      )
      const allTransactionsSnapshot = await getDocs(allTransactionsQuery)

      // Filter in memory for today's transactions
      allTransactionsSnapshot.forEach(doc => {
        const data = doc.data()
        const transactionDate = data[FIELD_NAMES.date]?.toDate()
        if (transactionDate && transactionDate >= today) {
          todayEarnings += data[FIELD_NAMES.amount] || 0
        }
      })
    } catch (transactionError) {
      console.warn('Could not fetch today\'s transactions:', transactionError)
      // Continue without today's earnings data
    }

    // Get pending withdrawals
    const pendingWithdrawalsQuery = query(
      collection(db, COLLECTIONS.withdrawals),
      where('status', '==', 'pending')
    )
    const pendingWithdrawalsSnapshot = await getDocs(pendingWithdrawalsQuery)
    const pendingWithdrawals = pendingWithdrawalsSnapshot.size

    // Get today's withdrawals
    const todayWithdrawalsQuery = query(
      collection(db, COLLECTIONS.withdrawals),
      where('date', '>=', todayTimestamp)
    )
    const todayWithdrawalsSnapshot = await getDocs(todayWithdrawalsQuery)
    const todayWithdrawalsCount = todayWithdrawalsSnapshot.size

    const stats = {
      totalUsers,
      totalTranslations,
      totalEarnings,
      pendingWithdrawals,
      todayUsers,
      todayTranslations,
      todayEarnings,
      todayWithdrawals: todayWithdrawalsCount
    }

    setCachedData(cacheKey, stats)
    return stats
  } catch (error) {
    console.error('Error getting admin dashboard stats:', error)
    throw error
  }
}

// Get all users with pagination
export async function getUsers(pageSize = 50, lastDoc = null) {
  try {
    let q = query(
      collection(db, COLLECTIONS.users),
      orderBy(FIELD_NAMES.joinedDate, 'desc'),
      limit(pageSize)
    )

    if (lastDoc) {
      q = query(
        collection(db, COLLECTIONS.users),
        orderBy(FIELD_NAMES.joinedDate, 'desc'),
        startAfter(lastDoc),
        limit(pageSize)
      )
    }

    const snapshot = await getDocs(q)
    const users = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate(),
      planExpiry: doc.data()[FIELD_NAMES.planExpiry]?.toDate()
    }))

    return {
      users,
      lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,
      hasMore: snapshot.docs.length === pageSize
    }
  } catch (error) {
    console.error('Error getting users:', error)
    throw error
  }
}

// Search users by mobile number (exact match)
export async function searchUsersByMobile(mobile: string) {
  try {
    const q = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.mobile, '==', mobile)
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate(),
      planExpiry: doc.data()[FIELD_NAMES.planExpiry]?.toDate()
    }))
  } catch (error) {
    console.error('Error searching users by mobile:', error)
    throw error
  }
}

// Enhanced search function for users (searches across all users)
export async function searchUsers(searchTerm: string) {
  try {
    if (!searchTerm || searchTerm.trim().length === 0) {
      return []
    }

    const searchTermLower = searchTerm.toLowerCase().trim()

    // Get all users (we need to search across all users, not just paginated results)
    // Note: For large datasets, consider implementing server-side search or using Algolia
    const q = query(
      collection(db, COLLECTIONS.users),
      orderBy(FIELD_NAMES.joinedDate, 'desc')
    )

    const snapshot = await getDocs(q)
    const allUsers = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate(),
      planExpiry: doc.data()[FIELD_NAMES.planExpiry]?.toDate()
    }))

    // Filter users based on search term (client-side filtering)
    const filteredUsers = allUsers.filter((user: any) => {
      // Safely convert to string and then to lowercase
      const name = String(user[FIELD_NAMES.name] || '').toLowerCase()
      const email = String(user[FIELD_NAMES.email] || '').toLowerCase()
      const mobile = String(user[FIELD_NAMES.mobile] || '').toLowerCase()
      const referralCode = String(user[FIELD_NAMES.referralCode] || '').toLowerCase()

      return name.includes(searchTermLower) ||
             email.includes(searchTermLower) ||
             mobile.includes(searchTermLower) ||
             referralCode.includes(searchTermLower)
    })

    return filteredUsers
  } catch (error) {
    console.error('Error searching users:', error)
    throw error
  }
}

// Get all users without pagination (for search and export)
export async function getAllUsers() {
  try {
    const q = query(
      collection(db, COLLECTIONS.users),
      orderBy(FIELD_NAMES.joinedDate, 'desc')
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate(),
      planExpiry: doc.data()[FIELD_NAMES.planExpiry]?.toDate()
    }))
  } catch (error) {
    console.error('Error getting all users:', error)
    throw error
  }
}

// Get total user count
export async function getTotalUserCount() {
  try {
    const q = query(collection(db, COLLECTIONS.users))
    const snapshot = await getDocs(q)
    return snapshot.size
  } catch (error) {
    console.error('Error getting total user count:', error)
    throw error
  }
}

// Get all transactions with pagination
export async function getTransactions(pageSize = 50, lastDoc = null) {
  try {
    let q = query(
      collection(db, COLLECTIONS.transactions),
      orderBy(FIELD_NAMES.date, 'desc'),
      limit(pageSize)
    )

    if (lastDoc) {
      q = query(
        collection(db, COLLECTIONS.transactions),
        orderBy(FIELD_NAMES.date, 'desc'),
        startAfter(lastDoc),
        limit(pageSize)
      )
    }

    const snapshot = await getDocs(q)
    const transactions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data()[FIELD_NAMES.date]?.toDate()
    }))

    return {
      transactions,
      lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,
      hasMore: snapshot.docs.length === pageSize
    }
  } catch (error) {
    console.error('Error getting transactions:', error)
    throw error
  }
}

// Get all withdrawals with pagination
export async function getWithdrawals(pageSize = 50, lastDoc = null) {
  try {
    let q = query(
      collection(db, COLLECTIONS.withdrawals),
      orderBy('date', 'desc'),
      limit(pageSize)
    )

    if (lastDoc) {
      q = query(
        collection(db, COLLECTIONS.withdrawals),
        orderBy('date', 'desc'),
        startAfter(lastDoc),
        limit(pageSize)
      )
    }

    const snapshot = await getDocs(q)
    const withdrawals = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate()
    }))

    return {
      withdrawals,
      lastDoc: snapshot.docs[snapshot.docs.length - 1] || null,
      hasMore: snapshot.docs.length === pageSize
    }
  } catch (error) {
    console.error('Error getting withdrawals:', error)
    throw error
  }
}

// Update user data
export async function updateUser(userId: string, updateData: any) {
  try {
    await updateDoc(doc(db, COLLECTIONS.users, userId), updateData)
    
    // Clear relevant cache
    adminCache.delete('dashboard-stats')
  } catch (error) {
    console.error('Error updating user:', error)
    throw error
  }
}

// Delete user
export async function deleteUser(userId: string) {
  try {
    await deleteDoc(doc(db, COLLECTIONS.users, userId))
    
    // Clear relevant cache
    adminCache.delete('dashboard-stats')
  } catch (error) {
    console.error('Error deleting user:', error)
    throw error
  }
}

// Update withdrawal status
export async function updateWithdrawalStatus(withdrawalId: string, status: string, adminNotes?: string) {
  try {
    // Get withdrawal details first
    const withdrawalDoc = await getDoc(doc(db, COLLECTIONS.withdrawals, withdrawalId))
    if (!withdrawalDoc.exists()) {
      throw new Error('Withdrawal not found')
    }

    const withdrawalData = withdrawalDoc.data()
    const { userId, amount, status: currentStatus } = withdrawalData

    const updateData: any = {
      status,
      updatedAt: Timestamp.now()
    }

    if (adminNotes) {
      updateData.adminNotes = adminNotes
    }

    // Update withdrawal status
    await updateDoc(doc(db, COLLECTIONS.withdrawals, withdrawalId), updateData)

    // If withdrawal is being approved, just add a transaction record (amount already debited)
    if (status === 'approved' && currentStatus !== 'approved') {
      const { addTransaction } = await import('./dataService')

      // Add transaction record for withdrawal approval
      await addTransaction(userId, {
        type: 'withdrawal_approved',
        amount: 0, // No amount change as it was already debited
        description: `Withdrawal approved - ₹${amount} processed for transfer`
      })
    }

    // If withdrawal is being rejected, credit back the amount (since it was already debited)
    if (status === 'rejected' && currentStatus !== 'rejected') {
      const { updateWalletBalance, addTransaction } = await import('./dataService')

      // Credit back the amount to user's wallet
      await updateWalletBalance(userId, amount)

      // Add transaction record for withdrawal credit back
      await addTransaction(userId, {
        type: 'withdrawal_rejected',
        amount: amount,
        description: `Withdrawal rejected - ₹${amount} credited back to wallet`
      })
    }

    // Clear relevant cache
    adminCache.delete('dashboard-stats')
  } catch (error) {
    console.error('Error updating withdrawal status:', error)
    throw error
  }
}

// Clear admin cache
export function clearAdminCache() {
  adminCache.clear()
}
