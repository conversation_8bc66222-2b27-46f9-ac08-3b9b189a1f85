// Service Worker for Instra Global Next.js App
const CACHE_NAME = 'instra-global-nextjs-v1'
const urlsToCache = [
  '/',
  '/dashboard',
  '/work',
  '/wallet',
  '/profile',
  '/refer',
  '/login',
  '/register',
  '/manifest.json',
  '/img/instra-logo.svg',
  '/img/instra-favicon.svg'
]

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache)
      })
  )
})

// Fetch event
self.addEventListener('fetch', (event) => {
  // Skip caching for Firebase API calls, JavaScript files, and critical assets
  if (event.request.url.includes('firebaseapp.com') ||
      event.request.url.includes('googleapis.com') ||
      event.request.url.includes('firebase') ||
      event.request.url.includes('/_next/') ||
      event.request.url.endsWith('.js') ||
      event.request.url.endsWith('.css') ||
      event.request.method === 'POST') {
    event.respondWith(fetch(event.request))
    return
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
      })
  )
})

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
})
