'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'
import Swal from 'sweetalert2'

export default function AdminSetupPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [setupComplete, setSetupComplete] = useState(false)

  const createAdminAccount = async () => {
    setIsLoading(true)

    try {
      // Check if admin already exists
      const adminEmail = '<EMAIL>'
      const adminPassword = '123456'

      // Try to create the admin user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        adminEmail,
        adminPassword
      )
      
      const user = userCredential.user

      // Create admin user document in Firestore
      const adminData = {
        [FIELD_NAMES.name]: 'Instra Global Admin',
        [FIELD_NAMES.email]: adminEmail,
        [FIELD_NAMES.mobile]: '**********',
        [FIELD_NAMES.referralCode]: 'TN0000',
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.plan]: 'Admin',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 999999,
        [FIELD_NAMES.totalTranslations]: 0,
        [FIELD_NAMES.todayTranslations]: 0,
        [FIELD_NAMES.lastTranslationDate]: null,
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        status: 'active',
        [FIELD_NAMES.translationDuration]: 300,
        // Admin specific fields
        role: 'admin',
        isAdmin: true,
        permissions: ['all']
      }

      await setDoc(doc(db, COLLECTIONS.users, user.uid), adminData)

      // Create admin document in admins collection for security rules
      const adminDoc = {
        email: adminEmail,
        name: 'Instra Global Admin',
        role: 'super_admin',
        permissions: ['all'],
        createdAt: Timestamp.now(),
        isActive: true
      }
      await setDoc(doc(db, 'admins', user.uid), adminDoc)

      // Sign out the admin user (they'll need to login properly)
      await auth.signOut()

      setSetupComplete(true)

      Swal.fire({
        icon: 'success',
        title: 'Admin Account Created!',
        html: `
          <div class="text-left">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> 123456</p>
            <br>
            <p>The admin account has been successfully created. You can now login using these credentials.</p>
          </div>
        `,
        confirmButtonText: 'Go to Admin Login'
      }).then(() => {
        window.location.href = '/admin/login'
      })

    } catch (error: any) {
      console.error('Error creating admin account:', error)
      
      let message = 'Failed to create admin account'
      
      if (error.code === 'auth/email-already-in-use') {
        message = 'Admin account already exists! You can login with: <EMAIL> / 123456'

        Swal.fire({
          icon: 'info',
          title: 'Admin Account Exists',
          html: `
            <div class="text-left">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> 123456</p>
              <br>
              <p>The admin account already exists. Use these credentials to login.</p>
            </div>
          `,
          confirmButtonText: 'Go to Admin Login'
        }).then(() => {
          window.location.href = '/admin/login'
        })
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Setup Failed',
          text: message,
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="glass-card w-full max-w-md p-8">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/img/instra-logo.svg"
              alt="Instra Global Logo"
              width={50}
              height={50}
              className="mr-3"
            />
            <span className="text-2xl font-bold text-white">Instra Global</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Admin Setup</h1>
          <p className="text-white/80">Create the admin account for Instra Global</p>
        </div>

        {!setupComplete ? (
          <div className="space-y-6">
            {/* Setup Information */}
            <div className="bg-blue-500/20 rounded-lg p-4 border border-blue-500/30">
              <h3 className="text-white font-semibold mb-2">
                <i className="fas fa-info-circle mr-2"></i>
                Admin Account Details
              </h3>
              <div className="text-white/80 text-sm space-y-1">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> 123456</p>
                <p><strong>Role:</strong> Super Administrator</p>
              </div>
            </div>

            {/* Setup Button */}
            <button
              onClick={createAdminAccount}
              disabled={isLoading}
              className="w-full btn-primary flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <div className="spinner mr-2 w-5 h-5"></div>
                  Creating Admin Account...
                </>
              ) : (
                <>
                  <i className="fas fa-user-shield mr-2"></i>
                  Create Admin Account
                </>
              )}
            </button>

            {/* Warning */}
            <div className="bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30">
              <div className="flex items-start text-yellow-300">
                <i className="fas fa-exclamation-triangle mr-2 mt-1"></i>
                <div className="text-sm">
                  <p className="font-semibold mb-1">Security Notice:</p>
                  <p>This will create an admin account with full system access. Make sure to change the password after first login for security.</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-green-400 text-6xl mb-4">
              <i className="fas fa-check-circle"></i>
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Setup Complete!</h2>
            <p className="text-white/80 mb-6">Admin account has been created successfully.</p>
            <Link
              href="/admin/login"
              className="btn-primary inline-flex items-center"
            >
              <i className="fas fa-sign-in-alt mr-2"></i>
              Go to Admin Login
            </Link>
          </div>
        )}

        {/* Navigation */}
        <div className="mt-8 text-center space-y-2">
          <Link
            href="/admin/login"
            className="text-white/80 hover:text-white transition-colors inline-flex items-center"
          >
            <i className="fas fa-sign-in-alt mr-2"></i>
            Admin Login
          </Link>
          <br />
          <Link
            href="/"
            className="text-white/60 hover:text-white/80 transition-colors inline-flex items-center text-sm"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
