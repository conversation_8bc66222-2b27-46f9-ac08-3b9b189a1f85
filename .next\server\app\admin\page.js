(()=>{var e={};e.id=3698,e.ids=[1391,3698],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1132:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12454:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687),a=s(43210),i=s(85814),o=s.n(i),l=s(30474),n=s(87979);s(91391);var d=s(51278);function c(){let{user:e,loading:t,isAdmin:s}=(0,n.wC)(),[i,c]=(0,a.useState)(null),[x,h]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1);return t||x?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${u?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,r.jsx)(l.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:32,height:32,className:"mr-2"}),(0,r.jsx)("span",{className:"text-white text-xl font-bold",children:"Instra Global Admin"})]}),(0,r.jsx)("nav",{className:"mt-8",children:(0,r.jsxs)("div",{className:"px-4 space-y-2",children:[(0,r.jsxs)(o(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,r.jsxs)(o(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,r.jsxs)(o(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,r.jsxs)(o(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,r.jsxs)(o(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,r.jsxs)(o(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,r.jsxs)(o(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,r.jsxs)(o(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,r.jsxs)(o(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]})]})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,r.jsxs)("button",{onClick:()=>{(0,d._f)(e?.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,r.jsxs)("div",{className:"lg:ml-64",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsx)("button",{onClick:()=>m(!u),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,r.jsxs)("main",{className:"p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.totalUsers?.toLocaleString()||"0"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-video text-green-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Videos"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.totalVideos?.toLocaleString()||"0"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",i?.totalEarnings?.toLocaleString()||"0"]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.pendingWithdrawals||"0"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:i?.todayUsers||"0"}),(0,r.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-green-600",children:i?.todayVideos?.toLocaleString()||"0"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Videos Watched"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",i?.todayEarnings?.toLocaleString()||"0"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-3xl font-bold text-red-600",children:i?.todayWithdrawals||"0"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(o(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,r.jsx)(o(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,r.jsx)(o(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,r.jsx)(o(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,r.jsx)(o(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,r.jsx)(o(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Update videos, wallet & active days via CSV"})]})]})})]})]})]}),u&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>m(!1)})]})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},18625:(e,t,s)=>{Promise.resolve().then(s.bind(s,1132))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31777:(e,t,s)=>{Promise.resolve().then(s.bind(s,12454))},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>d,j2:()=>n});var r=s(67989),a=s(63385),i=s(75535),o=s(70146);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),n=(0,a.xI)(l),d=(0,i.aU)(l);(0,o.c7)(l)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47314:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),l=s(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},51278:(e,t,s)=>{"use strict";s.d(t,{M4:()=>l,_f:()=>o});var r=s(33784),a=s(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e,t="/login"){try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>o,hD:()=>i,wC:()=>l});var r=s(43210);s(63385),s(33784);var a=s(51278);function i(){let[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)(!0),o=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:o}}function o(){let{user:e,loading:t}=i();return{user:e,loading:t}}function l(){let{user:e,loading:t}=i(),[s,a]=(0,r.useState)(!1),[o,l]=(0,r.useState)(!0);return{user:e,loading:t||o,isAdmin:s}}},91391:(e,t,s)=>{"use strict";s.d(t,{CF:()=>c,Pn:()=>l,TK:()=>u,getWithdrawals:()=>h,hG:()=>m,lo:()=>n,nQ:()=>x,updateWithdrawalStatus:()=>g,x5:()=>d});var r=s(75535),a=s(33784),i=s(3582);let o=new Map;async function l(){let e="dashboard-stats",t=function(e){let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=r.Dc.fromDate(t),l=await (0,r.getDocs)((0,r.collection)(a.db,i.COLLECTIONS.users)),n=l.size,d=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r._M)(i.FIELD_NAMES.joinedDate,">=",s)),c=(await (0,r.getDocs)(d)).size,x=0,h=0,u=0,m=0;l.forEach(e=>{let s=e.data();x+=s[i.FIELD_NAMES.totalVideos]||0,h+=s[i.FIELD_NAMES.wallet]||0;let r=s[i.FIELD_NAMES.lastVideoDate]?.toDate();r&&r.toDateString()===t.toDateString()&&(u+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.transactions),(0,r._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{let s=e.data(),r=s[i.FIELD_NAMES.date]?.toDate();r&&r>=t&&(m+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),p=(await (0,r.getDocs)(g)).size,f=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r._M)("date",">=",s)),N=(await (0,r.getDocs)(f)).size,w={totalUsers:n,totalVideos:x,totalEarnings:h,pendingWithdrawals:p,todayUsers:c,todayVideos:u,todayEarnings:m,todayWithdrawals:N};return o.set(e,{data:w,timestamp:Date.now()}),w}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(e=50,t=null){try{let s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(e));t&&(s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let o=await (0,r.getDocs)(s);return{users:o.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(s)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let s=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),o=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||r.includes(t)||a.includes(t)||o.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function x(){try{let e=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(e=50,t=null){try{let s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let o=await (0,r.getDocs)(s);return{withdrawals:o.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function u(e,t){try{await (0,r.mZ)((0,r.H9)(a.db,i.COLLECTIONS.users,e),t),o.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function m(e){try{await (0,r.kd)((0,r.H9)(a.db,i.COLLECTIONS.users,e)),o.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function g(e,t,l){try{let n=await (0,r.x7)((0,r.H9)(a.db,i.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:x}=n.data(),h={status:t,updatedAt:r.Dc.now()};if(l&&(h.adminNotes=l),await (0,r.mZ)((0,r.H9)(a.db,i.COLLECTIONS.withdrawals,e),h),"approved"===t&&"approved"!==x){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==x){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}o.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,2756,7567,5901,3582],()=>s(47314));module.exports=r})();