rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Simple admin check
    function isAdmin() {
      return request.auth != null &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    // Users collection - SIMPLIFIED RULES FOR DEBUGGING
    match /users/{userId} {
      // Allow all authenticated users to read/write any user document (temporary for debugging)
      allow read, write: if request.auth != null;
      // Allow admins to read/write any document
      allow read, write: if isAdmin();
      // Allow all reads for registration checks
      allow read: if true;
    }

    // System collection - counters, settings, etc.
    match /system/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Transactions collection
    match /transactions/{transactionId} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read, write: if isAdmin();
    }

    match /transactions/{document=**} {
      allow read: if request.auth != null;
    }

    // Withdrawals collection
    match /withdrawals/{withdrawalId} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read, write: if isAdmin();
    }

    match /withdrawals/{document=**} {
      allow read: if request.auth != null;
    }

    // Plans collection
    match /plans/{planId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    match /plans/{document=**} {
      allow read: if true;
    }

    // Settings collection
    match /settings/{settingId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    match /settings/{document=**} {
      allow read: if true;
    }

    // Notifications collection
    match /notifications/{notificationId} {
      allow read: if request.auth != null;
      allow write: if isAdmin();
    }

    match /notifications/{document=**} {
      allow read: if request.auth != null;
    }

    // Admin leaves collection
    match /adminLeaves/{leaveId} {
      allow read: if request.auth != null;
      allow write: if isAdmin();
    }

    match /adminLeaves/{document=**} {
      allow read: if request.auth != null;
    }

    // User leaves collection
    match /userLeaves/{leaveId} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
      allow read, write: if isAdmin();
    }

    match /userLeaves/{document=**} {
      allow read: if request.auth != null;
    }

    // Bank details collection
    match /bankDetails/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read, write: if isAdmin();
    }

    // Translation data collection
    match /translationData/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read, write: if isAdmin();
    }

    // User sessions collection
    match /userSessions/{sessionId} {
      allow read, write: if request.auth != null;
      allow read, write: if isAdmin();
    }

    match /userSessions/{document=**} {
      allow read: if request.auth != null;
    }

    // Referrals collection
    match /referrals/{referralId} {
      allow read, write: if request.auth != null;
      allow read, write: if isAdmin();
    }

    match /referrals/{document=**} {
      allow read: if request.auth != null;
    }

    // Admin logs collection
    match /adminLogs/{logId} {
      allow read, write: if isAdmin();
    }

    match /adminLogs/{document=**} {
      allow read: if isAdmin();
    }

    // Admins collection
    match /admins/{adminId} {
      allow read: if request.auth != null;
      allow write: if isAdmin();
    }

    // Test collection
    match /test_collection/{docId} {
      allow read, write: if true;
    }

    // Public data collection
    match /public/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Counters collection
    match /counters/{counterId} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Allow all other collections for authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
      allow read, write: if isAdmin();
    }
  }
}
