"use strict";exports.id=3582,exports.ids=[3582],exports.modules={3582:(t,a,e)=>{e.d(a,{AX:()=>M,COLLECTIONS:()=>i,FIELD_NAMES:()=>o,HY:()=>w,I0:()=>g,II:()=>k,IK:()=>A,Q6:()=>E,QD:()=>G,Ss:()=>$,_f:()=>I,addTransaction:()=>u,b6:()=>d,bA:()=>B,fP:()=>N,getPlanValidityDays:()=>b,getUserData:()=>s,getVideoCountData:()=>c,getWalletData:()=>l,gj:()=>m,i8:()=>Z,iA:()=>U,iF:()=>p,isUserPlanExpired:()=>T,mm:()=>D,mv:()=>P,pl:()=>f,pu:()=>F,ul:()=>_,updateWalletBalance:()=>h,w1:()=>x,wT:()=>q,x4:()=>j,xj:()=>C,yx:()=>y,z8:()=>S,zb:()=>v});var r=e(75535),n=e(33784);let o={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalTranslations:"totalTranslations",todayTranslations:"todayTranslations",lastTranslationDate:"lastTranslationDate",translationDuration:"translationDuration",quickTranslationAdvantage:"quickTranslationAdvantage",quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",quickTranslationAdvantageDays:"quickTranslationAdvantageDays",quickTranslationAdvantageSeconds:"quickTranslationAdvantageSeconds",quickTranslationAdvantageGrantedBy:"quickTranslationAdvantageGrantedBy",quickTranslationAdvantageGrantedAt:"quickTranslationAdvantageGrantedAt",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getUserData:",t),null;let a=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(a.exists()){let t=a.data(),e={name:String(t[o.name]||""),email:String(t[o.email]||""),mobile:String(t[o.mobile]||""),referralCode:String(t[o.referralCode]||""),referredBy:String(t[o.referredBy]||""),plan:String(t[o.plan]||"Trial"),planExpiry:t[o.planExpiry]?.toDate()||null,activeDays:Number(t[o.activeDays]||0),joinedDate:t[o.joinedDate]?.toDate()||new Date,translationDuration:Number(t[o.translationDuration]||("Trial"===t[o.plan]?30:300)),quickTranslationAdvantage:!!t[o.quickTranslationAdvantage],quickTranslationAdvantageExpiry:t[o.quickTranslationAdvantageExpiry]?.toDate()||null,quickTranslationAdvantageDays:Number(t[o.quickTranslationAdvantageDays]||0),quickTranslationAdvantageSeconds:Number(t[o.quickTranslationAdvantageSeconds]||30),quickTranslationAdvantageGrantedBy:String(t[o.quickTranslationAdvantageGrantedBy]||""),quickTranslationAdvantageGrantedAt:t[o.quickTranslationAdvantageGrantedAt]?.toDate()||null};return console.log("getUserData result:",e),e}return null}catch(t){return console.error("Error getting user data:",t),null}}async function l(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getWalletData:",t),{wallet:0};let a=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(a.exists()){let t=a.data(),e={wallet:Number(t[o.wallet]||0)};return console.log("getWalletData result:",e),e}return{wallet:0}}catch(t){return console.error("Error getting wallet data:",t),{wallet:0}}}async function c(t){try{let a=(0,r.H9)(n.db,i.users,t),e=await (0,r.x7)(a);if(e.exists()){let n=e.data(),i=n[o.totalTranslations]||0,s=n[o.todayTranslations]||0,l=n[o.lastTranslationDate]?.toDate(),c=new Date;if((!l||l.toDateString()!==c.toDateString())&&s>0){console.log(`🔄 Resetting daily translation count for user ${t} (was ${s})`),await (0,r.mZ)(a,{[o.todayTranslations]:0}),s=0;try{await p(t)}catch(t){console.error("Error updating active days during daily reset:",t)}}return{totalTranslations:i,todayTranslations:s,remainingTranslations:Math.max(0,50-s)}}return{totalTranslations:0,todayTranslations:0,remainingTranslations:50}}catch(t){throw console.error("Error getting video count data:",t),t}}async function d(t,a){try{await (0,r.mZ)((0,r.H9)(n.db,i.users,t),a)}catch(t){throw console.error("Error updating user data:",t),t}}async function u(t,a){try{let e={[o.userId]:t,[o.type]:a.type,[o.amount]:a.amount,[o.description]:a.description,[o.status]:a.status||"completed",[o.date]:r.Dc.now()};await (0,r.gS)((0,r.collection)(n.db,i.transactions),e)}catch(t){throw console.error("Error adding transaction:",t),t}}async function g(t,a=10){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getTransactions:",t),[];let e=(0,r.P)((0,r.collection)(n.db,i.transactions),(0,r._M)(o.userId,"==",t),(0,r.AB)(a)),s=(await (0,r.getDocs)(e)).docs.map(t=>({id:t.id,...t.data(),date:t.data()[o.date]?.toDate()}));return s.sort((t,a)=>{let e=t.date||new Date(0);return(a.date||new Date(0)).getTime()-e.getTime()}),s}catch(t){return console.error("Error getting transactions:",t),[]}}async function f(t){try{let a=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referredBy,"==",t));return(await (0,r.getDocs)(a)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[o.joinedDate]?.toDate()}))}catch(t){throw console.error("Error getting referrals:",t),t}}async function y(t){try{let a=new Date,e=(0,r.H9)(n.db,i.users,t),s=await (0,r.x7)(e);if(s.exists()){let n=s.data(),i=n[o.lastTranslationDate]?.toDate(),l=n[o.todayTranslations]||0;(!i||i.toDateString()!==a.toDateString())&&l>0?(console.log(`🔄 Resetting and updating daily translation count for user ${t}`),await (0,r.mZ)(e,{[o.totalTranslations]:(0,r.GV)(1),[o.todayTranslations]:1,[o.lastTranslationDate]:r.Dc.fromDate(a)})):await (0,r.mZ)(e,{[o.totalTranslations]:(0,r.GV)(1),[o.todayTranslations]:(0,r.GV)(1),[o.lastTranslationDate]:r.Dc.fromDate(a)})}else await (0,r.mZ)(e,{[o.totalTranslations]:(0,r.GV)(1),[o.todayTranslations]:(0,r.GV)(1),[o.lastTranslationDate]:r.Dc.fromDate(a)})}catch(t){throw console.error("Error updating translation count:",t),t}}async function w(t){try{let a=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(a,{[o.todayTranslations]:0}),console.log(`✅ Reset daily translation count for user ${t}`)}catch(t){throw console.error("Error resetting daily translation count:",t),t}}async function p(t){try{let a=await s(t);if(!a)return void console.error("User data not found for active days update:",t);let l=0;if("Trial"===a.plan){let t=a.joinedDate||new Date,e=new Date;l=Math.floor((e.getTime()-t.getTime())/864e5)+1}else{let{calculateActiveDays:r}=await e.e(7087).then(e.bind(e,87087)),n=a.planExpiry?new Date(a.planExpiry.getTime()-24*b(a.plan)*36e5):a.joinedDate||new Date,o=await r(t,n);l=Math.max(1,o+1)}let c=a.activeDays||0;if(l!==c){console.log(`📅 Updating active days for user ${t}: ${c} → ${l}`);let a=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(a,{[o.activeDays]:l})}return l}catch(t){throw console.error("Error updating user active days:",t),t}}async function m(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let t=await (0,r.getDocs)((0,r.collection)(n.db,i.users)),a=0,e=0;for(let r of t.docs)try{await p(r.id),a++}catch(t){console.error(`Error fixing active days for user ${r.id}:`,t),e++}return console.log(`✅ Fixed active days for ${a} users, ${e} errors`),{fixedCount:a,errorCount:e}}catch(t){throw console.error("Error fixing all users active days:",t),t}}async function h(t,a){try{let e=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(e,{[o.wallet]:(0,r.GV)(a)})}catch(t){throw console.error("Error updating wallet balance:",t),t}}async function D(t,a){try{if(!t||"string"!=typeof t)throw Error("Invalid userId provided");var e=a;let{accountHolderName:s,accountNumber:l,ifscCode:c,bankName:d}=e;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!l||!/^\d{9,18}$/.test(l.trim()))throw Error("Account number must be 9-18 digits");if(!c||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(c.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!d||d.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(u,{[o.bankAccountHolderName]:a.accountHolderName.trim(),[o.bankAccountNumber]:a.accountNumber.trim(),[o.bankIfscCode]:a.ifscCode.trim().toUpperCase(),[o.bankName]:a.bankName.trim(),[o.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",t)}catch(t){throw console.error("Error saving bank details:",t),t}}async function v(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getBankDetails:",t),null;let a=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(a.exists()){let t=a.data();if(t[o.bankAccountNumber]){let a={accountHolderName:String(t[o.bankAccountHolderName]||""),accountNumber:String(t[o.bankAccountNumber]||""),ifscCode:String(t[o.bankIfscCode]||""),bankName:String(t[o.bankName]||"")};return console.log("getBankDetails result found"),a}}return console.log("No bank details found for user"),null}catch(t){return console.error("Error getting bank details:",t),null}}function b(t){return({Trial:2,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[t]||2}async function T(t){try{let a=await s(t);if(!a)return{expired:!0,reason:"User data not found"};if("Trial"===a.plan){let t=a.joinedDate||new Date,e=new Date,r=Math.floor((e.getTime()-t.getTime())/864e5)+1,n=Math.max(0,2-r);return{expired:n<=0,reason:n<=0?"Trial period expired":void 0,daysLeft:n,activeDays:r}}if(a.planExpiry){let t=new Date,e=t>a.planExpiry,r=e?0:Math.ceil((a.planExpiry.getTime()-t.getTime())/864e5);return{expired:e,reason:e?"Plan subscription expired":void 0,daysLeft:r,activeDays:a.activeDays||0}}let e=b(a.plan),r=a.activeDays||0,n=Math.max(0,e-r),o=n<=0;return{expired:o,reason:o?`Plan validity period (${e} days) exceeded based on active days`:void 0,daysLeft:n,activeDays:r}}catch(t){return console.error("Error checking plan expiry:",t),{expired:!0,reason:"Error checking plan status"}}}async function k(t,a,e){try{let s=(0,r.H9)(n.db,i.users,t);if("Trial"===a)await (0,r.mZ)(s,{[o.planExpiry]:null});else{let n;if(e)n=e;else{let t=b(a),e=new Date;n=new Date(e.getTime()+24*t*36e5)}await (0,r.mZ)(s,{[o.planExpiry]:r.Dc.fromDate(n)}),console.log(`Updated plan expiry for user ${t} to ${n.toDateString()}`)}}catch(t){throw console.error("Error updating plan expiry:",t),t}}async function A(t,a,e){try{if("Trial"!==a||"Trial"===e)return void console.log("Referral bonus only applies when upgrading from Trial to paid plan");console.log(`Processing referral bonus for user ${t} upgrading from ${a} to ${e}`);let s=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(!s.exists())return void console.log("User not found");let l=s.data(),c=l[o.referredBy],d=l[o.referralBonusCredited];if(!c)return void console.log("User was not referred by anyone, skipping bonus processing");if(d)return void console.log("Referral bonus already credited for this user, skipping");console.log("Finding referrer with code:",c);let g=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referralCode,"==",c),(0,r.AB)(1)),f=await (0,r.getDocs)(g);if(f.empty)return void console.log("Referral code not found:",c);let y=f.docs[0].id,w={Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[e]||0;if(console.log(`Found referrer: ${y}, bonus amount: ₹${w}`),w>0){await h(y,w);let a=(0,r.H9)(n.db,i.users,y);await (0,r.mZ)(a,{[o.totalTranslations]:(0,r.GV)(50)});let s=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(s,{[o.referralBonusCredited]:!0}),await u(y,{type:"referral_bonus",amount:w,description:`Referral bonus for ${e} plan upgrade + 50 bonus translations (User: ${l[o.name]})`}),console.log(`✅ Referral bonus processed: ₹${w} + 50 translations for referrer ${y}`)}else console.log("No bonus amount calculated, skipping")}catch(t){console.error("❌ Error processing referral bonus:",t)}}async function E(t){try{var a;let e=await s(t);if(!e)return{translationDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1};let r=!!(a=e).quickTranslationAdvantage&&!!a.quickTranslationAdvantageExpiry&&new Date<a.quickTranslationAdvantageExpiry,n=e.translationDuration;return r?n=e.quickTranslationAdvantageSeconds||30:n&&"Trial"!==e.plan||(n=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[e.plan]||30),{translationDuration:n,earningPerBatch:({Trial:10,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[e.plan]||10,plan:e.plan,hasQuickAdvantage:r,quickAdvantageExpiry:e.quickTranslationAdvantageExpiry}}catch(t){return console.error("Error getting user translation settings:",t),{translationDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1}}}async function x(t,a,e,s=30){try{if(a<=0||a>365)throw Error("Days must be between 1 and 365");if(s<1||s>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let l=new Date,c=new Date(l.getTime()+24*a*36e5),d=(0,r.H9)(n.db,i.users,t);return await (0,r.mZ)(d,{[o.quickTranslationAdvantage]:!0,[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(c),[o.quickTranslationAdvantageDays]:a,[o.quickTranslationAdvantageSeconds]:s,[o.quickTranslationAdvantageGrantedBy]:e,[o.quickTranslationAdvantageGrantedAt]:r.Dc.fromDate(l)}),console.log(`Granted quick translation advantage to user ${t} for ${a} days until ${c.toDateString()}`),await u(t,{type:"quick_advantage_granted",amount:0,description:`Quick translation advantage granted for ${a} days by ${e}`}),{success:!0,expiry:c}}catch(t){throw console.error("Error granting quick video advantage:",t),t}}async function q(t,a){try{let e=(0,r.H9)(n.db,i.users,t);return await (0,r.mZ)(e,{[o.quickTranslationAdvantage]:!1,[o.quickTranslationAdvantageExpiry]:null,[o.quickTranslationAdvantageDays]:0,[o.quickTranslationAdvantageSeconds]:30,[o.quickTranslationAdvantageGrantedBy]:"",[o.quickTranslationAdvantageGrantedAt]:null}),console.log(`Removed quick translation advantage from user ${t}`),await u(t,{type:"quick_advantage_removed",amount:0,description:`Quick translation advantage removed by ${a}`}),{success:!0}}catch(t){throw console.error("Error removing quick video advantage:",t),t}}async function S(t){try{let a={title:t.title,message:t.message,type:t.type,targetUsers:t.targetUsers,userIds:t.userIds||[],createdAt:r.Dc.now(),createdBy:t.createdBy};console.log("Adding notification to Firestore:",a);let e=await (0,r.gS)((0,r.collection)(n.db,i.notifications),a);console.log("Notification added successfully with ID:",e.id);let o=await (0,r.x7)(e);return o.exists()?console.log("Notification verified in database:",o.data()):console.warn("Notification not found after adding"),e.id}catch(t){throw console.error("Error adding notification:",t),t}}async function $(t,a=20){try{let e,o;if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getUserNotifications:",t),[];console.log(`Loading notifications for user: ${t}`);try{let t=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(a));e=await (0,r.getDocs)(t),console.log(`Found ${e.docs.length} notifications for all users`)}catch(o){console.warn("Error querying all users notifications, trying without orderBy:",o);let t=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(a));e=await (0,r.getDocs)(t)}try{let e=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",t),(0,r.My)("createdAt","desc"),(0,r.AB)(a));o=await (0,r.getDocs)(e),console.log(`Found ${o.docs.length} notifications for specific user`)}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let e=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",t),(0,r.AB)(a));o=await (0,r.getDocs)(e)}let s=[];e.docs.forEach(t=>{s.push({id:t.id,...t.data(),createdAt:t.data().createdAt?.toDate()||new Date})}),o.docs.forEach(t=>{s.push({id:t.id,...t.data(),createdAt:t.data().createdAt?.toDate()||new Date})}),s.sort((t,a)=>a.createdAt.getTime()-t.createdAt.getTime());let l=s.slice(0,a);return console.log(`Returning ${l.length} total notifications for user`),l}catch(t){return console.error("Error getting user notifications:",t),[]}}async function I(t=50){try{let a=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(t));return(await (0,r.getDocs)(a)).docs.map(t=>({id:t.id,...t.data(),createdAt:t.data().createdAt?.toDate()||new Date}))}catch(t){return console.error("Error getting all notifications:",t),[]}}async function N(t){try{if(!t||"string"!=typeof t)throw Error("Invalid notification ID provided");console.log("Deleting notification:",t),await (0,r.kd)((0,r.H9)(n.db,i.notifications,t)),console.log("Notification deleted successfully")}catch(t){throw console.error("Error deleting notification:",t),t}}async function B(t,a){try{let e=JSON.parse(localStorage.getItem(`read_notifications_${a}`)||"[]");e.includes(t)||(e.push(t),localStorage.setItem(`read_notifications_${a}`,JSON.stringify(e)))}catch(t){console.error("Error marking notification as read:",t)}}function P(t,a){try{return JSON.parse(localStorage.getItem(`read_notifications_${a}`)||"[]").includes(t)}catch(t){return console.error("Error checking notification read status:",t),!1}}function _(t,a){try{let e=JSON.parse(localStorage.getItem(`read_notifications_${a}`)||"[]");return t.filter(t=>!e.includes(t.id)).length}catch(t){return console.error("Error getting unread notification count:",t),0}}async function M(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getUnreadNotifications:",t),[];console.log(`Loading unread notifications for user: ${t}`);let a=await $(t,50),e=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]"),r=a.filter(t=>t.id&&!e.includes(t.id));return console.log(`Found ${r.length} unread notifications`),r}catch(t){return console.error("Error getting unread notifications:",t),[]}}async function U(t){try{return(await M(t)).length>0}catch(t){return console.error("Error checking for unread notifications:",t),!1}}async function H(t){try{let a=(0,r.P)((0,r.collection)(n.db,i.withdrawals),(0,r._M)("userId","==",t),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.getDocs)(a)).empty}catch(t){return console.error("Error checking pending withdrawals:",t),!1}}async function G(t){try{let a=await s(t);if(!a)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===a.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await H(t))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let r=new Date,n=r.getHours();if(n<10||n>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:o}=await e.e(7087).then(e.bind(e,87087));if(await o(r))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await e.e(7087).then(e.bind(e,87087));if(await i(t,r))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(t){return console.error("Error checking withdrawal allowed:",t),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function C(t,a,e){try{if(a<50)throw Error("Minimum withdrawal amount is ₹50");let o=await G(t);if(!o.allowed)throw Error(o.reason);if((await l(t)).wallet<a)throw Error("Insufficient wallet balance");await h(t,-a),await u(t,{type:"withdrawal_request",amount:-a,description:`Withdrawal request submitted - ₹${a} debited from wallet`});let s={userId:t,amount:a,bankDetails:e,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()};return(await (0,r.gS)((0,r.collection)(n.db,i.withdrawals),s)).id}catch(t){throw console.error("Error creating withdrawal request:",t),t}}async function Z(t,a=20){try{let e=(0,r.P)((0,r.collection)(n.db,i.withdrawals),(0,r._M)("userId","==",t),(0,r.My)("date","desc"),(0,r.AB)(a));return(await (0,r.getDocs)(e)).docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()}))}catch(t){return console.error("Error getting user withdrawals:",t),[]}}async function j(){try{try{let t=(0,r.collection)(n.db,i.users),a=((await (0,r.d_)(t)).data().count+1).toString().padStart(4,"0");return`TN${a}`}catch(e){console.warn("Failed to get count from server, using fallback method:",e);let t=Date.now().toString().slice(-4),a=Math.random().toString(36).substring(2,4).toUpperCase();return`TN${t}${a}`}}catch(a){console.error("Error generating unique referral code:",a);let t=Date.now().toString().slice(-4);return`TN${t}`}}async function F(){return j()}}};