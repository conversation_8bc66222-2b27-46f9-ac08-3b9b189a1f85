(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{12:(e,r,t)=>{"use strict";t.d(r,{M4:()=>n,_f:()=>i});var s=t(6104),o=t(4752),a=t.n(o);function l(e){try{Object.keys(localStorage).forEach(r=>{(r.includes(e)||r.startsWith("video_session_")||r.startsWith("watch_times_")||r.startsWith("video_refresh_")||r.startsWith("video_change_notification_")||r.startsWith("leave_")||r.includes("mytube_")||r.includes("user_"))&&localStorage.removeItem(r)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function i(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await a().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e),await s.j2.signOut(),a().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=r}),!0;return!1}catch(e){return console.error("Logout error:",e),a().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&l(e),await s.j2.signOut(),window.location.href=r}catch(e){console.error("Quick logout error:",e),window.location.href=r}}},1469:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return n},getImageProps:function(){return i}});let s=t(8229),o=t(8883),a=t(3063),l=s._(t(1193));function i(e){let{props:r}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let n=a.Image},3587:(e,r,t)=>{Promise.resolve().then(t.bind(t,6616))},6104:(e,r,t)=>{"use strict";t.d(r,{db:()=>c,j2:()=>n});var s=t(3915),o=t(3004),a=t(5317),l=t(858);let i=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),n=(0,o.xI)(i),c=(0,a.aU)(i);(0,l.c7)(i)},6616:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(5155),o=t(2115),a=t(6874),l=t.n(a),i=t(6766),n=t(3004),c=t(5317),d=t(6104),u=t(6681),m=t(3592),f=t(4752),h=t.n(f);function g(){let{user:e,loading:r}=(0,u.hD)(),[t,a]=(0,o.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[f,g]=(0,o.useState)(!1),[w,p]=(0,o.useState)(!1),[b,x]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("ref");e&&a(r=>({...r,referralCode:e}))},[]);let y=e=>{let{name:r,value:t}=e.target;a(e=>({...e,[r]:t}))},N=()=>{let{name:e,email:r,mobile:s,password:o,confirmPassword:a}=t;if(!e||!r||!s||!o||!a)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(s))throw Error("Please enter a valid 10-digit mobile number");if(o.length<6)throw Error("Password must be at least 6 characters long");if(o!==a)throw Error("Passwords do not match")},v=async e=>{e.preventDefault();try{N(),g(!0),console.log("Creating user with email and password...");let e=(await (0,n.eJ)(d.j2,t.email,t.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let r=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),o="TN".concat(r).concat(s);console.log("Generated referral code:",o);let a={[m.FIELD_NAMES.name]:t.name.trim(),[m.FIELD_NAMES.email]:t.email.toLowerCase(),[m.FIELD_NAMES.mobile]:t.mobile,[m.FIELD_NAMES.referralCode]:o,[m.FIELD_NAMES.referredBy]:t.referralCode||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:1,[m.FIELD_NAMES.joinedDate]:c.Dc.now(),[m.FIELD_NAMES.wallet]:0,[m.FIELD_NAMES.totalVideos]:0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.videoDuration]:30,status:"active"};console.log("Creating user document with data:",a),console.log("User UID:",e.uid),console.log("Collection:",m.COLLECTIONS.users),console.log("Document path:","".concat(m.COLLECTIONS.users,"/").concat(e.uid)),console.log("Creating user document in Firestore...");let l=(0,c.H9)(d.db,m.COLLECTIONS.users,e.uid);console.log("Document reference created:",l.path),console.log("About to create document with data:",JSON.stringify(a,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",l.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,c.BN)(l,a),console.log("✅ User document created successfully");let r=await (0,c.x7)(l);if(r.exists())console.log("✅ Document verification successful:",r.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error("Failed to create user profile: ".concat(e.message,". Your account was created but profile setup failed. Please contact support."))}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),h().fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to MyTube!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(r){console.error("Registration error:",r),console.error("Error code:",r.code),console.error("Error message:",r.message),console.error("Full error object:",JSON.stringify(r,null,2));let e="An error occurred during registration";if(r.message.includes("fill in all"))e=r.message;else if(r.message.includes("Name must be"))e=r.message;else if(r.message.includes("valid email"))e=r.message;else if(r.message.includes("valid 10-digit"))e=r.message;else if(r.message.includes("Password must be"))e=r.message;else if(r.message.includes("Passwords do not match"))e=r.message;else if(r.message.includes("email address is already registered"))e=r.message;else if(r.message.includes("mobile number is already registered"))e=r.message;else switch(r.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=r.message||"Registration failed"}h().fire({icon:"error",title:"Registration Failed",text:e})}finally{g(!1)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8",children:(0,s.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(i.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,s.jsx)("p",{className:"text-white/80",children:"Join Instra Global and start earning today"})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:t.name,onChange:y,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:y,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,s.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:t.mobile,onChange:y,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:w?"text":"password",id:"password",name:"password",value:t.password,onChange:y,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>p(!w),className:"password-toggle-btn","aria-label":w?"Hide password":"Show password",children:(0,s.jsx)("i",{className:"fas ".concat(w?"fa-eye-slash":"fa-eye")})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:b?"text":"password",id:"confirmPassword",name:"confirmPassword",value:t.confirmPassword,onChange:y,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!b),className:"password-toggle-btn","aria-label":b?"Hide confirm password":"Show confirm password",children:(0,s.jsx)("i",{className:"fas ".concat(b?"fa-eye-slash":"fa-eye")})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,s.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:t.referralCode,onChange:y,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,s.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center mt-6",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(l(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6681:(e,r,t)=>{"use strict";t.d(r,{Nu:()=>n,hD:()=>i,wC:()=>c});var s=t(2115),o=t(3004),a=t(6104),l=t(12);function i(){let[e,r]=(0,s.useState)(null),[t,i]=(0,s.useState)(!0);(0,s.useEffect)(()=>{try{let e=(0,o.hg)(a.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),r(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let n=async()=>{try{await (0,l.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:n}}function n(){let{user:e,loading:r}=i();return(0,s.useEffect)(()=>{r||e||(window.location.href="/login")},[e,r]),{user:e,loading:r}}function c(){let{user:e,loading:r}=i(),[t,o]=(0,s.useState)(!1),[a,l]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{if(!r&&!e){window.location.href="/admin/login";return}if(e){let r=["<EMAIL>","<EMAIL>"].includes(e.email||"");o(r),l(!1),r||(window.location.href="/login")}},[e,r]),{user:e,loading:r||a,isAdmin:t}}},6766:(e,r,t)=>{"use strict";t.d(r,{default:()=>o.a});var s=t(1469),o=t.n(s)}},e=>{var r=r=>e(e.s=r);e.O(0,[2992,7416,8320,5181,6874,3063,3592,8441,1684,7358],()=>r(3587)),_N_E=e.O()}]);