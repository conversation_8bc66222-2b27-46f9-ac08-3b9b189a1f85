(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>l,_f:()=>n});var r=s(6104),a=s(4752),o=s.n(a);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await o().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),o().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),o().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return l},getImageProps:function(){return n}});let r=s(8229),a=s(8883),o=s(3063),i=r._(s(1193));function n(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let l=o.Image},3621:(e,t,s)=>{Promise.resolve().then(s.bind(s,4622))},4622:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(5155),a=s(2115),o=s(6874),i=s.n(o),n=s(6766),l=s(3004),c=s(6104),d=s(6681),u=s(4752),h=s.n(u);function f(){let{user:e,loading:t}=(0,d.hD)(),[s,o]=(0,a.useState)(""),[u,f]=(0,a.useState)(""),[w,m]=(0,a.useState)(""),[x,g]=(0,a.useState)(""),[p,b]=(0,a.useState)(!1),[v,j]=(0,a.useState)(!0),[y,N]=(0,a.useState)(!1),[k,P]=(0,a.useState)(!1),[S,C]=(0,a.useState)(!1),[_,L]=(0,a.useState)(!1);(0,a.useEffect)(()=>{e&&!t&&(window.location.href="/dashboard")},[e,t]),(0,a.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("oobCode");e?(o(e),T(e)):(j(!1),h().fire({icon:"error",title:"Invalid Reset Link",text:"This password reset link is invalid or has expired. Please request a new one.",confirmButtonText:"Go to Forgot Password"}).then(()=>{window.location.href="/forgot-password"}))},[]);let T=async e=>{try{j(!0);let t=await (0,l.RE)(c.j2,e);f(t),N(!0)}catch(t){console.error("Code verification error:",t);let e="This password reset link is invalid or has expired.";switch(t.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/user-disabled":e="This account has been disabled. Please contact support.";break;case"auth/user-not-found":e="No account found for this reset link. The account may have been deleted."}h().fire({icon:"error",title:"Invalid Reset Link",text:e,confirmButtonText:"Request New Reset Link"}).then(()=>{window.location.href="/forgot-password"})}finally{j(!1)}},E=async e=>{if(e.preventDefault(),!w.trim())return void h().fire({icon:"error",title:"Password Required",text:"Please enter a new password"});if(w.length<6)return void h().fire({icon:"error",title:"Password Too Short",text:"Password must be at least 6 characters long"});if(w!==x)return void h().fire({icon:"error",title:"Passwords Don't Match",text:"Please make sure both passwords match"});b(!0);try{await (0,l.R4)(c.j2,s,w),L(!0),h().fire({icon:"success",title:"Password Reset Successful!",text:"Your password has been updated successfully. You can now login with your new password.",confirmButtonText:"Go to Login",confirmButtonColor:"#3b82f6"}).then(()=>{window.location.href="/login"})}catch(t){console.error("Password reset error:",t);let e="An error occurred while resetting your password";switch(t.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/weak-password":e="Password is too weak. Please choose a stronger password.";break;default:e=t.message||"Failed to reset password"}h().fire({icon:"error",title:"Reset Failed",text:e})}finally{b(!1)}};return t||v?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:v?"Verifying reset link...":"Loading..."})]})}):y?(0,r.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(i(),{href:"/",className:"inline-block",children:(0,r.jsx)(n.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Set New Password"}),(0,r.jsxs)("p",{className:"text-white/80",children:["Enter your new password for ",(0,r.jsx)("span",{className:"font-semibold text-blue-400",children:u})]})]}),(0,r.jsxs)("form",{onSubmit:E,className:"glass-card p-8 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newPassword",className:"block text-white font-medium mb-2",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,r.jsx)("input",{type:k?"text":"password",id:"newPassword",value:w,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter new password",disabled:p}),(0,r.jsx)("button",{type:"button",onClick:()=>P(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,r.jsx)("i",{className:"fas ".concat(k?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,r.jsx)("input",{type:S?"text":"password",id:"confirmPassword",value:x,onChange:e=>g(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Confirm new password",disabled:p}),(0,r.jsx)("button",{type:"button",onClick:()=>C(!S),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,r.jsx)("i",{className:"fas ".concat(S?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsx)("button",{type:"submit",disabled:p,className:"w-full btn-primary flex items-center justify-center",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Updating Password..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-check mr-2"}),"Update Password"]})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)(i(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]})})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("i",{className:"fas fa-times text-red-400 text-2xl"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Invalid Reset Link"}),(0,r.jsx)("p",{className:"text-white/80 mb-6",children:"This password reset link is invalid or has expired."}),(0,r.jsx)(i(),{href:"/forgot-password",className:"btn-primary",children:"Request New Reset Link"})]})})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var r=s(3915),a=s(3004),o=s(5317),i=s(858);let n=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,a.xI)(n),c=(0,o.aU)(n);(0,i.c7)(n)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>l,hD:()=>n,wC:()=>c});var r=s(2115),a=s(3004),o=s(6104),i=s(12);function n(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,a.hg)(o.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let l=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:l}}function l(){let{user:e,loading:t}=n();return(0,r.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=n(),[s,a]=(0,r.useState)(!1),[o,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");a(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||o,isAdmin:s}}},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(1469),a=s.n(r)}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(3621)),_N_E=e.O()}]);