(()=>{var e={};e.id=1561,e.ids=[1561],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6968:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(t,c);let l={children:["",{children:["admin",{children:["fix-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\fix-active-days\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\fix-active-days\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/fix-active-days/page",pathname:"/admin/fix-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12703:(e,t,r)=>{Promise.resolve().then(r.bind(r,43927))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>l,j2:()=>c});var s=r(67989),i=r(63385),o=r(75535),n=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),c=(0,i.xI)(a),l=(0,o.aU)(a);(0,n.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},43927:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\fix-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\fix-active-days\\page.tsx","default")},49655:(e,t,r)=>{Promise.resolve().then(r.bind(r,95293))},51278:(e,t,r)=>{"use strict";r.d(t,{M4:()=>a,_f:()=>n});var s=r(33784),i=r(77567);function o(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e,t="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&o(e),await s.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function a(e,t="/login"){try{e&&o(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>n,hD:()=>o,wC:()=>a});var s=r(43210);r(63385),r(33784);var i=r(51278);function o(){let[e,t]=(0,s.useState)(null),[r,o]=(0,s.useState)(!0),n=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:n}}function n(){let{user:e,loading:t}=o();return{user:e,loading:t}}function a(){let{user:e,loading:t}=o(),[r,i]=(0,s.useState)(!1),[n,a]=(0,s.useState)(!0);return{user:e,loading:t||n,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95293:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),i=r(43210),o=r(87979),n=r(3582),a=r(77567);function c(){let{user:e,loading:t}=(0,o.Nu)(),[c,l]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1),[x,p]=(0,i.useState)(null),h=e?.email==="<EMAIL>",m=async()=>{if(!h)return void a.A.fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await a.A.fire({icon:"warning",title:"Fix All Users Active Days",text:"This will recalculate and update active days for all users. This may take a while. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Fix All",cancelButtonText:"Cancel"})).isConfirmed)try{l(!0);let e=await (0,n.gj)();p(e),a.A.fire({icon:"success",title:"Active Days Fixed!",html:`
          <div class="text-left">
            <p><strong>Fixed:</strong> ${e.fixedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
          </div>
        `,timer:5e3})}catch(e){console.error("Error fixing active days:",e),a.A.fire({icon:"error",title:"Error",text:"Failed to fix active days. Check console for details."})}finally{l(!1)}},f=async()=>{if(!h)return void a.A.fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await a.A.fire({icon:"warning",title:"Reset All Daily Video Counts",text:"This will reset today's video count to 0 for all users. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Reset All",cancelButtonText:"Cancel"})).isConfirmed)try{u(!0);let{getDocs:e,collection:t}=await Promise.resolve().then(r.bind(r,75535)),{db:s}=await Promise.resolve().then(r.bind(r,33784)),{COLLECTIONS:i}=await Promise.resolve().then(r.bind(r,3582)),o=await e(t(s,i.users)),c=0,l=0;for(let e of o.docs)try{await (0,n.HY)(e.id),c++}catch(t){console.error(`Error resetting daily count for user ${e.id}:`,t),l++}a.A.fire({icon:"success",title:"Daily Counts Reset!",html:`
          <div class="text-left">
            <p><strong>Reset:</strong> ${c} users</p>
            <p><strong>Errors:</strong> ${l} users</p>
          </div>
        `,timer:5e3})}catch(e){console.error("Error resetting daily counts:",e),a.A.fire({icon:"error",title:"Error",text:"Failed to reset daily counts. Check console for details."})}finally{u(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading..."})]})}):h?(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-white mb-6",children:[(0,s.jsx)("i",{className:"fas fa-tools mr-2"}),"Fix Active Days & Daily Counts"]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,s.jsx)("i",{className:"fas fa-calendar-check mr-2"}),"Fix Active Days"]}),(0,s.jsx)("p",{className:"text-white/80 mb-4",children:"Recalculates and updates active days for all users based on their plan activation date and leave history."}),(0,s.jsx)("button",{onClick:m,disabled:c,className:`btn-primary ${c?"opacity-50 cursor-not-allowed":""}`,children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Fixing Active Days..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-wrench mr-2"}),"Fix All Users Active Days"]})})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,s.jsx)("i",{className:"fas fa-redo mr-2"}),"Reset Daily Video Counts"]}),(0,s.jsx)("p",{className:"text-white/80 mb-4",children:"Resets today's video count to 0 for all users. Use this if daily counts are showing incorrect values."}),(0,s.jsx)("button",{onClick:f,disabled:d,className:`btn-secondary ${d?"opacity-50 cursor-not-allowed":""}`,children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Resetting Daily Counts..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Reset All Daily Counts"]})})]}),x&&(0,s.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-green-300 mb-2",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Last Operation Results"]}),(0,s.jsxs)("div",{className:"text-white",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Fixed:"})," ",x.fixedCount," users"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Errors:"})," ",x.errorCount," users"]})]})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("a",{href:"/admin",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin Dashboard"]})})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-white mb-4",children:"Only admin can access this page."}),(0,s.jsx)("a",{href:"/admin",className:"btn-primary",children:"Back to Admin"})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,5901,3582],()=>r(6968));module.exports=s})();