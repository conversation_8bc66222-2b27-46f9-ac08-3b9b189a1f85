(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>l,_f:()=>o});var a=s(6104),r=s(4752),i=s.n(r);function n(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await a.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&n(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},699:(e,t,s)=>{Promise.resolve().then(s.bind(s,9028))},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return l},getImageProps:function(){return o}});let a=s(8229),r=s(8883),i=s(3063),n=a._(s(1193));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let l=i.Image},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var a=s(3915),r=s(3004),i=s(5317),n=s(858);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,r.xI)(o),c=(0,i.aU)(o);(0,n.c7)(o)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>l,hD:()=>o,wC:()=>c});var a=s(2115),r=s(3004),i=s(6104),n=s(12);function o(){let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,r.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let l=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:l}}function l(){let{user:e,loading:t}=o();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[s,r]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:s}}},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var a=s(1469),r=s.n(a)},9028:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(5155),r=s(2115),i=s(6874),n=s.n(i),o=s(6766),l=s(3004),c=s(6104),d=s(6681),u=s(4752),m=s.n(u);function h(){let{user:e,loading:t}=(0,d.hD)(),[s,i]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[f,x]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&!t&&(window.location.href="/dashboard")},[e,t]);let g=async e=>{if(e.preventDefault(),!s.trim())return void m().fire({icon:"error",title:"Email Required",text:"Please enter your email address"});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return void m().fire({icon:"error",title:"Invalid Email",text:"Please enter a valid email address"});h(!0);try{await (0,l.J1)(c.j2,s.trim().toLowerCase(),{url:"".concat(window.location.origin,"/login"),handleCodeInApp:!1}),x(!0),m().fire({icon:"success",title:"Reset Email Sent!",html:'\n          <p>We\'ve sent a password reset link to:</p>\n          <p class="font-semibold text-blue-600">'.concat(s,'</p>\n          <p class="mt-4 text-sm text-gray-600">\n            Please check your email and click the link to reset your password.\n            If you don\'t see the email, check your spam folder.\n          </p>\n        '),confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})}catch(t){console.error("Password reset error:",t);let e="An error occurred while sending the reset email";switch(t.code){case"auth/user-not-found":e="No account found with this email address. Please check your email or create a new account.";break;case"auth/invalid-email":e="Invalid email address format";break;case"auth/too-many-requests":e="Too many reset attempts. Please wait a few minutes before trying again.";break;case"auth/network-request-failed":e="Network error. Please check your internet connection and try again.";break;default:e=t.message||"Failed to send reset email"}m().fire({icon:"error",title:"Reset Failed",text:e})}finally{h(!1)}};return t?(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,a.jsx)("div",{className:"spinner"}),(0,a.jsx)("p",{className:"text-white mt-4",children:"Loading..."})]}):(0,a.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(n(),{href:"/",className:"inline-block",children:(0,a.jsx)(o.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Reset Password"}),(0,a.jsx)("p",{className:"text-white/80",children:f?"Check your email for reset instructions":"Enter your email to receive a password reset link"})]}),f?(0,a.jsxs)("div",{className:"glass-card p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"fas fa-check text-green-400 text-2xl"})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Email Sent!"}),(0,a.jsxs)("p",{className:"text-white/80 mb-6",children:["We've sent a password reset link to ",(0,a.jsx)("span",{className:"font-semibold text-blue-400",children:s})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("button",{onClick:()=>{x(!1),i("")},className:"w-full btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-redo mr-2"}),"Send to Different Email"]})})]}):(0,a.jsxs)("form",{onSubmit:g,className:"glass-card p-8 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("i",{className:"fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,a.jsx)("input",{type:"email",id:"email",value:s,onChange:e=>i(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter your email address",disabled:u})]})]}),(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full btn-primary flex items-center justify-center",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Sending Reset Email..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Reset Email"]})})]}),(0,a.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,a.jsxs)(n(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]}),(0,a.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,a.jsx)(n(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)("div",{className:"glass-card p-4",children:[(0,a.jsxs)("h4",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"Need Help?"]}),(0,a.jsx)("p",{className:"text-white/60 text-sm mb-3",children:"If you don't receive the email within a few minutes:"}),(0,a.jsxs)("ul",{className:"text-white/60 text-sm space-y-1 text-left",children:[(0,a.jsx)("li",{children:"• Check your spam/junk folder"}),(0,a.jsx)("li",{children:"• Make sure you entered the correct email"}),(0,a.jsx)("li",{children:"• Wait a few minutes and try again"}),(0,a.jsx)("li",{children:"• Contact support if the problem persists"})]})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(699)),_N_E=e.O()}]);