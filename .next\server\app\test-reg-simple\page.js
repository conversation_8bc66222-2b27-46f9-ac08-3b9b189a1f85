(()=>{var e={};e.id=6669,e.ids=[6669],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>u,j2:()=>l});var s=r(67989),i=r(63385),a=r(75535),o=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,i.xI)(n),u=(0,a.aU)(n);(0,o.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34032:(e,t,r)=>{Promise.resolve().then(r.bind(r,80079))},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},43916:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let u={children:["",{children:["test-reg-simple",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80079)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-reg-simple\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-reg-simple\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-reg-simple/page",pathname:"/test-reg-simple",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68880:(e,t,r)=>{Promise.resolve().then(r.bind(r,92325))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-reg-simple\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-reg-simple\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),i=r(43210),a=r(63385),o=r(75535),n=r(33784);function l(){let[e,t]=(0,i.useState)(""),[r,l]=(0,i.useState)(!1),u=e=>{t(t=>t+e+"\n")},c=async()=>{t(""),l(!0);let e=null;try{u("\uD83E\uDDEA Testing Simple Registration Process...\n"),u("=== STEP 1: Creating Firebase Auth User ===");let t=`test${Date.now()}@example.com`;e=(await (0,a.eJ)(n.j2,t,"test123456")).user,u(`✅ Auth user created: ${e.uid}`),u(`   Email: ${e.email}`),u("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,2e3)),u(`Current auth user: ${n.j2.currentUser?.uid}`),u(`Auth state matches: ${n.j2.currentUser?.uid===e.uid}`),u("\n=== STEP 3: Creating User Document ===");let r=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),i=`MY${r}${s}`;u(`Generated referral code: ${i}`);let l={name:"Test User",email:t.toLowerCase(),mobile:"9876543210",referralCode:i,referredBy:"",referralBonusCredited:!1,plan:"Trial",planExpiry:null,activeDays:1,joinedDate:o.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,videoDuration:30,status:"active"};u(`Document path: users/${e.uid}`),u(`Data fields: ${Object.keys(l).length}`),u("\n=== STEP 4: Creating Document ===");let c=(0,o.H9)(n.db,"users",e.uid);u("Attempting setDoc..."),await (0,o.BN)(c,l),u("✅ setDoc completed successfully"),u("\n=== STEP 5: Verifying Document ===");let d=await (0,o.x7)(c);if(d.exists()){let e=d.data();u("✅ Document verification successful"),u(`   Name: ${e.name}`),u(`   Email: ${e.email}`),u(`   Plan: ${e.plan}`),u(`   Referral Code: ${e.referralCode}`),u(`   Wallet: ${e.wallet}`),u(`   Fields count: ${Object.keys(e).length}`),u("\n\uD83C\uDF89 SUCCESS: Registration process works perfectly!"),u("The issue might be in the registration form logic or error handling.")}else u("❌ Document not found after creation"),u("This indicates a serious Firestore issue")}catch(e){u(`❌ Test failed: ${e.message}`),u(`   Error code: ${e.code}`),u(`   Error name: ${e.name}`),"permission-denied"===e.code?(u("\n\uD83D\uDD27 PERMISSION DENIED ANALYSIS:"),u("   - Firestore security rules are blocking the write"),u("   - Check if user authentication is properly recognized"),u("   - Verify rules allow authenticated users to create documents")):"unavailable"===e.code?(u("\n\uD83D\uDD27 FIRESTORE UNAVAILABLE:"),u("   - Check internet connection"),u("   - Verify Firestore is enabled in Firebase console")):"auth/email-already-in-use"===e.code&&(u("\n\uD83D\uDD27 EMAIL ALREADY IN USE:"),u("   - This is expected if testing multiple times"),u("   - Try with a different email or wait a moment")),u(`
   Full error details:`),u(`   ${JSON.stringify(e,null,2)}`)}finally{if(e)try{u("\n=== CLEANUP ==="),await (0,a.hG)(e),u("✅ Test user deleted")}catch(e){u(`⚠️ User deletion failed: ${e.message}`)}try{await (0,a.CI)(n.j2),u("✅ Signed out")}catch(e){u(`⚠️ Sign out failed: ${e.message}`)}l(!1)}},d=async()=>{t(""),l(!0);try{u("\uD83D\uDD27 Testing Firebase Basics...\n"),u("=== TEST 1: Firebase Instances ==="),u(`Auth: ${n.j2?"✅ Initialized":"❌ Not initialized"}`),u(`Firestore: ${n.db?"✅ Initialized":"❌ Not initialized"}`),u(`Current user: ${n.j2.currentUser?.uid||"None"}`),u("\n=== TEST 2: Basic Firestore Write ===");let e=(0,o.H9)(n.db,"test_basic",`test_${Date.now()}`);await (0,o.BN)(e,{test:!0,timestamp:o.Dc.now()}),u("✅ Basic write successful"),u("\n=== TEST 3: Basic Firestore Read ===");let t=await (0,o.x7)(e);t.exists()?(u("✅ Basic read successful"),u(`   Data: ${JSON.stringify(t.data())}`)):u("❌ Basic read failed"),u("\n✅ Firebase basics are working correctly")}catch(e){u(`❌ Firebase basics test failed: ${e.message}`),u(`   Error code: ${e.code}`)}finally{l(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Simple Registration"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,s.jsx)("button",{onClick:d,disabled:r,className:"btn-primary",children:r?"Testing...":"Test Firebase Basics"}),(0,s.jsx)("button",{onClick:c,disabled:r,className:"btn-primary",children:r?"Testing...":"Test Registration Process"})]}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||"Click a test button to start..."})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,5901],()=>r(43916));module.exports=s})();