'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useRequireAdmin } from '@/hooks/useAuth'
import { uploadUsersFromCSV, uploadUsersFromJSON, validateUserData } from '@/lib/userMigrationService'
import Swal from 'sweetalert2'

interface UploadResult {
  success: number
  failed: number
  errors: string[]
  duplicates: number
}

export default function AdminUploadUsersPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const router = useRouter()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadFormat, setUploadFormat] = useState<'csv' | 'json'>('csv')
  const [previewData, setPreviewData] = useState<any[]>([])
  const [showPreview, setShowPreview] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<string>('')

  // Prevent navigation during upload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isUploading) {
        e.preventDefault()
        return 'Upload is in progress. Are you sure you want to leave?'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [isUploading])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setPreviewData([])
      setShowPreview(false)
      setUploadResult(null)
    }
  }

  const handlePreviewFile = async () => {
    if (!selectedFile) return

    try {
      setIsUploading(true)
      const text = await selectedFile.text()
      
      let data: any[] = []
      
      if (uploadFormat === 'csv') {
        // Parse CSV/TSV - detect delimiter
        const lines = text.split('\n').filter(line => line.trim())
        if (lines.length < 2) {
          throw new Error('CSV file must have at least a header row and one data row')
        }

        // Detect delimiter (comma or tab)
        const firstLine = lines[0]
        const delimiter = firstLine.includes('\t') ? '\t' : ','

        const headers = firstLine.split(delimiter).map(h => h.trim().replace(/"/g, ''))
        data = lines.slice(1).map(line => {
          const values = line.split(delimiter).map(v => v.trim().replace(/"/g, ''))
          const obj: any = {}
          headers.forEach((header, index) => {
            obj[header] = values[index] || ''
          })
          return obj
        })
      } else {
        // Parse JSON
        data = JSON.parse(text)
        if (!Array.isArray(data)) {
          throw new Error('JSON file must contain an array of user objects')
        }
      }

      // Validate first few records
      const preview = data.slice(0, 5)
      const validationErrors: string[] = []
      
      preview.forEach((userData, index) => {
        const errors = validateUserData(userData)
        if (errors.length > 0) {
          validationErrors.push(`Row ${index + 1}: ${errors.join(', ')}`)
        }
      })

      setPreviewData(preview)
      setShowPreview(true)

      if (validationErrors.length > 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Validation Issues Found',
          html: `<div class="text-left"><p>Issues found in preview data:</p><ul>${validationErrors.map(error => `<li>${error}</li>`).join('')}</ul></div>`,
          confirmButtonText: 'Continue Anyway',
          showCancelButton: true,
          cancelButtonText: 'Fix Data First'
        })
      }

    } catch (error: any) {
      console.error('Error previewing file:', error)
      Swal.fire({
        icon: 'error',
        title: 'Preview Failed',
        text: error.message || 'Failed to preview file. Please check the format.',
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleUploadUsers = async () => {
    if (!selectedFile) return

    const result = await Swal.fire({
      icon: 'question',
      title: 'Confirm User Upload',
      html: `
        <div class="text-left">
          <p><strong>Are you sure you want to upload users from this file?</strong></p>
          <br>
          <p>This will:</p>
          <ul>
            <li>Create Firebase Authentication accounts</li>
            <li>Create user documents in Firestore</li>
            <li>Use provided referral codes or assign new sequential ones</li>
            <li>Set up wallet and transaction data</li>
            <li>Apply quick video advantage if specified</li>
            <li>Validate referral code uniqueness</li>
          </ul>
          <br>
          <p class="text-red-600"><strong>Warning:</strong> This action cannot be undone!</p>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Yes, Upload Users',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#dc2626'
    })

    if (!result.isConfirmed) return

    try {
      setIsUploading(true)
      setUploadResult(null)
      setUploadProgress('Starting upload...')

      // Show progress modal
      Swal.fire({
        title: 'Uploading Users',
        html: `
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p id="upload-progress">Starting upload...</p>
            <p class="text-sm text-gray-600 mt-2">Please do not close this page or navigate away.</p>
          </div>
        `,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
          // Update progress in the modal
          const updateProgress = (message: string) => {
            const progressElement = document.getElementById('upload-progress')
            if (progressElement) {
              progressElement.textContent = message
            }
          }

          // Store the update function globally so we can access it
          ;(window as any).updateUploadProgress = updateProgress
        }
      })

      let result: UploadResult

      if (uploadFormat === 'csv') {
        result = await uploadUsersFromCSV(selectedFile)
      } else {
        result = await uploadUsersFromJSON(selectedFile)
      }

      // Close progress modal
      Swal.close()
      setUploadResult(result)

      // Show success/failure summary
      if (result.success > 0) {
        Swal.fire({
          icon: result.failed > 0 ? 'warning' : 'success',
          title: 'Upload Complete',
          html: `
            <div class="text-left">
              <p><strong>Upload Summary:</strong></p>
              <ul>
                <li class="text-green-600">✓ Successfully created: ${result.success} users</li>
                ${result.duplicates > 0 ? `<li class="text-yellow-600">⚠ Skipped duplicates: ${result.duplicates} users</li>` : ''}
                ${result.failed > 0 ? `<li class="text-red-600">✗ Failed: ${result.failed} users</li>` : ''}
              </ul>
              ${result.errors.length > 0 ? `<br><p><strong>Errors:</strong></p><ul>${result.errors.slice(0, 5).map(error => `<li class="text-red-600">${error}</li>`).join('')}</ul>` : ''}
            </div>
          `,
          timer: result.failed > 0 ? undefined : 5000,
          showConfirmButton: result.failed > 0
        })
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Upload Failed',
          text: 'No users were successfully created. Please check your data and try again.',
        })
      }

    } catch (error: any) {
      console.error('Error uploading users:', error)
      Swal.fire({
        icon: 'error',
        title: 'Upload Failed',
        text: error.message || 'Failed to upload users. Please try again.',
      })
    } finally {
      setIsUploading(false)
    }
  }

  const downloadSampleCSV = () => {
    const sampleData = [
      'name,email,mobile,password,plan,activeDays,wallet,totalVideos,referredBy,referralCode,quickVideoAdvantage,quickVideoAdvantageDays,quickVideoAdvantageSeconds,quickVideoAdvantageGrantedBy',
      'John Doe,<EMAIL>,9876543210,password123,Basic,30,2000,100,MYN0001,MYN1001,true,7,10,<EMAIL>',
      'Jane Smith,<EMAIL>,9876543211,password456,Premium,25,5000,150,MYN0002,MYN1002,false,,,,,',
      'Mike Johnson,<EMAIL>,9876543212,password789,Starter,30,1000,50,,MYN1003,true,14,30,<EMAIL>',
      'Sarah Wilson,<EMAIL>,9876543213,password321,Gold,20,8000,200,MYN0001,MYN1004,true,3,1,<EMAIL>'
    ].join('\n')

    const blob = new Blob([sampleData], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sample-users.csv'
    a.click()
    URL.revokeObjectURL(url)
  }

  const downloadSampleJSON = () => {
    const sampleData = [
      {
        name: "John Doe",
        email: "<EMAIL>",
        mobile: "9876543210",
        password: "password123",
        plan: "Basic",
        activeDays: 30,
        wallet: 2000,
        totalTranslations: 100,
        referredBy: "TN0001",
        referralCode: "TN1001",
        quickTranslationAdvantage: true,
        quickTranslationAdvantageDays: 7,
        quickTranslationAdvantageSeconds: 10,
        quickTranslationAdvantageGrantedBy: "<EMAIL>"
      },
      {
        name: "Jane Smith",
        email: "<EMAIL>",
        mobile: "9876543211",
        password: "password456",
        plan: "Premium",
        activeDays: 25,
        wallet: 5000,
        totalTranslations: 150,
        referredBy: "TN0002",
        referralCode: "TN1002",
        quickTranslationAdvantage: false
      },
      {
        name: "Mike Johnson",
        email: "<EMAIL>",
        mobile: "9876543212",
        password: "password789",
        plan: "Starter",
        activeDays: 30,
        wallet: 1000,
        totalVideos: 50,
        referralCode: "MYN1003",
        quickVideoAdvantage: true,
        quickVideoAdvantageDays: 14,
        quickVideoAdvantageSeconds: 30,
        quickVideoAdvantageGrantedBy: "<EMAIL>"
      },
      {
        name: "Sarah Wilson",
        email: "<EMAIL>",
        mobile: "9876543213",
        password: "password321",
        plan: "Gold",
        activeDays: 20,
        wallet: 8000,
        totalVideos: 200,
        referredBy: "MYN0001",
        referralCode: "MYN1004",
        quickVideoAdvantage: true,
        quickVideoAdvantageDays: 3,
        quickVideoAdvantageSeconds: 1,
        quickVideoAdvantageGrantedBy: "<EMAIL>"
      }
    ]

    const blob = new Blob([JSON.stringify(sampleData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sample-users.json'
    a.click()
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Upload Users</h1>
            <p className="text-white/80">Transfer existing users from old platform</p>
          </div>
          {isUploading ? (
            <button
              disabled
              className="btn-secondary opacity-50 cursor-not-allowed"
              title="Upload in progress - navigation disabled"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Users
            </button>
          ) : (
            <Link
              href="/admin/users"
              className="btn-secondary"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Users
            </Link>
          )}
        </div>

        {/* Upload Form */}
        <div className="glass-card p-6 mb-6">
          <h2 className="text-xl font-bold text-white mb-4">
            <i className="fas fa-upload mr-2"></i>
            Upload User Data
          </h2>

          {/* Format Selection */}
          <div className="mb-4">
            <label className="block text-white font-medium mb-2">Upload Format</label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="csv"
                  checked={uploadFormat === 'csv'}
                  onChange={(e) => setUploadFormat(e.target.value as 'csv' | 'json')}
                  className="mr-2"
                />
                <span className="text-white">CSV/TSV File</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="json"
                  checked={uploadFormat === 'json'}
                  onChange={(e) => setUploadFormat(e.target.value as 'csv' | 'json')}
                  className="mr-2"
                />
                <span className="text-white">JSON File</span>
              </label>
            </div>
          </div>

          {/* Sample Downloads */}
          <div className="mb-4">
            <label className="block text-white font-medium mb-2">Sample Files</label>
            <div className="flex space-x-4">
              <button
                onClick={downloadSampleCSV}
                className="btn-secondary text-sm"
              >
                <i className="fas fa-download mr-2"></i>
                Download Sample CSV
              </button>
              <button
                onClick={downloadSampleJSON}
                className="btn-secondary text-sm"
              >
                <i className="fas fa-download mr-2"></i>
                Download Sample JSON
              </button>
            </div>
          </div>

          {/* File Upload */}
          <div className="mb-4">
            <label className="block text-white font-medium mb-2">Select File</label>
            <input
              type="file"
              accept={uploadFormat === 'csv' ? '.csv,.tsv,.txt' : '.json'}
              onChange={handleFileSelect}
              className="form-input"
            />
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex space-x-4">
              <button
                onClick={handlePreviewFile}
                disabled={!selectedFile || isUploading}
                className="btn-secondary"
              >
                {isUploading ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-eye mr-2"></i>
                    Preview Data
                  </>
                )}
              </button>

              <button
                onClick={handleUploadUsers}
                disabled={!selectedFile || isUploading || !showPreview}
                className="btn-primary"
              >
                {isUploading ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4"></div>
                    Uploading...
                  </>
                ) : (
                  <>
                    <i className="fas fa-upload mr-2"></i>
                    Upload Users
                  </>
                )}
              </button>
            </div>

            {isUploading && (
              <div className="bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-3">
                <div className="flex items-center text-yellow-300">
                  <i className="fas fa-exclamation-triangle mr-2"></i>
                  <span className="text-sm">
                    <strong>Upload in progress!</strong> Please do not close this page or navigate away until the upload is complete.
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Data Preview */}
        {showPreview && previewData.length > 0 && (
          <div className="glass-card p-6 mb-6">
            <h2 className="text-xl font-bold text-white mb-4">
              <i className="fas fa-table mr-2"></i>
              Data Preview (First 5 Records)
            </h2>

            <div className="overflow-x-auto">
              <table className="w-full text-white">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left p-2">Name</th>
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">Mobile</th>
                    <th className="text-left p-2">Plan</th>
                    <th className="text-left p-2">Active Days</th>
                    <th className="text-left p-2">Wallet</th>
                    <th className="text-left p-2">Total Videos</th>
                  </tr>
                </thead>
                <tbody>
                  {previewData.map((user, index) => (
                    <tr key={index} className="border-b border-white/10">
                      <td className="p-2">{user.name || 'N/A'}</td>
                      <td className="p-2">{user.email || 'N/A'}</td>
                      <td className="p-2">{user.mobile || 'N/A'}</td>
                      <td className="p-2">{user.plan || 'Trial'}</td>
                      <td className="p-2">{user.activeDays || 0}</td>
                      <td className="p-2">₹{user.wallet || 0}</td>
                      <td className="p-2">{user.totalVideos || 0}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Upload Result */}
        {uploadResult && (
          <div className="glass-card p-6 mb-6">
            <h2 className="text-xl font-bold text-white mb-4">
              <i className="fas fa-chart-bar mr-2"></i>
              Upload Results
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                <div className="text-green-400 text-2xl font-bold">{uploadResult.success}</div>
                <div className="text-green-300 text-sm">Successfully Created</div>
              </div>

              {uploadResult.duplicates > 0 && (
                <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
                  <div className="text-yellow-400 text-2xl font-bold">{uploadResult.duplicates}</div>
                  <div className="text-yellow-300 text-sm">Skipped (Duplicates)</div>
                </div>
              )}

              {uploadResult.failed > 0 && (
                <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
                  <div className="text-red-400 text-2xl font-bold">{uploadResult.failed}</div>
                  <div className="text-red-300 text-sm">Failed</div>
                </div>
              )}
            </div>

            {uploadResult.errors.length > 0 && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                <h3 className="text-red-400 font-bold mb-2">Errors:</h3>
                <ul className="text-red-300 text-sm space-y-1">
                  {uploadResult.errors.slice(0, 10).map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                  {uploadResult.errors.length > 10 && (
                    <li className="text-red-400">... and {uploadResult.errors.length - 10} more errors</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="glass-card p-6">
          <h2 className="text-xl font-bold text-white mb-4">
            <i className="fas fa-info-circle mr-2"></i>
            Upload Instructions
          </h2>

          <div className="text-white/80 space-y-4">
            <div>
              <h3 className="font-bold text-white mb-2">Required Fields:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li><strong>name:</strong> User's full name</li>
                <li><strong>email:</strong> Valid email address (must be unique)</li>
                <li><strong>mobile:</strong> 10-digit mobile number (must be unique)</li>
                <li><strong>password:</strong> Password for the user account (min 6 characters)</li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-white mb-2">Optional Fields:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li><strong>plan:</strong> User's plan (Trial, Starter, Basic, Premium, Gold, Platinum, Diamond) - defaults to Trial</li>
                <li><strong>activeDays:</strong> Number of active days remaining - defaults to 1</li>
                <li><strong>wallet:</strong> Wallet balance in rupees - defaults to 0</li>
                <li><strong>totalVideos:</strong> Total videos watched - defaults to 0</li>
                <li><strong>referredBy:</strong> Referral code of the person who referred this user</li>
                <li><strong>referralCode:</strong> User's own referral code (MYN0001, MYN0002, etc.) - auto-generated if not provided</li>
                <li><strong>quickVideoAdvantage:</strong> Whether user has quick video advantage (true/false) - defaults to false</li>
                <li><strong>quickVideoAdvantageDays:</strong> Number of days for quick advantage (1-365) - only if quickVideoAdvantage is true</li>
                <li><strong>quickVideoAdvantageSeconds:</strong> Video duration in seconds during advantage (1-420) - defaults to 30</li>
                <li><strong>quickVideoAdvantageGrantedBy:</strong> Admin who granted the advantage - optional</li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-white mb-2">Important Notes:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>Each user will get a sequential referral code (MYN0001, MYN0002, etc.)</li>
                <li>Firebase Authentication accounts will be created automatically</li>
                <li>Duplicate emails or mobile numbers will be skipped</li>
                <li>Users can login immediately with their email and password</li>
                <li>All wallet balances and video counts will be preserved</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
