(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6174],{8811:(e,s,a)=>{Promise.resolve().then(a.bind(a,9357))},9357:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(5155),l=a(2115),i=a(6874),n=a.n(i),r=a(6681),o=a(7460),d=a(8647);function c(){let e=function(){let e=new Date,s=e.getHours(),a=e.getDay();if(!(a>=1&&a<=5)){let s=new Date(e);return s.setDate(e.getDate()+(0===a?1:8-a)),s.setHours(9,0,0,0),{isAvailable:!1,message:"Support is available Monday to Friday, 9 AM - 6 PM",nextAvailableTime:"Next available: ".concat(s.toLocaleDateString()," at 9:00 AM")}}if(!(s>=9&&s<18))if(s<9)return new Date(e).setHours(9,0,0,0),{isAvailable:!1,message:"Support hours: 9 AM - 6 PM (Working days)",nextAvailableTime:"Available today at 9:00 AM"};else{let s=new Date(e);s.setDate(e.getDate()+1);let a=s.getDay();if(a>=1&&a<=5)return s.setHours(9,0,0,0),{isAvailable:!1,message:"Support hours: 9 AM - 6 PM (Working days)",nextAvailableTime:"Next available: ".concat(s.toLocaleDateString()," at 9:00 AM")};{let e=new Date(s);return e.setDate(s.getDate()+(0===a?1:8-a)),e.setHours(9,0,0,0),{isAvailable:!1,message:"Support is available Monday to Friday, 9 AM - 6 PM",nextAvailableTime:"Next available: ".concat(e.toLocaleDateString()," at 9:00 AM")}}}return{isAvailable:!0,message:"Support is currently available! We typically respond within minutes."}}();return{status:e.isAvailable?"online":"offline",message:e.message,nextAvailable:e.nextAvailableTime,hoursInfo:"Monday to Friday, 9:00 AM - 6:00 PM"}}function m(){let{user:e,loading:s}=(0,r.Nu)(),{hasBlockingNotifications:a,isChecking:i,markAllAsRead:m}=(0,o.J)((null==e?void 0:e.uid)||null),[x,h]=(0,l.useState)(c());return((0,l.useEffect)(()=>{let e=setInterval(()=>{h(c())},6e4);return()=>clearInterval(e)},[]),s||i)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:s?"Loading...":"Checking notifications..."})]})}):a&&e?(0,t.jsx)(d.A,{userId:e.uid,onAllRead:m}):(0,t.jsxs)("div",{className:"min-h-screen p-4",children:[(0,t.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"Support & Help"}),(0,t.jsx)("div",{className:"w-24"})," "]})}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[(0,t.jsx)("i",{className:"fas fa-headset mr-2"}),"Contact Support"]}),(0,t.jsxs)("div",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("online"===x.status?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-red-500/20 text-red-400 border border-red-500/30"),children:[(0,t.jsx)("i",{className:"fas fa-circle mr-2 ".concat("online"===x.status?"text-green-400":"text-red-400")}),"online"===x.status?"Online":"Offline"]})]}),(0,t.jsxs)("div",{className:"p-4 rounded-lg mb-6 ".concat("online"===x.status?"bg-green-500/10 border border-green-500/20":"bg-orange-500/10 border border-orange-500/20"),children:[(0,t.jsxs)("p",{className:"font-medium mb-2 ".concat("online"===x.status?"text-green-400":"text-orange-400"),children:[(0,t.jsx)("i",{className:"fas ".concat("online"===x.status?"fa-check-circle":"fa-clock"," mr-2")}),x.message]}),x.nextAvailable&&(0,t.jsx)("p",{className:"text-white/60 text-sm",children:x.nextAvailable}),(0,t.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:[(0,t.jsx)("i",{className:"fas fa-calendar mr-2"}),x.hoursInfo]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("i",{className:"fab fa-whatsapp text-green-400 text-3xl mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-bold text-lg",children:"WhatsApp Support"}),(0,t.jsx)("p",{className:"text-green-300 text-sm",children:"Instant messaging support"})]})]}),(0,t.jsx)("p",{className:"text-white/80 mb-4",children:"Get instant help via WhatsApp during business hours. Our team responds quickly to your queries on working days."}),(0,t.jsxs)("div",{className:"mb-3 p-2 rounded text-sm ".concat("online"===x.status?"bg-green-500/20 text-green-400":"bg-orange-500/20 text-orange-400"),children:[(0,t.jsx)("i",{className:"fas ".concat("online"===x.status?"fa-check":"fa-clock"," mr-2")}),"online"===x.status?"Available now - Usually responds within minutes":"Currently offline - Will respond when available"]}),(0,t.jsxs)("a",{href:"https://wa.me/917676636990",target:"_blank",rel:"noopener noreferrer",className:"btn-success bg-green-500 hover:bg-green-600 w-full",children:[(0,t.jsx)("i",{className:"fab fa-whatsapp mr-2"}),"Chat on WhatsApp: +91 7676636990"]})]}),(0,t.jsxs)("div",{className:"bg-blue-500/20 border border-blue-500/30 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("i",{className:"fas fa-envelope text-blue-400 text-3xl mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-bold text-lg",children:"Email Support"}),(0,t.jsx)("p",{className:"text-blue-300 text-sm",children:"Detailed support via email"})]})]}),(0,t.jsx)("p",{className:"text-white/80 mb-4",children:"Send us detailed queries and we'll respond within 24 hours on working days."}),(0,t.jsxs)("div",{className:"mb-3 p-2 rounded text-sm ".concat("online"===x.status?"bg-blue-500/20 text-blue-400":"bg-orange-500/20 text-orange-400"),children:[(0,t.jsx)("i",{className:"fas ".concat("online"===x.status?"fa-check":"fa-clock"," mr-2")}),"online"===x.status?"Available now - Response within 24 hours":"Currently offline - Will respond on next working day"]}),(0,t.jsxs)("a",{href:"mailto:<EMAIL>",className:"btn-primary bg-blue-500 hover:bg-blue-600 w-full",children:[(0,t.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email: <EMAIL>"]})]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-white mb-6",children:[(0,t.jsx)("i",{className:"fas fa-question-circle mr-2"}),"Frequently Asked Questions"]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,t.jsx)("i",{className:"fas fa-play-circle mr-2 text-youtube-red"}),"How do I earn money by watching videos?"]}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"Watch videos completely to earn money. You earn ₹10-400 per batch of 50 videos depending on your plan. Videos must be watched for the full duration to count towards your earnings."})]}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,t.jsx)("i",{className:"fas fa-wallet mr-2 text-green-400"}),"When can I withdraw my earnings?"]}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"Withdrawals are available between 10:00 AM to 6:00 PM on non-leave days. Minimum withdrawal is ₹50. Trial users cannot withdraw - upgrade to a paid plan first."})]}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,t.jsx)("i",{className:"fas fa-users mr-2 text-blue-400"}),"How does the referral system work?"]}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"Share your referral code with friends. When they join and purchase a plan, you earn a bonus ranging from ₹50 to ₹1200 depending on the plan they choose."})]}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,t.jsx)("i",{className:"fas fa-crown mr-2 text-yellow-400"}),"What are the different plans available?"]}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"We offer Trial (free), Starter (₹499), Basic (₹1499), Premium (₹2999), Gold (₹3999), Platinum (₹5999), and Diamond (₹9999) plans with different earning rates and video durations."})]}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,t.jsx)("i",{className:"fas fa-bolt mr-2 text-orange-400"}),"What is Quick Video Advantage?"]}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"Admins can grant temporary quick video advantage that reduces your video duration to 30 seconds for a limited period, helping you complete tasks faster."})]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-clock mr-2"}),"Support Hours"]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-2",children:"WhatsApp Support"}),(0,t.jsx)("p",{className:"text-white/80 text-sm mb-1",children:"Monday - Friday: 9:00 AM - 6:00 PM"}),(0,t.jsx)("p",{className:"text-white/60 text-sm mb-1",children:"Working days only"}),(0,t.jsx)("p",{className:"text-green-400 text-sm",children:"Usually responds within minutes during business hours"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Email Support"}),(0,t.jsx)("p",{className:"text-white/80 text-sm mb-1",children:"Monday - Friday: 9:00 AM - 6:00 PM"}),(0,t.jsx)("p",{className:"text-white/60 text-sm mb-1",children:"Working days only"}),(0,t.jsx)("p",{className:"text-blue-400 text-sm",children:"Response within 24 hours on working days"})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3592,1018,8441,1684,7358],()=>s(8811)),_N_E=e.O()}]);