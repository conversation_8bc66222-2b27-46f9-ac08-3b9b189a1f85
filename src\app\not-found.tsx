import Link from 'next/link'
import Image from 'next/image'

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="text-center">
        <div className="mb-8">
          <Image
            src="/img/instra-logo.svg"
            alt="Instra Global Logo"
            width={80}
            height={80}
            className="mx-auto mb-4"
          />
          <h1 className="text-6xl font-bold text-white mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-white mb-2">Page Not Found</h2>
          <p className="text-white/80 mb-8 max-w-md mx-auto">
            The page you're looking for doesn't exist or has been moved.
          </p>

          {/* Support Contact */}
          <div className="mb-8">
            <p className="text-white/60 mb-4">Need help finding what you're looking for?</p>
            <div className="flex justify-center">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <i className="fas fa-envelope mr-2"></i>
                Email Support
              </a>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/"
            className="btn-primary inline-flex items-center"
          >
            <i className="fas fa-home mr-2"></i>
            Go Home
          </Link>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/dashboard"
              className="btn-secondary inline-flex items-center"
            >
              <i className="fas fa-tachometer-alt mr-2"></i>
              Dashboard
            </Link>
            <Link
              href="/work"
              className="btn-secondary inline-flex items-center"
            >
              <i className="fas fa-play-circle mr-2"></i>
              Watch Videos
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
