'use client'

import { useState, useEffect } from 'react'
import { 
  loadTranslationsFromFile, 
  initializeTranslationSystem, 
  getTranslationStats, 
  clearTranslationStorage,
  TranslationData 
} from '@/lib/translationManager'

export default function TestTranslationsPage() {
  const [translations, setTranslations] = useState<TranslationData[]>([])
  const [stats, setStats] = useState({
    totalTranslations: 0,
    currentBatch: 0,
    totalBatches: 0,
    translationsInCurrentBatch: 0
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateStats = () => {
    const currentStats = getTranslationStats()
    setStats(currentStats)
  }

  const handleLoadFromFile = async () => {
    setLoading(true)
    setError(null)
    try {
      const loadedTranslations = await loadTranslationsFromFile()
      setTranslations(loadedTranslations.slice(0, 10)) // Show first 10 for testing
      updateStats()
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleInitializeSystem = async () => {
    setLoading(true)
    setError(null)
    try {
      const systemTranslations = await initializeTranslationSystem()
      setTranslations(systemTranslations.slice(0, 10)) // Show first 10 for testing
      updateStats()
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleClearStorage = () => {
    clearTranslationStorage()
    setTranslations([])
    updateStats()
  }

  useEffect(() => {
    updateStats()
  }, [])

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <h1 className="text-xl font-bold text-white mb-4">Translation System Test</h1>
        
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="bg-white/10 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-400">{stats.totalTranslations}</div>
            <div className="text-white/80 text-sm">Total Translations</div>
          </div>
          <div className="bg-white/10 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-400">{stats.currentBatch}</div>
            <div className="text-white/80 text-sm">Current Batch</div>
          </div>
          <div className="bg-white/10 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-400">{stats.totalBatches}</div>
            <div className="text-white/80 text-sm">Total Batches</div>
          </div>
          <div className="bg-white/10 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-orange-400">{stats.translationsInCurrentBatch}</div>
            <div className="text-white/80 text-sm">In Current Batch</div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap gap-4">
          <button
            onClick={handleLoadFromFile}
            disabled={loading}
            className="btn-primary px-4 py-2 rounded-lg disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Load from File'}
          </button>
          
          <button
            onClick={handleInitializeSystem}
            disabled={loading}
            className="btn-primary px-4 py-2 rounded-lg disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Initialize System'}
          </button>
          
          <button
            onClick={handleClearStorage}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Clear Storage
          </button>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
            <p className="text-red-400">Error: {error}</p>
          </div>
        )}
      </header>

      {/* Translation List */}
      <div className="glass-card p-6">
        <h2 className="text-lg font-bold text-white mb-4">
          Loaded Translations ({translations.length})
        </h2>
        
        {translations.length === 0 ? (
          <p className="text-white/60 text-center py-8">
            No translations loaded. Click a button above to test.
          </p>
        ) : (
          <div className="space-y-4">
            {translations.map((translation, index) => (
              <div key={translation.id} className="bg-white/10 rounded-lg p-4">
                <div className="mb-3">
                  <h3 className="text-white font-medium text-sm mb-2">
                    English Text:
                  </h3>
                  <p className="text-white/90 bg-white/5 p-3 rounded border-l-4 border-blue-400">
                    {translation.english}
                  </p>
                </div>
                
                {/* Show available translations */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {translation.hindi && (
                    <div className="bg-white/5 p-2 rounded">
                      <div className="text-xs text-white/60 mb-1">Hindi:</div>
                      <div className="text-white/80 text-sm">{translation.hindi}</div>
                    </div>
                  )}
                  {translation.spanish && (
                    <div className="bg-white/5 p-2 rounded">
                      <div className="text-xs text-white/60 mb-1">Spanish:</div>
                      <div className="text-white/80 text-sm">{translation.spanish}</div>
                    </div>
                  )}
                  {translation.french && (
                    <div className="bg-white/5 p-2 rounded">
                      <div className="text-xs text-white/60 mb-1">French:</div>
                      <div className="text-white/80 text-sm">{translation.french}</div>
                    </div>
                  )}
                  {translation.german && (
                    <div className="bg-white/5 p-2 rounded">
                      <div className="text-xs text-white/60 mb-1">German:</div>
                      <div className="text-white/80 text-sm">{translation.german}</div>
                    </div>
                  )}
                  {translation.italian && (
                    <div className="bg-white/5 p-2 rounded">
                      <div className="text-xs text-white/60 mb-1">Italian:</div>
                      <div className="text-white/80 text-sm">{translation.italian}</div>
                    </div>
                  )}
                  {translation.portuguese && (
                    <div className="bg-white/5 p-2 rounded">
                      <div className="text-xs text-white/60 mb-1">Portuguese:</div>
                      <div className="text-white/80 text-sm">{translation.portuguese}</div>
                    </div>
                  )}
                </div>
                
                <div className="text-xs text-white/60 mt-3 space-y-1">
                  <p>ID: {translation.id}</p>
                  <p>Category: {translation.category}</p>
                  <p>Batch: {translation.batchIndex}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
