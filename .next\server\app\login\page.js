(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>c,j2:()=>l});var s=t(67989),o=t(63385),i=t(75535),a=t(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,o.xI)(n),c=(0,i.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,r,t)=>{"use strict";t.d(r,{M4:()=>n,_f:()=>a});var s=t(33784),o=t(77567);function i(e){try{Object.keys(localStorage).forEach(r=>{(r.includes(e)||r.startsWith("video_session_")||r.startsWith("watch_times_")||r.startsWith("video_refresh_")||r.startsWith("video_change_notification_")||r.startsWith("leave_")||r.includes("mytube_")||r.includes("user_"))&&localStorage.removeItem(r)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function a(e,r="/login"){try{if((await o.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await s.j2.signOut(),o.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=r}),!0;return!1}catch(e){return console.error("Logout error:",e),o.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e,r="/login"){try{e&&i(e),await s.j2.signOut(),window.location.href=r}catch(e){console.error("Quick logout error:",e),window.location.href=r}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57691:(e,r,t)=>{Promise.resolve().then(t.bind(t,69488))},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),o=t(43210),i=t(85814),a=t.n(i),n=t(30474),l=t(63385),c=t(33784),u=t(87979),d=t(77567);function p(){let{user:e,loading:r}=(0,u.hD)(),[t,i]=(0,o.useState)(""),[p,h]=(0,o.useState)(""),[x,m]=(0,o.useState)(!1),[g,f]=(0,o.useState)(!1),v=async e=>{if(e.preventDefault(),!t||!p)return void d.A.fire({icon:"error",title:"Error",text:"Please fill in all fields",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"});m(!0);try{await (0,l.x9)(c.j2,t,p)}catch(r){console.error("Login error:",r);let e="An error occurred during login";switch(r.code){case"auth/user-not-found":e="No account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=r.message||"Login failed"}d.A.fire({icon:"error",title:"Login Failed",text:e,background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"}),h("")}finally{m(!1)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Welcome Back"}),(0,s.jsx)("p",{className:"text-white/80",children:"Sign in to continue earning"})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,s.jsx)("input",{type:"email",id:"email",value:t,onChange:e=>i(e.target.value),className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:g?"text":"password",id:"password",value:p,onChange:e=>h(e.target.value),className:"form-input pr-12",placeholder:"Enter your password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>f(!g),className:"password-toggle-btn","aria-label":g?"Hide password":"Show password",children:(0,s.jsx)("i",{className:`fas ${g?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:x,className:"w-full btn-primary flex items-center justify-center",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Logging in..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})})]}),(0,s.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,s.jsx)(a(),{href:"/forgot-password",className:"text-white/80 hover:text-white transition-colors",children:"Forgot your password?"}),(0,s.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,s.jsx)(a(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(a(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81746:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=t(65239),o=t(48088),i=t(88170),a=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\login\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},87979:(e,r,t)=>{"use strict";t.d(r,{Nu:()=>a,hD:()=>i,wC:()=>n});var s=t(43210);t(63385),t(33784);var o=t(51278);function i(){let[e,r]=(0,s.useState)(null),[t,i]=(0,s.useState)(!0),a=async()=>{try{await (0,o.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:a}}function a(){let{user:e,loading:r}=i();return{user:e,loading:r}}function n(){let{user:e,loading:r}=i(),[t,o]=(0,s.useState)(!1),[a,n]=(0,s.useState)(!0);return{user:e,loading:r||a,isAdmin:t}}},91645:e=>{"use strict";e.exports=require("net")},92035:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6204,2756,7567,5901],()=>t(81746));module.exports=s})();