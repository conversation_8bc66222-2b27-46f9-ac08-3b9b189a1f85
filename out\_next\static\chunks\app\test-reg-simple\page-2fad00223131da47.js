(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6669],{2275:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(5155),i=a(2115),r=a(3004),n=a(5317),c=a(6104);function o(){let[e,t]=(0,i.useState)(""),[a,o]=(0,i.useState)(!1),l=e=>{t(t=>t+e+"\n")},d=async()=>{t(""),o(!0);let e=null;try{var a,s;l("\uD83E\uDDEA Testing Simple Registration Process...\n"),l("=== STEP 1: Creating Firebase Auth User ===");let t="test".concat(Date.now(),"@example.com");e=(await (0,r.eJ)(c.j2,t,"test123456")).user,l("✅ Auth user created: ".concat(e.uid)),l("   Email: ".concat(e.email)),l("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,2e3)),l("Current auth user: ".concat(null==(a=c.j2.currentUser)?void 0:a.uid)),l("Auth state matches: ".concat((null==(s=c.j2.currentUser)?void 0:s.uid)===e.uid)),l("\n=== STEP 3: Creating User Document ===");let i=Date.now().toString().slice(-4),o=Math.random().toString(36).substring(2,4).toUpperCase(),d="MY".concat(i).concat(o);l("Generated referral code: ".concat(d));let u={name:"Test User",email:t.toLowerCase(),mobile:"9876543210",referralCode:d,referredBy:"",referralBonusCredited:!1,plan:"Trial",planExpiry:null,activeDays:1,joinedDate:n.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,videoDuration:30,status:"active"};l("Document path: users/".concat(e.uid)),l("Data fields: ".concat(Object.keys(u).length)),l("\n=== STEP 4: Creating Document ===");let m=(0,n.H9)(c.db,"users",e.uid);l("Attempting setDoc..."),await (0,n.BN)(m,u),l("✅ setDoc completed successfully"),l("\n=== STEP 5: Verifying Document ===");let h=await (0,n.x7)(m);if(h.exists()){let e=h.data();l("✅ Document verification successful"),l("   Name: ".concat(e.name)),l("   Email: ".concat(e.email)),l("   Plan: ".concat(e.plan)),l("   Referral Code: ".concat(e.referralCode)),l("   Wallet: ".concat(e.wallet)),l("   Fields count: ".concat(Object.keys(e).length)),l("\n\uD83C\uDF89 SUCCESS: Registration process works perfectly!"),l("The issue might be in the registration form logic or error handling.")}else l("❌ Document not found after creation"),l("This indicates a serious Firestore issue")}catch(e){l("❌ Test failed: ".concat(e.message)),l("   Error code: ".concat(e.code)),l("   Error name: ".concat(e.name)),"permission-denied"===e.code?(l("\n\uD83D\uDD27 PERMISSION DENIED ANALYSIS:"),l("   - Firestore security rules are blocking the write"),l("   - Check if user authentication is properly recognized"),l("   - Verify rules allow authenticated users to create documents")):"unavailable"===e.code?(l("\n\uD83D\uDD27 FIRESTORE UNAVAILABLE:"),l("   - Check internet connection"),l("   - Verify Firestore is enabled in Firebase console")):"auth/email-already-in-use"===e.code&&(l("\n\uD83D\uDD27 EMAIL ALREADY IN USE:"),l("   - This is expected if testing multiple times"),l("   - Try with a different email or wait a moment")),l("\n   Full error details:"),l("   ".concat(JSON.stringify(e,null,2)))}finally{if(e)try{l("\n=== CLEANUP ==="),await (0,r.hG)(e),l("✅ Test user deleted")}catch(e){l("⚠️ User deletion failed: ".concat(e.message))}try{await (0,r.CI)(c.j2),l("✅ Signed out")}catch(e){l("⚠️ Sign out failed: ".concat(e.message))}o(!1)}},u=async()=>{t(""),o(!0);try{var e;l("\uD83D\uDD27 Testing Firebase Basics...\n"),l("=== TEST 1: Firebase Instances ==="),l("Auth: ".concat(c.j2?"✅ Initialized":"❌ Not initialized")),l("Firestore: ".concat(c.db?"✅ Initialized":"❌ Not initialized")),l("Current user: ".concat((null==(e=c.j2.currentUser)?void 0:e.uid)||"None")),l("\n=== TEST 2: Basic Firestore Write ===");let t=(0,n.H9)(c.db,"test_basic","test_".concat(Date.now()));await (0,n.BN)(t,{test:!0,timestamp:n.Dc.now()}),l("✅ Basic write successful"),l("\n=== TEST 3: Basic Firestore Read ===");let a=await (0,n.x7)(t);a.exists()?(l("✅ Basic read successful"),l("   Data: ".concat(JSON.stringify(a.data())))):l("❌ Basic read failed"),l("\n✅ Firebase basics are working correctly")}catch(e){l("❌ Firebase basics test failed: ".concat(e.message)),l("   Error code: ".concat(e.code))}finally{o(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Simple Registration"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,s.jsx)("button",{onClick:u,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Firebase Basics"}),(0,s.jsx)("button",{onClick:d,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Registration Process"})]}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||"Click a test button to start..."})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},5848:(e,t,a)=>{Promise.resolve().then(a.bind(a,2275))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var s=a(3915),i=a(3004),r=a(5317),n=a(858);let c=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),o=(0,i.xI)(c),l=(0,r.aU)(c);(0,n.c7)(c)}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,8441,1684,7358],()=>t(5848)),_N_E=e.O()}]);