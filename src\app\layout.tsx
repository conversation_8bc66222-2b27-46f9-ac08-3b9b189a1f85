import type { Metadata, Viewport } from 'next'
import { Poppins } from 'next/font/google'
import './globals.css'
import PWAInstaller from '@/components/PWAInstaller'
import ErrorBoundary from '@/components/ErrorBoundary'

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
})

export const metadata: Metadata = {
  title: 'Instra Global - Instant Translation & Earn',
  description: 'Translate text and earn money. Complete daily translation tasks to earn rewards.',
  keywords: 'translation, earn money, online earning, translation tasks, rewards, language services',
  authors: [{ name: 'Instra Global Team' }],
  manifest: '/manifest.json',
  icons: {
    icon: '/img/instra-favicon.svg',
    apple: '/img/instra-favicon.svg',
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false,
  themeColor: '#6A11CB',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={poppins.variable}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        />
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11" async></script>
      </head>
      <body className={`${poppins.className} antialiased`}>
        <div className="animated-bg"></div>
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
        <PWAInstaller />


      </body>
    </html>
  )
}
