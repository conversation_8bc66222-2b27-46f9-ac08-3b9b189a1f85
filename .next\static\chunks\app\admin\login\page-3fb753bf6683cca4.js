(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2116],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>l,_f:()=>o});var a=s(6104),r=s(4752),i=s.n(r);function n(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await a.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&n(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return l},getImageProps:function(){return o}});let a=s(8229),r=s(8883),i=s(3063),n=a._(s(1193));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let l=i.Image},5430:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(5155),r=s(2115),i=s(6874),n=s.n(i),o=s(6766),l=s(3004),c=s(6104),d=s(6681),u=s(4752),m=s.n(u);function h(){let{user:e,loading:t}=(0,d.hD)(),[s,i]=(0,r.useState)(""),[u,h]=(0,r.useState)(""),[f,g]=(0,r.useState)(!1),[x,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&!t&&(["<EMAIL>","<EMAIL>"].includes(e.email||"")?window.location.href="/admin":(c.j2.signOut(),m().fire({icon:"error",title:"Access Denied",text:"You do not have admin privileges"})))},[e,t]);let w=async e=>{if(e.preventDefault(),!s||!u)return void m().fire({icon:"error",title:"Error",text:"Please fill in all fields"});g(!0);try{let e=(await (0,l.x9)(c.j2,s,u)).user;if(!["<EMAIL>","<EMAIL>"].includes(e.email||""))throw await c.j2.signOut(),Error("Access denied. Admin privileges required.")}catch(t){console.error("Admin login error:",t);let e="An error occurred during login";if(t.message.includes("Access denied"))e="Access denied. Admin privileges required.";else switch(t.code){case"auth/user-not-found":e="No admin account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This admin account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=t.message||"Admin login failed"}m().fire({icon:"error",title:"Admin Login Failed",text:e}),h("")}finally{g(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(o.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Panel"}),(0,a.jsx)("p",{className:"text-white/80",children:"Sign in to access admin dashboard"})]}),(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:[(0,a.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Admin Email"]}),(0,a.jsx)("input",{type:"email",id:"email",value:s,onChange:e=>i(e.target.value),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lock mr-2"}),"Password"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:x?"text":"password",id:"password",value:u,onChange:e=>h(e.target.value),className:"form-input pr-12",placeholder:"Enter admin password",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!x),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"fas ".concat(x?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Signing in..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]})})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30",children:(0,a.jsxs)("div",{className:"flex items-center text-red-300",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"This is a secure admin area. Only authorized personnel can access this panel."})]})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(n(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var a=s(3915),r=s(3004),i=s(5317),n=s(858);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,r.xI)(o),c=(0,i.aU)(o);(0,n.c7)(o)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>l,hD:()=>o,wC:()=>c});var a=s(2115),r=s(3004),i=s(6104),n=s(12);function o(){let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,r.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let l=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:l}}function l(){let{user:e,loading:t}=o();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[s,r]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:s}}},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var a=s(1469),r=s.n(a)},9833:(e,t,s)=>{Promise.resolve().then(s.bind(s,5430))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(9833)),_N_E=e.O()}]);