(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8733],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>l,_f:()=>o});var r=a(6104),n=a(4752),s=a.n(n);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await s().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),s().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},2899:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),n=a(2115),s=a(6874),i=a.n(s),o=a(6681),l=a(6779),c=a(3592),d=a(3737),u=a(4752),g=a.n(u);function x(){let{user:e,loading:t,isAdmin:a}=(0,o.wC)(),[s,u]=(0,n.useState)([]),[x,m]=(0,n.useState)(!0),[p,h]=(0,n.useState)(""),[y,f]=(0,n.useState)(!1),[v,b]=(0,n.useState)(0),[w,D]=(0,n.useState)(null),[j,N]=(0,n.useState)(!1),[S,E]=(0,n.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalVideos:0,todayVideos:0,wallet:0,status:"active",translationDuration:300,quickTranslationAdvantage:!1,quickTranslationAdvantageDays:7,quickTranslationAdvantageSeconds:30}),[k,C]=(0,n.useState)(!1),[A,T]=(0,n.useState)(1),[L,q]=(0,n.useState)(!0),[I,B]=(0,n.useState)(null);(0,n.useEffect)(()=>{a&&M()},[a]);let M=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{m(!0);let t=await (0,l.lo)(50,e?null:I);if(e){u(t.users),T(1);try{let e=await (0,l.nQ)();b(e)}catch(e){console.error("Error getting total user count:",e)}}else u(e=>[...e,...t.users]);B(t.lastDoc),q(t.hasMore)}catch(e){console.error("Error loading users:",e),g().fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{m(!1)}},_=async()=>{if(!p.trim())return void M();try{f(!0);let e=await (0,l.x5)(p.trim());u(e),q(!1)}catch(e){console.error("Error searching users:",e),g().fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{f(!1)}},F=e=>{D(e),E({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalVideos:e.totalVideos,todayVideos:e.todayVideos,wallet:e.wallet||0,status:e.status,translationDuration:e.translationDuration||300,quickTranslationAdvantage:e.quickTranslationAdvantage||!1,quickTranslationAdvantageDays:e.quickTranslationAdvantageDays||7,quickTranslationAdvantageSeconds:e.quickTranslationAdvantageSeconds||30}),N(!0)},P=async()=>{if(w)try{C(!0);let t=w.plan,a=S.plan,r=t!==a,n={name:S.name,email:S.email,mobile:S.mobile,referralCode:S.referralCode,referredBy:S.referredBy,plan:S.plan,activeDays:S.activeDays,totalVideos:S.totalVideos,todayVideos:S.todayVideos,wallet:S.wallet,status:S.status};await (0,l.TK)(w.id,n),S.translationDuration!==(w.translationDuration||300)&&await (0,c.Gl)(w.id,S.translationDuration);let s=w.quickTranslationAdvantage&&w.quickTranslationAdvantageExpiry&&new Date<w.quickTranslationAdvantageExpiry;if(S.quickTranslationAdvantage&&!s?await (0,c.w1)(w.id,S.quickTranslationAdvantageDays,(null==e?void 0:e.email)||"admin",S.quickTranslationAdvantageSeconds):!S.quickTranslationAdvantage&&s?await (0,c.wT)(w.id,(null==e?void 0:e.email)||"admin"):S.quickTranslationAdvantage&&s&&(await (0,c.wT)(w.id,(null==e?void 0:e.email)||"admin"),await (0,c.w1)(w.id,S.quickTranslationAdvantageDays,(null==e?void 0:e.email)||"admin",S.quickTranslationAdvantageSeconds)),r)try{await (0,c.II)(w.id,a),console.log("Updated plan expiry for user ".concat(w.id,": ").concat(t," -> ").concat(a))}catch(e){console.error("Error updating plan expiry:",e)}if(r&&"Trial"===t&&"Trial"!==a)try{console.log("Processing referral bonus for user ".concat(w.id,": ").concat(t," -> ").concat(a)),await (0,c.IK)(w.id,t,a),g().fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:'\n              <div class="text-left">\n                <p><strong>User plan updated:</strong> '.concat(t," → ").concat(a,"</p>\n                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>\n              </div>\n            "),timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error processing referral bonus:",e),g().fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:'\n              <div class="text-left">\n                <p><strong>User plan updated successfully:</strong> '.concat(t," → ").concat(a,'</p>\n                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>\n                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>\n              </div>\n            '),timer:5e3,showConfirmButton:!1})}else{let e="User information has been updated successfully";S.quickTranslationAdvantage&&!s?e+=". Quick translation advantage granted for ".concat(S.quickTranslationAdvantageDays," days."):!S.quickTranslationAdvantage&&s?e+=". Quick translation advantage removed.":S.quickTranslationAdvantage&&s&&(e+=". Quick translation advantage updated for ".concat(S.quickTranslationAdvantageDays," days.")),g().fire({icon:"success",title:"User Updated",text:e,timer:3e3,showConfirmButton:!1})}u(e=>e.map(e=>e.id===w.id?{...e,...n,translationDuration:S.translationDuration,quickTranslationAdvantage:S.quickTranslationAdvantage,quickTranslationAdvantageDays:S.quickTranslationAdvantageDays,quickTranslationAdvantageSeconds:S.quickTranslationAdvantageSeconds}:e)),N(!1),D(null),await M()}catch(e){console.error("Error updating user:",e),g().fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{C(!1)}},V=async e=>{if((await g().fire({icon:"warning",title:"Delete User",text:"Are you sure you want to delete ".concat(e.name,"? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,l.hG)(e.id),u(t=>t.filter(t=>t.id!==e.id)),g().fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),g().fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},O=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2)),U=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},W=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}},R=async()=>{try{g().fire({title:"Exporting Users...",text:"Please wait while we prepare your export file.",allowOutsideClick:!1,didOpen:()=>{g().showLoading()}});let e=await (0,l.CF)();if(0===e.length)return void g().fire({icon:"warning",title:"No Data",text:"No users to export."});let t=(0,d.Fz)(e);(0,d.Bf)(t,"users"),g().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(e.length," users to CSV file."),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error exporting users:",e),g().fire({icon:"error",title:"Export Failed",text:"Failed to export users. Please try again."})}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)(i(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),v>0&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:p?"Showing ".concat(s.length," of ").concat(v," users"):"Total: ".concat(v," users")})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,r.jsxs)("button",{onClick:R,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>M(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("input",{type:"text",value:p,onChange:e=>h(e.target.value),placeholder:"Search by name, email, mobile, or referral code...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyDown:e=>"Enter"===e.key&&_()}),(0,r.jsx)("button",{onClick:_,disabled:y,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),p&&(0,r.jsx)("button",{onClick:()=>{h(""),M()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Videos"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Translation Duration"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Copy-Paste"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x&&0===s.length?(0,r.jsx)("tr",{children:(0,r.jsxs)("td",{colSpan:9,className:"px-6 py-4 text-center",children:[(0,r.jsx)("div",{className:"spinner mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===s.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:9,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):s.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():new Date(e.joinedDate).toLocaleDateString()]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(U(e.plan)),children:e.plan}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalVideos]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayVideos]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300,"s"):"".concat(Math.round((e.videoDuration||300)/60),"m")}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300," second").concat((e.videoDuration||300)>1?"s":""):"".concat(Math.round((e.videoDuration||300)/60)," minute").concat(Math.round((e.videoDuration||300)/60)>1?"s":"")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickTranslationAdvantage&&e.quickTranslationAdvantageExpiry&&new Date<e.quickTranslationAdvantageExpiry?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Enabled"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Until: ",e.quickTranslationAdvantageExpiry instanceof Date?e.quickTranslationAdvantageExpiry.toLocaleDateString():new Date(e.quickTranslationAdvantageExpiry).toLocaleDateString()]})]}):(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"Disabled"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),O(e.wallet||0)]}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(W(e.status)),children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>F(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,r.jsx)("i",{className:"fas fa-edit"})}),(0,r.jsx)("button",{onClick:()=>V(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,r.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),L&&!x&&s.length>0&&(0,r.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,r.jsxs)("button",{onClick:()=>{L&&!x&&M(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),j&&w&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,r.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,r.jsx)("input",{type:"text",value:S.name,onChange:e=>E(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,r.jsx)("input",{type:"email",value:S.email,onChange:e=>E(t=>({...t,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,r.jsx)("input",{type:"text",value:S.mobile,onChange:e=>E(t=>({...t,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,r.jsx)("input",{type:"text",value:S.referralCode,onChange:e=>E(t=>({...t,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,r.jsx)("input",{type:"text",value:S.referredBy,onChange:e=>E(t=>({...t,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,r.jsxs)("select",{value:S.plan,onChange:e=>E(t=>({...t,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Trial",children:"Trial"}),(0,r.jsx)("option",{value:"Starter",children:"Starter"}),(0,r.jsx)("option",{value:"Basic",children:"Basic"}),(0,r.jsx)("option",{value:"Premium",children:"Premium"}),(0,r.jsx)("option",{value:"Gold",children:"Gold"}),(0,r.jsx)("option",{value:"Platinum",children:"Platinum"}),(0,r.jsx)("option",{value:"Diamond",children:"Diamond"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,r.jsx)("input",{type:"number",value:S.activeDays,onChange:e=>E(t=>({...t,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Videos"}),(0,r.jsx)("input",{type:"number",value:S.totalVideos,onChange:e=>E(t=>({...t,totalVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Videos"}),(0,r.jsx)("input",{type:"number",value:S.todayVideos,onChange:e=>E(t=>({...t,todayVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,r.jsx)("input",{type:"number",step:"0.01",value:S.wallet,onChange:e=>E(t=>({...t,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,r.jsxs)("select",{value:S.videoDuration,onChange:e=>E(t=>({...t,videoDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]}),(0,r.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,r.jsx)("option",{value:60,children:"1 minute"}),(0,r.jsx)("option",{value:120,children:"2 minutes"}),(0,r.jsx)("option",{value:180,children:"3 minutes"}),(0,r.jsx)("option",{value:240,children:"4 minutes"}),(0,r.jsx)("option",{value:300,children:"5 minutes"}),(0,r.jsx)("option",{value:360,children:"6 minutes"}),(0,r.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:S.videoDuration<60?"".concat(S.videoDuration," second").concat(S.videoDuration>1?"s":""):"".concat(Math.round(S.videoDuration/60)," minute").concat(Math.round(S.videoDuration/60)>1?"s":"")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:S.status,onChange:e=>E(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,r.jsxs)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:[(0,r.jsx)("i",{className:"fas fa-copy mr-2 text-yellow-500"}),"Quick Translation Copy-Paste Advantage"]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"When enabled, user can copy-paste English text instead of typing manually"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"quickTranslationAdvantage",checked:S.quickTranslationAdvantage,onChange:e=>E(t=>({...t,quickTranslationAdvantage:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"quickTranslationAdvantage",className:"ml-2 block text-sm text-gray-700",children:"Enable Copy-Paste for English Text"})]}),S.quickTranslationAdvantage&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Days"}),(0,r.jsx)("input",{type:"number",min:"1",max:"365",value:S.quickTranslationAdvantageDays,onChange:e=>E(t=>({...t,quickTranslationAdvantageDays:parseInt(e.target.value)||7})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Translation Duration"}),(0,r.jsxs)("select",{value:S.quickTranslationAdvantageSeconds,onChange:e=>E(t=>({...t,quickTranslationAdvantageSeconds:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]})]})]}),w&&(0,r.jsx)("div",{className:"ml-6 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Current Status:"})," ",w.quickTranslationAdvantage&&w.quickTranslationAdvantageExpiry&&new Date<w.quickTranslationAdvantageExpiry?(0,r.jsxs)("span",{className:"text-green-600",children:["Copy-paste enabled until ",w.quickTranslationAdvantageExpiry instanceof Date?w.quickTranslationAdvantageExpiry.toLocaleDateString():new Date(w.quickTranslationAdvantageExpiry).toLocaleDateString()]}):(0,r.jsx)("span",{className:"text-gray-500",children:"Copy-paste disabled - manual typing required"})]})})]})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:P,disabled:k,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:k?"Saving...":"Save Changes"}),(0,r.jsx)("button",{onClick:()=>N(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},3737:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],s=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=n.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):r&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(s);i.setAttribute("href",e),i.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function n(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Advantage Days":e.quickVideoAdvantageDays||"","Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function s(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>{var t,a,r,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(r=e.bankDetails)?void 0:r.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>n,Pe:()=>o,dB:()=>i,sL:()=>s})},5125:(e,t,a)=>{Promise.resolve().then(a.bind(a,2899))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>l});var r=a(3915),n=a(3004),s=a(5317),i=a(858);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,n.xI)(o),c=(0,s.aU)(o);(0,i.c7)(o)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>l,hD:()=>o,wC:()=>c});var r=a(2115),n=a(3004),s=a(6104),i=a(12);function o(){let[e,t]=(0,r.useState)(null),[a,o]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,n.hg)(s.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let l=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:l}}function l(){let{user:e,loading:t}=o();return(0,r.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[a,n]=(0,r.useState)(!1),[s,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");n(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||s,isAdmin:a}}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,Pn:()=>o,TK:()=>x,getWithdrawals:()=>g,hG:()=>m,lo:()=>l,nQ:()=>u,updateWithdrawalStatus:()=>p,x5:()=>c});var r=a(5317),n=a(6104),s=a(3592);let i=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=r.Dc.fromDate(t),o=await (0,r.getDocs)((0,r.collection)(n.db,s.COLLECTIONS.users)),l=o.size,c=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.users),(0,r._M)(s.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,r.getDocs)(c)).size,u=0,g=0,x=0,m=0;o.forEach(e=>{var a;let r=e.data();u+=r[s.FIELD_NAMES.totalTranslations]||0,g+=r[s.FIELD_NAMES.wallet]||0;let n=null==(a=r[s.FIELD_NAMES.lastTranslationDate])?void 0:a.toDate();n&&n.toDateString()===t.toDateString()&&(x+=r[s.FIELD_NAMES.todayTranslations]||0)});try{let e=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.transactions),(0,r._M)(s.FIELD_NAMES.type,"==","translation_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{var a;let r=e.data(),n=null==(a=r[s.FIELD_NAMES.date])?void 0:a.toDate();n&&n>=t&&(m+=r[s.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let p=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),h=(await (0,r.getDocs)(p)).size,y=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),f=(await (0,r.getDocs)(y)).size,v={totalUsers:l,totalTranslations:u,totalEarnings:g,pendingWithdrawals:h,todayUsers:d,todayTranslations:x,todayEarnings:m,todayWithdrawals:f};return i.set(e,{data:v,timestamp:Date.now()}),v}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.getDocs)(a);return{users:i.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[s.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[s.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[s.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[s.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[s.FIELD_NAMES.name]||"").toLowerCase(),r=String(e[s.FIELD_NAMES.email]||"").toLowerCase(),n=String(e[s.FIELD_NAMES.mobile]||"").toLowerCase(),i=String(e[s.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||r.includes(t)||n.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[s.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[s.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(n.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.getDocs)(a);return{withdrawals:i.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function x(e,t){try{await (0,r.mZ)((0,r.H9)(n.db,s.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function m(e){try{await (0,r.kd)((0,r.H9)(n.db,s.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function p(e,t,o){try{let l=await (0,r.x7)((0,r.H9)(n.db,s.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),g={status:t,updatedAt:r.Dc.now()};if(o&&(g.adminNotes=o),await (0,r.mZ)((0,r.H9)(n.db,s.COLLECTIONS.withdrawals,e),g),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(5125)),_N_E=e.O()}]);