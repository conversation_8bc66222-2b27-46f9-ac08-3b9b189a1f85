(()=>{var e={};e.id=5338,e.ids=[5338],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20961:(e,t,r)=>{Promise.resolve().then(r.bind(r,80682))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>u,j2:()=>c});var s=r(67989),i=r(63385),o=r(75535),n=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),c=(0,i.xI)(a),u=(0,o.aU)(a);(0,n.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53360:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\debug-firestore\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\debug-firestore\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80682:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),o=r(63385),n=r(75535),a=r(33784),c=r(3582);function u(){let[e,t]=(0,i.useState)(""),[r,u]=(0,i.useState)(!1),l=e=>{t(t=>t+e+"\n"),console.log(e)},d=async()=>{u(!0),t("");try{l("\uD83D\uDD0D Starting Firestore Diagnostics..."),l("=".repeat(50)),l("\n\uD83D\uDCCB Test 1: Firebase Configuration"),l("Project ID: instra-global"),l("Auth Domain: instra-global.firebaseapp.com"),l(`API Key: ${"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68".substring(0,10)}...`),l("\n\uD83D\uDD17 Test 2: Firestore Connection");try{(0,n.collection)(a.db,"test"),l("✅ Firestore connection established")}catch(e){l(`❌ Firestore connection failed: ${e.message}`);return}l("\n\uD83D\uDD10 Test 3: Authentication Test");let e=`test-${Date.now()}@example.com`,t=null;try{t=(await (0,o.eJ)(a.j2,e,"test123456")).user,l(`✅ Test user created: ${t.uid}`)}catch(e){l(`❌ User creation failed: ${e.message}`);return}l("\n\uD83D\uDCDD Test 4: Firestore Write Test");let r={[c.FIELD_NAMES.name]:"Debug Test User",[c.FIELD_NAMES.email]:e,[c.FIELD_NAMES.mobile]:"9999999999",[c.FIELD_NAMES.referralCode]:"DEBUG001",[c.FIELD_NAMES.referredBy]:"",[c.FIELD_NAMES.plan]:"Trial",[c.FIELD_NAMES.planExpiry]:null,[c.FIELD_NAMES.activeDays]:2,[c.FIELD_NAMES.joinedDate]:n.Dc.now(),[c.FIELD_NAMES.wallet]:0,[c.FIELD_NAMES.totalVideos]:0,[c.FIELD_NAMES.todayVideos]:0,[c.FIELD_NAMES.lastVideoDate]:null,status:"active"};try{let e=(0,n.H9)(a.db,c.COLLECTIONS.users,t.uid);l(`📍 Document path: ${e.path}`),l(`📊 Data to write: ${JSON.stringify(r,null,2)}`),await (0,n.BN)(e,r),l("✅ Document created successfully")}catch(e){l(`❌ Document creation failed: ${e.message}`),l(`❌ Error code: ${e.code}`),l(`❌ Full error: ${JSON.stringify(e,null,2)}`)}l("\n\uD83D\uDCD6 Test 5: Firestore Read Test");try{let e=(0,n.H9)(a.db,c.COLLECTIONS.users,t.uid),r=await (0,n.x7)(e);r.exists()?(l("✅ Document read successfully"),l(`📄 Document data: ${JSON.stringify(r.data(),null,2)}`)):l("❌ Document does not exist after creation")}catch(e){l(`❌ Document read failed: ${e.message}`)}l("\n\uD83D\uDCDA Test 6: Collection Access Test");try{let e=(0,n.collection)(a.db,c.COLLECTIONS.users),t=await (0,n.getDocs)(e);l(`✅ Collection accessible, found ${t.size} documents`)}catch(e){l(`❌ Collection access failed: ${e.message}`)}l("\n\uD83C\uDFF7️ Test 7: Field Names Check"),l(`COLLECTIONS.users: ${c.COLLECTIONS.users}`),Object.entries(c.FIELD_NAMES).forEach(([e,t])=>{l(`FIELD_NAMES.${e}: ${t}`)}),l("\n\uD83D\uDCDD Test 8: Simple Document Test");try{let r={name:"Simple Test",email:e,created:new Date().toISOString()},s=(0,n.H9)(a.db,"test-collection",t.uid);await (0,n.BN)(s,r),l("✅ Simple document created successfully"),(await (0,n.x7)(s)).exists()?l("✅ Simple document verified"):l("❌ Simple document not found")}catch(e){l(`❌ Simple document test failed: ${e.message}`)}l("\n\uD83E\uDDF9 Cleanup: Deleting test user");try{await (0,o.hG)(t),l("✅ Test user deleted successfully")}catch(e){l(`⚠️ Test user deletion failed: ${e.message}`)}l("\n\uD83C\uDF89 Diagnostics completed!")}catch(e){l(`💥 Unexpected error: ${e.message}`),l(`💥 Stack trace: ${e.stack}`)}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"\uD83D\uDD0D Firestore Debug Tool"}),(0,s.jsx)("p",{className:"text-white/80 mb-6",children:"This tool will run comprehensive diagnostics to identify why Firestore user creation is failing."}),(0,s.jsx)("button",{onClick:d,disabled:r,className:"btn-primary mb-6",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Running Diagnostics..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-bug mr-2"}),"Run Firestore Diagnostics"]})}),e&&(0,s.jsxs)("div",{className:"bg-black/50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Diagnostic Results:"}),(0,s.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono overflow-x-auto",children:e})]})]})})})}},81630:e=>{"use strict";e.exports=require("http")},84513:(e,t,r)=>{Promise.resolve().then(r.bind(r,53360))},91645:e=>{"use strict";e.exports=require("net")},92494:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>p,tree:()=>u});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(t,c);let u={children:["",{children:["debug-firestore",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,53360)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\debug-firestore\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\debug-firestore\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-firestore/page",pathname:"/debug-firestore",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,5901,3582],()=>r(92494));module.exports=s})();