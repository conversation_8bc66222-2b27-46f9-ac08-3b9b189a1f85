(()=>{var e={};e.id=6393,e.ids=[6393],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14375:(e,s,t)=>{Promise.resolve().then(t.bind(t,29155))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28605:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(60687),i=t(43210),n=t(85814),a=t.n(n),o=t(87979),l=t(3582),c=t(77567);function d(){let{user:e,loading:s,isAdmin:t}=(0,o.wC)(),[n,d]=(0,i.useState)(!1),u=async()=>{try{d(!0),await (0,l.z8)({title:"\uD83D\uDEA8 Important System Update",message:"This is a test notification. Users must acknowledge this message before they can continue using the platform. This ensures important announcements are seen by all users.",type:"warning",targetUsers:"all",userIds:[],createdBy:e?.email||"admin"}),c.A.fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users. Users will need to acknowledge this before accessing any features.",timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error sending blocking notification:",e),c.A.fire({icon:"error",title:"Send Failed",text:"Failed to send blocking notification. Please try again."})}finally{d(!1)}},x=async()=>{try{d(!0),await (0,l.z8)({title:"Another Test Notification",message:"This is another test notification. All notifications are now blocking and users must acknowledge them.",type:"info",targetUsers:"all",userIds:[],createdBy:e?.email||"admin"}),c.A.fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users.",timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error sending notification:",e),c.A.fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{d(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"flex items-center justify-between px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(a(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Blocking Notifications"})]})})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-bold text-blue-900 mb-3",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Test Notifications"]}),(0,r.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"1."})," Send a notification using the buttons below"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"2."})," Open a new tab and go to the user dashboard or work page"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"3."})," You should see a full-page modal that blocks all activities until acknowledged"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"4."}),' Click "Acknowledge" to dismiss the notification']}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"5."})," All notifications are now blocking/mandatory for better user engagement"]})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDEA8 Warning Notification"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends a warning notification that users must acknowledge before continuing"}),(0,r.jsx)("button",{onClick:u,disabled:n,className:"w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Warning Notification"]})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("i",{className:"fas fa-bell text-blue-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDCE2 Info Notification"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends an info notification that users must acknowledge before continuing"}),(0,r.jsx)("button",{onClick:x,disabled:n,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Info Notification"]})})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-2 text-green-500"}),"Notification Features (All Blocking)"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-shield-alt text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Blocks all user activities until acknowledged"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-eye text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Forces users to read important announcements"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-users text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Can target all users or specific users"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-mobile-alt text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Works on all pages (dashboard, work, wallet, etc.)"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-chart-line text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Progress indicator for multiple notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-clock text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Persistent until user acknowledges"})]})]})]})]})]})})]})}},29021:e=>{"use strict";e.exports=require("fs")},29155:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\test-blocking\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\test-blocking\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,s,t)=>{"use strict";t.d(s,{db:()=>c,j2:()=>l});var r=t(67989),i=t(63385),n=t(75535),a=t(70146);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,i.xI)(o),c=(0,n.aU)(o);(0,a.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},50176:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["admin",{children:["test-blocking",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29155)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\test-blocking\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\test-blocking\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/test-blocking/page",pathname:"/admin/test-blocking",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51278:(e,s,t)=>{"use strict";t.d(s,{M4:()=>o,_f:()=>a});var r=t(33784),i=t(77567);function n(e){try{Object.keys(localStorage).forEach(s=>{(s.includes(e)||s.startsWith("video_session_")||s.startsWith("watch_times_")||s.startsWith("video_refresh_")||s.startsWith("video_change_notification_")||s.startsWith("leave_")||s.includes("mytube_")||s.includes("user_"))&&localStorage.removeItem(s)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function a(e,s="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await r.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=s}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,s="/login"){try{e&&n(e),await r.j2.signOut(),window.location.href=s}catch(e){console.error("Quick logout error:",e),window.location.href=s}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,s,t)=>{"use strict";t.d(s,{Nu:()=>a,hD:()=>n,wC:()=>o});var r=t(43210);t(63385),t(33784);var i=t(51278);function n(){let[e,s]=(0,r.useState)(null),[t,n]=(0,r.useState)(!0),a=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:a}}function a(){let{user:e,loading:s}=n();return{user:e,loading:s}}function o(){let{user:e,loading:s}=n(),[t,i]=(0,r.useState)(!1),[a,o]=(0,r.useState)(!0);return{user:e,loading:s||a,isAdmin:t}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96223:(e,s,t)=>{Promise.resolve().then(t.bind(t,28605))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[6204,2756,7567,5901,3582],()=>t(50176));module.exports=r})();