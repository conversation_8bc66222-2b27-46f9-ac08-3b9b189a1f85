{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/fix-active-days", "regex": "^/admin/fix\\-active\\-days(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/fix\\-active\\-days(?:/)?$"}, {"page": "/admin/fix-permissions", "regex": "^/admin/fix\\-permissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/fix\\-permissions(?:/)?$"}, {"page": "/admin/leaves", "regex": "^/admin/leaves(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/leaves(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/admin/notifications", "regex": "^/admin/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/setup", "regex": "^/admin/setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/setup(?:/)?$"}, {"page": "/admin/simple-upload", "regex": "^/admin/simple\\-upload(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/simple\\-upload(?:/)?$"}, {"page": "/admin/test-blocking", "regex": "^/admin/test\\-blocking(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/test\\-blocking(?:/)?$"}, {"page": "/admin/transactions", "regex": "^/admin/transactions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/transactions(?:/)?$"}, {"page": "/admin/upload-users", "regex": "^/admin/upload\\-users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/upload\\-users(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/admin/withdrawals", "regex": "^/admin/withdrawals(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/withdrawals(?:/)?$"}, {"page": "/clear-cache", "regex": "^/clear\\-cache(?:/)?$", "routeKeys": {}, "namedRegex": "^/clear\\-cache(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/debug-firestore", "regex": "^/debug\\-firestore(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-firestore(?:/)?$"}, {"page": "/debug-firestore-issue", "regex": "^/debug\\-firestore\\-issue(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-firestore\\-issue(?:/)?$"}, {"page": "/debug-registration", "regex": "^/debug\\-registration(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-registration(?:/)?$"}, {"page": "/debug-registration-simple", "regex": "^/debug\\-registration\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-registration\\-simple(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/plans", "regex": "^/plans(?:/)?$", "routeKeys": {}, "namedRegex": "^/plans(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/refer", "regex": "^/refer(?:/)?$", "routeKeys": {}, "namedRegex": "^/refer(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/registration-diagnostics", "regex": "^/registration\\-diagnostics(?:/)?$", "routeKeys": {}, "namedRegex": "^/registration\\-diagnostics(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/support", "regex": "^/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/support(?:/)?$"}, {"page": "/test-firebase", "regex": "^/test\\-firebase(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-firebase(?:/)?$"}, {"page": "/test-firebase-connection", "regex": "^/test\\-firebase\\-connection(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-firebase\\-connection(?:/)?$"}, {"page": "/test-firebase-connectivity", "regex": "^/test\\-firebase\\-connectivity(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-firebase\\-connectivity(?:/)?$"}, {"page": "/test-firestore", "regex": "^/test\\-firestore(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-firestore(?:/)?$"}, {"page": "/test-reg-simple", "regex": "^/test\\-reg\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-reg\\-simple(?:/)?$"}, {"page": "/test-registration", "regex": "^/test\\-registration(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-registration(?:/)?$"}, {"page": "/test-simple-registration", "regex": "^/test\\-simple\\-registration(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-simple\\-registration(?:/)?$"}, {"page": "/test-translations", "regex": "^/test\\-translations(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-translations(?:/)?$"}, {"page": "/transactions", "regex": "^/transactions(?:/)?$", "routeKeys": {}, "namedRegex": "^/transactions(?:/)?$"}, {"page": "/wallet", "regex": "^/wallet(?:/)?$", "routeKeys": {}, "namedRegex": "^/wallet(?:/)?$"}, {"page": "/work", "regex": "^/work(?:/)?$", "routeKeys": {}, "namedRegex": "^/work(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}