(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return c},getImageProps:function(){return r}});let a=t(8229),l=t(8883),i=t(3063),n=a._(t(1193));function r(e){let{props:s}=(0,l.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let c=i.Image},3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(5155);t(2115);var l=t(6874),i=t.n(l),n=t(8926),r=t(6766);function c(){return(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-50 p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(r.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:32,height:32,className:"mr-2"}),(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:"Instra Global"})]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)(i(),{href:"#pricing",className:"nav-link",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-2"}),"Plans"]}),(0,a.jsxs)(i(),{href:"/login",className:"nav-link",children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})]})]})}),(0,a.jsx)("section",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,a.jsx)(r.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:60,height:60,className:"mr-4"}),(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"Instra Global"})]}),(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-bold mb-6 gradient-text",children:"Translate Text & Earn Money"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto",children:"Translate text and earn up to ₹30,000 per month. Start your journey to financial freedom today by completing simple translation tasks!"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:[(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-language text-4xl text-instra-purple mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Multiple Languages"}),(0,a.jsx)("p",{className:"text-white/80",children:"Translate to various languages daily"})]}),(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Instant Earnings"}),(0,a.jsx)("p",{className:"text-white/80",children:"Get paid for every translation completed"})]}),(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-bolt text-4xl text-yellow-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Fast & Simple"}),(0,a.jsx)("p",{className:"text-white/80",children:"Easy translation process"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(i(),{href:"/login",className:"btn-primary inline-flex items-center text-lg px-8 py-4",children:[(0,a.jsx)("i",{className:"fas fa-rocket mr-3"}),"Start Earning Now"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mt-6",children:[(0,a.jsxs)(i(),{href:"/how-it-works",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How It Works"]}),(0,a.jsxs)(i(),{href:"/faq",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"FAQ"]})]})]})]})}),(0,a.jsx)("section",{id:"pricing",className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Choose Your Earning Plan"}),(0,a.jsx)("p",{className:"text-xl text-white/80 max-w-3xl mx-auto",children:"Start with our free trial or upgrade to premium plans for higher earnings. Translate text and earn money with flexible pricing options."})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"glass-card p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Trial"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"Free"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 2 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹10 per 50 translations"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"2 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹10 per 50 translations"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Basic support"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Simple text translations"]})]}),(0,a.jsx)(i(),{href:"/register",className:"w-full btn-secondary block text-center",children:"Start Free Trial"})]}),(0,a.jsxs)("div",{className:"glass-card p-8 relative ring-2 ring-yellow-400",children:[(0,a.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Gold"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"₹3,999"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 30 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹200 per 50 translations"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"30 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹200 per 50 translations"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Medium complexity texts"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Referral bonus: ₹400"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Priority support"]})]}),(0,a.jsx)(i(),{href:"/plans",className:"w-full bg-yellow-400 text-black py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-300 block text-center",children:"Choose Gold"})]}),(0,a.jsxs)("div",{className:"glass-card p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Diamond"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"₹9,999"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 30 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹400 per 50 translations"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"30 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹400 per 50 translations"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Complex text translations"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Referral bonus: ₹1200"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"VIP support"]})]}),(0,a.jsx)(i(),{href:"/plans",className:"w-full btn-primary block text-center",children:"Choose Diamond"})]})]}),(0,a.jsx)("div",{className:"text-center mt-12",children:(0,a.jsxs)(i(),{href:"/plans",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-2"}),"View All Plans"]})})]})}),(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Need Help?"}),(0,a.jsx)("p",{className:"text-xl text-white/80 mb-12 max-w-2xl mx-auto",children:"Our support team is here to help you get started and answer any questions about earning with Instra Global."}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,a.jsxs)("a",{href:"https://wa.me/917676636990",target:"_blank",rel:"noopener noreferrer",className:"glass-card p-8 hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-5xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"WhatsApp Support"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Get instant help via WhatsApp"}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"+91 7676636990"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"9 AM - 6 PM (Working days)"})]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"glass-card p-8 hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-5xl text-blue-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Email Support"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Send us detailed queries"}),(0,a.jsx)("p",{className:"text-blue-400 font-semibold",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"9 AM - 6 PM (Working days)"})]}),(0,a.jsx)(n.A,{variant:"homepage"})]})]})})]})}},4188:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>l.a});var a=t(1469),l=t.n(a)},8926:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(5155),l=t(2115),i=t(4752),n=t.n(i);function r(e){let{variant:s="homepage",className:t=""}=e,{isInstallable:i,isInstalled:r,installApp:c,getInstallInstructions:x}=function(){let[e,s]=(0,l.useState)(null),[t,a]=(0,l.useState)(!1),[i,n]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=e=>{e.preventDefault(),s(e),a(!0)},t=()=>{n(!0),a(!1),s(null)};return window.matchMedia("(display-mode: standalone)").matches&&n(!0),window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",t),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",t)}},[]),{isInstallable:t,isInstalled:i,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:t}=await e.userChoice;if("accepted"===t)return n(!0),a(!1),s(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[o,m]=(0,l.useState)(!1),d=async()=>{await c()?n().fire({icon:"success",title:"App Installed!",text:"MyTube has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):i||m(!0)},h=()=>{let e=x();n().fire({title:"Install MyTube on ".concat(e.browser),html:'\n        <div class="text-left">\n          <p class="mb-4 text-gray-600">Follow these steps to install MyTube as an app:</p>\n          <ol class="list-decimal list-inside space-y-2">\n            '.concat(e.steps.map(e=>'<li class="text-gray-700">'.concat(e,"</li>")).join(""),'\n          </ol>\n          <div class="mt-6 p-4 bg-blue-50 rounded-lg">\n            <p class="text-sm text-blue-800">\n              <i class="fas fa-info-circle mr-2"></i>\n              Installing the app gives you faster access, offline capabilities, and a native app experience!\n            </p>\n          </div>\n        </div>\n      '),confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})};return r?(0,a.jsx)("div",{className:"".concat(t),children:"homepage"===s?(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,a.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,a.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===s?(0,a.jsxs)("div",{className:"glass-card p-8 hover:scale-105 transition-transform ".concat(t),children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,a.jsx)("div",{className:"space-y-3",children:i?(0,a.jsxs)("button",{onClick:d,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,a.jsxs)("button",{onClick:h,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,a.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,a.jsx)("div",{className:"glass-card p-4 ".concat(t),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),i?(0,a.jsxs)("button",{onClick:d,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,a.jsxs)("button",{onClick:h,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8320,6874,3063,8441,1684,7358],()=>s(4188)),_N_E=e.O()}]);