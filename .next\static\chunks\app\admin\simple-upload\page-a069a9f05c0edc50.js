(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[773,6779],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>n,_f:()=>o});var s=a(6104),l=a(4752),r=a.n(l);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await r().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await s.j2.signOut(),r().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),r().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},3489:(e,t,a)=>{Promise.resolve().then(a.bind(a,5067))},5067:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(5155),l=a(2115),r=a(6874),i=a.n(r),o=a(6681),n=a(6779),c=a(4752),d=a.n(c);function u(){let{user:e,loading:t,isAdmin:r}=(0,o.wC)(),[c,u]=(0,l.useState)(!1),[h,m]=(0,l.useState)(null),[w,x]=(0,l.useState)(null),[p,f]=(0,l.useState)([]),[g,v]=(0,l.useState)(!1),b=async()=>{if(w)try{u(!0);let e=(await w.text()).split("\n").filter(e=>e.trim());if(e.length<2)throw Error("CSV file must have at least a header row and one data row");let t=e[0],a=t.includes("	")?"	":",",s=t.split(a).map(e=>e.trim().replace(/"/g,"").toLowerCase());if(["email","totalvideos","walletbalance","activedays"].filter(e=>!s.some(t=>t.includes(e.replace("totalvideos","videos").replace("walletbalance","wallet").replace("activedays","active")))).length>0)throw Error("Missing required columns. Expected: email, totalVideos (or videos), walletBalance (or wallet), activeDays (or active)");let l=e.slice(1).map((e,t)=>{let l=e.split(a).map(e=>e.trim().replace(/"/g,"")),r={};s.forEach((e,t)=>{r[e]=l[t]||""});let i=r.email||"",o=parseInt(r.totalvideos||r.videos||r.totalVideos||"0")||0,n=parseFloat(r.walletbalance||r.wallet||r.walletBalance||"0")||0,c=parseInt(r.activedays||r.active||r.activeDays||"0")||0;if(!i)throw Error("Row ".concat(t+2,": Email is required"));if(!i.includes("@"))throw Error("Row ".concat(t+2,": Invalid email format"));return{email:i,totalVideos:o,walletBalance:n,activeDays:c}});f(l.slice(0,10)),v(!0)}catch(e){console.error("Error previewing file:",e),d().fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{u(!1)}},j=async()=>{if(w&&(await d().fire({icon:"question",title:"Confirm Data Upload",html:'\n        <div class="text-left">\n          <p><strong>Are you sure you want to update user data from this file?</strong></p>\n          <br>\n          <p>This will:</p>\n          <ul>\n            <li>Find users by email address</li>\n            <li>Add to their existing total videos count</li>\n            <li>Add to their existing wallet balance</li>\n            <li>SET their active days to the specified value</li>\n            <li>Skip users not found in the system</li>\n          </ul>\n          <br>\n          <p class="text-yellow-600"><strong>Note:</strong> Videos and wallet will be ADDED, but active days will be SET (replaced)!</p>\n        </div>\n      ',showCancelButton:!0,confirmButtonText:"Yes, Update Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{u(!0),m(null),d().fire({title:"Updating Users",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p>Processing user updates...</p>\n            <p class="text-sm text-gray-600 mt-2">Please wait...</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});let e=(await w.text()).split("\n").filter(e=>e.trim()),t=e[0],s=t.includes("	")?"	":",",l=t.split(s).map(e=>e.trim().replace(/"/g,"").toLowerCase()),r=e.slice(1).map(e=>{let t=e.split(s).map(e=>e.trim().replace(/"/g,"")),a={};return l.forEach((e,s)=>{a[e]=t[s]||""}),{email:a.email||"",totalVideos:parseInt(a.totalvideos||a.videos||a.totalVideos||"0")||0,walletBalance:parseFloat(a.walletbalance||a.wallet||a.walletBalance||"0")||0,activeDays:parseInt(a.activedays||a.active||a.activeDays||"0")||0}}).filter(e=>e.email),i=0,o=0,c=0,h=[];for(let e of r)try{let t=(await (0,n.x5)(e.email)).find(t=>{var a;return(null==(a=t.email)?void 0:a.toLowerCase())===e.email.toLowerCase()});if(!t){c++,h.push("User not found: ".concat(e.email));continue}let{getWalletData:s,getVideoCountData:l}=await Promise.resolve().then(a.bind(a,3592)),[r,o]=await Promise.all([s(t.id),l(t.id)]),d={totalVideos:(o.totalVideos||0)+e.totalVideos,wallet:(r.wallet||0)+e.walletBalance,activeDays:e.activeDays};await (0,n.TK)(t.id,d),i++}catch(t){o++,h.push("Failed to update ".concat(e.email,": ").concat(t.message))}d().close();let x={success:i,failed:o,errors:h,notFound:c};m(x),d().fire({icon:i>0?o>0||c>0?"warning":"success":"error",title:"Update Complete",html:'\n          <div class="text-left">\n            <p><strong>Update Summary:</strong></p>\n            <ul>\n              <li class="text-green-600">✓ Successfully updated: '.concat(i,' users</li>\n              <li class="text-yellow-600">⚠ Not found: ').concat(c,' users</li>\n              <li class="text-red-600">✗ Failed: ').concat(o," users</li>\n            </ul>\n            ").concat(h.length>0?"<br><p><strong>First 5 errors:</strong></p><ul>".concat(h.slice(0,5).map(e=>'<li class="text-red-600 text-sm">'.concat(e,"</li>")).join(""),"</ul>"):"","\n          </div>\n        "),timer:o>0?void 0:5e3,showConfirmButton:o>0})}catch(e){console.error("Error updating users:",e),d().fire({icon:"error",title:"Update Failed",text:e.message||"Failed to update users. Please try again."})}finally{u(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Simple User Update"}),(0,s.jsx)("p",{className:"text-white/80",children:"Update user videos, wallet balance, and active days via CSV"})]}),(0,s.jsxs)(i(),{href:"/admin/users",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload CSV File"]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample File"}),(0,s.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalVideos,walletBalance,activeDays\<EMAIL>,100,500,15\<EMAIL>,250,1200,25\<EMAIL>,75,300,5"],{type:"text/csv"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="simple-upload-sample.csv",a.click(),URL.revokeObjectURL(t)},className:"btn-secondary text-sm",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select CSV File"}),(0,s.jsx)("input",{type:"file",accept:".csv,.txt",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&(x(a),f([]),v(!1),m(null))},className:"form-input"})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{onClick:b,disabled:!w||c,className:"btn-secondary",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,s.jsx)("button",{onClick:j,disabled:!w||c||!g,className:"btn-primary",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Updating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Update Users"]})})]})})]}),g&&p.length>0&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 10 Records)"]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-white",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-white/20",children:[(0,s.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Add Videos"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Add Wallet (₹)"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Set Active Days"})]})}),(0,s.jsx)("tbody",{children:p.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b border-white/10",children:[(0,s.jsx)("td",{className:"p-2",children:e.email}),(0,s.jsxs)("td",{className:"p-2",children:["+",e.totalVideos]}),(0,s.jsxs)("td",{className:"p-2",children:["+₹",e.walletBalance]}),(0,s.jsx)("td",{className:"p-2",children:e.activeDays})]},t))})]})})]}),h&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Update Results"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:h.success}),(0,s.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Updated"})]}),(0,s.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:h.notFound}),(0,s.jsx)("div",{className:"text-yellow-300 text-sm",children:"Users Not Found"})]}),(0,s.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:h.failed}),(0,s.jsx)("div",{className:"text-red-300 text-sm",children:"Failed Updates"})]})]}),h.errors.length>0&&(0,s.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,s.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[h.errors.slice(0,10).map((e,t)=>(0,s.jsxs)("li",{children:["• ",e]},t)),h.errors.length>10&&(0,s.jsxs)("li",{className:"text-red-400",children:["... and ",h.errors.length-10," more errors"]})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Instructions"]}),(0,s.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"CSV Format:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"email:"})," User's email address (must exist in system)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"totalVideos:"})," Number of videos to ADD to current count"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"walletBalance:"})," Amount to ADD to current wallet balance"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"activeDays:"})," Active days to SET (replace current value)"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Videos and wallet values are ADDED to existing data"}),(0,s.jsx)("li",{children:"Active days value REPLACES the current active days"}),(0,s.jsx)("li",{children:"Users must already exist in the system"}),(0,s.jsx)("li",{children:"Email addresses are case-insensitive"}),(0,s.jsx)("li",{children:"Use comma or tab as delimiter"}),(0,s.jsx)("li",{children:"First row must be headers: email,totalVideos,walletBalance,activeDays"})]})]})]})]})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>n});var s=a(3915),l=a(3004),r=a(5317),i=a(858);let o=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),n=(0,l.xI)(o),c=(0,r.aU)(o);(0,i.c7)(o)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>n,hD:()=>o,wC:()=>c});var s=a(2115),l=a(3004),r=a(6104),i=a(12);function o(){let[e,t]=(0,s.useState)(null),[a,o]=(0,s.useState)(!0);(0,s.useEffect)(()=>{try{let e=(0,l.hg)(r.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let n=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:n}}function n(){let{user:e,loading:t}=o();return(0,s.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[a,l]=(0,s.useState)(!1),[r,i]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");l(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||r,isAdmin:a}}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,Pn:()=>o,TK:()=>m,getWithdrawals:()=>h,hG:()=>w,lo:()=>n,nQ:()=>u,updateWithdrawalStatus:()=>x,x5:()=>c});var s=a(5317),l=a(6104),r=a(3592);let i=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),o=await (0,s.getDocs)((0,s.collection)(l.db,r.COLLECTIONS.users)),n=o.size,c=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.users),(0,s._M)(r.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,s.getDocs)(c)).size,u=0,h=0,m=0,w=0;o.forEach(e=>{var a;let s=e.data();u+=s[r.FIELD_NAMES.totalVideos]||0,h+=s[r.FIELD_NAMES.wallet]||0;let l=null==(a=s[r.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();l&&l.toDateString()===t.toDateString()&&(m+=s[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.transactions),(0,s._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var a;let s=e.data(),l=null==(a=s[r.FIELD_NAMES.date])?void 0:a.toDate();l&&l>=t&&(w+=s[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let x=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),p=(await (0,s.getDocs)(x)).size,f=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),g=(await (0,s.getDocs)(f)).size,v={totalUsers:n,totalVideos:u,totalEarnings:h,pendingWithdrawals:p,todayUsers:d,todayVideos:m,todayEarnings:w,todayWithdrawals:g};return i.set(e,{data:v,timestamp:Date.now()}),v}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let i=await (0,s.getDocs)(a);return{users:i.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),l=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||l.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(l.db,r.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.HM)(t),(0,s.AB)(e)));let i=await (0,s.getDocs)(a);return{withdrawals:i.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function m(e,t){try{await (0,s.mZ)((0,s.H9)(l.db,r.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function w(e){try{await (0,s.kd)((0,s.H9)(l.db,r.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function x(e,t,o){try{let n=await (0,s.x7)((0,s.H9)(l.db,r.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=n.data(),h={status:t,updatedAt:s.Dc.now()};if(o&&(h.adminNotes=o),await (0,s.mZ)((0,s.H9)(l.db,r.COLLECTIONS.withdrawals,e),h),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(3489)),_N_E=e.O()}]);