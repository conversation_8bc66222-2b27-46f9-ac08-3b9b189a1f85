'use client'

import { useState } from 'react'
import { auth, db } from '@/lib/firebase'
import { signInAnonymously, signOut, createUserWithEmailAndPassword, deleteUser } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'

export default function TestFirebaseConnectivityPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const testConnectivity = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      addToResult('🌐 Testing Firebase Connectivity...\n')
      
      // Test 1: Check Firebase instances
      addToResult('=== TEST 1: Firebase Instances ===')
      addToResult(`Auth instance: ${auth ? '✅ Initialized' : '❌ Not initialized'}`)
      addToResult(`Firestore instance: ${db ? '✅ Initialized' : '❌ Not initialized'}`)
      addToResult(`Auth app: ${auth.app ? '✅ Connected' : '❌ Not connected'}`)
      addToResult(`Firestore app: ${db.app ? '✅ Connected' : '❌ Not connected'}`)
      
      // Test 2: Check environment variables
      addToResult('\n=== TEST 2: Environment Variables ===')
      addToResult(`API Key: ${process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '✅ Set' : '❌ Missing'}`)
      addToResult(`Auth Domain: ${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN ? '✅ Set' : '❌ Missing'}`)
      addToResult(`Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID ? '✅ Set' : '❌ Missing'}`)
      addToResult(`Project ID Value: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`)
      
      // Test 3: Test anonymous authentication (simpler than email/password)
      addToResult('\n=== TEST 3: Anonymous Authentication ===')
      try {
        addToResult('Attempting anonymous sign-in...')
        const userCredential = await signInAnonymously(auth)
        addToResult(`✅ Anonymous auth successful: ${userCredential.user.uid}`)
        
        // Test 4: Test Firestore write with anonymous user
        addToResult('\n=== TEST 4: Firestore Write Test ===')
        const testDoc = doc(db, 'connectivity_test', `test_${Date.now()}`)
        await setDoc(testDoc, {
          test: true,
          timestamp: Timestamp.now(),
          message: 'Connectivity test successful'
        })
        addToResult('✅ Firestore write successful')
        
        // Test 5: Test Firestore read
        addToResult('\n=== TEST 5: Firestore Read Test ===')
        const readDoc = await getDoc(testDoc)
        if (readDoc.exists()) {
          addToResult('✅ Firestore read successful')
          addToResult(`   Data: ${JSON.stringify(readDoc.data())}`)
        } else {
          addToResult('❌ Firestore read failed - document not found')
        }
        
        // Sign out
        await signOut(auth)
        addToResult('\n✅ Signed out successfully')
        
        addToResult('\n🎉 ALL TESTS PASSED - Firebase connectivity is working!')
        addToResult('The registration issue might be specific to email/password authentication.')
        
      } catch (authError: any) {
        addToResult(`❌ Anonymous auth failed: ${authError.message}`)
        addToResult(`   Error code: ${authError.code}`)
        
        if (authError.code === 'auth/network-request-failed') {
          addToResult('\n🔧 NETWORK CONNECTIVITY ISSUE DETECTED:')
          addToResult('   1. Check your internet connection')
          addToResult('   2. Check if firewall/antivirus is blocking Firebase')
          addToResult('   3. Try using a different network (mobile hotspot)')
          addToResult('   4. Check if your ISP blocks Firebase services')
          addToResult('   5. Try using a VPN')
          addToResult('')
          addToResult('   Firebase domains that need to be accessible:')
          addToResult('   - firebase.google.com')
          addToResult('   - firestore.googleapis.com')
          addToResult('   - identitytoolkit.googleapis.com')
          addToResult('   - mytube-india.firebaseapp.com')
        } else if (authError.code === 'auth/operation-not-allowed') {
          addToResult('\n🔧 FIREBASE CONFIGURATION ISSUE:')
          addToResult('   1. Go to Firebase Console → Authentication → Sign-in method')
          addToResult('   2. Enable "Anonymous" authentication')
          addToResult('   3. Or enable "Email/Password" authentication')
        }
      }
      
    } catch (error: any) {
      addToResult(`❌ Connectivity test failed: ${error.message}`)
      addToResult(`   Error code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testNetworkDiagnostics = async () => {
    setResult('')
    setIsLoading(true)

    try {
      addToResult('🔍 Network Diagnostics...\n')

      // Test 1: Basic fetch to Firebase
      addToResult('=== TEST 1: Firebase Domain Accessibility ===')
      try {
        const response = await fetch('https://firebase.google.com', { mode: 'no-cors' })
        addToResult('✅ Firebase.google.com is accessible')
      } catch (fetchError: any) {
        addToResult(`❌ Firebase.google.com not accessible: ${fetchError.message}`)
      }

      // Test 2: Project-specific domain
      addToResult('\n=== TEST 2: Project Domain Accessibility ===')
      try {
        const response = await fetch('https://instra-global.firebaseapp.com', { mode: 'no-cors' })
        addToResult('✅ instra-global.firebaseapp.com is accessible')
      } catch (fetchError: any) {
        addToResult(`❌ instra-global.firebaseapp.com not accessible: ${fetchError.message}`)
      }

      // Test 3: Firestore API endpoint
      addToResult('\n=== TEST 3: Firestore API Accessibility ===')
      try {
        const response = await fetch('https://firestore.googleapis.com', { mode: 'no-cors' })
        addToResult('✅ firestore.googleapis.com is accessible')
      } catch (fetchError: any) {
        addToResult(`❌ firestore.googleapis.com not accessible: ${fetchError.message}`)
      }

      addToResult('\n=== RECOMMENDATIONS ===')
      addToResult('If any domains are not accessible:')
      addToResult('1. Check your internet connection')
      addToResult('2. Try disabling firewall/antivirus temporarily')
      addToResult('3. Try using a different network (mobile hotspot)')
      addToResult('4. Contact your ISP about Firebase access')
      addToResult('5. Try using a VPN service')

    } catch (error: any) {
      addToResult(`❌ Network diagnostics failed: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testSpecificUID = async () => {
    setResult('')
    setIsLoading(true)

    try {
      addToResult('🔍 Testing Specific UID: b7690183-ab6b-4719-944d-c0a080a59e8c\n')

      // Test if this UID exists in Firestore
      addToResult('=== Checking if UID exists in Firestore ===')
      const specificUID = 'b7690183-ab6b-4719-944d-c0a080a59e8c'
      const userDocRef = doc(db, 'users', specificUID)

      try {
        const userDoc = await getDoc(userDocRef)
        if (userDoc.exists()) {
          const userData = userDoc.data()
          addToResult('✅ User document found!')
          addToResult(`   Name: ${userData.name || 'N/A'}`)
          addToResult(`   Email: ${userData.email || 'N/A'}`)
          addToResult(`   Plan: ${userData.plan || 'N/A'}`)
          addToResult(`   Referral Code: ${userData.referralCode || 'N/A'}`)
          addToResult(`   Status: ${userData.status || 'N/A'}`)
          addToResult(`   Joined: ${userData.joinedDate?.toDate()?.toLocaleString() || 'N/A'}`)

          addToResult('\n✅ This confirms Firestore is working!')
          addToResult('The registration issue might be specific to the registration flow.')
        } else {
          addToResult('❌ User document not found')
          addToResult('This UID might be from Firebase Auth but Firestore document creation failed')
        }
      } catch (firestoreError: any) {
        addToResult(`❌ Firestore query failed: ${firestoreError.message}`)
        addToResult(`   Error code: ${firestoreError.code}`)

        if (firestoreError.code === 'permission-denied') {
          addToResult('   This indicates Firestore security rules are blocking reads')
        } else if (firestoreError.code === 'unavailable') {
          addToResult('   This indicates Firestore service is unavailable')
        }
      }

    } catch (error: any) {
      addToResult(`❌ UID test failed: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testEmailPasswordAuth = async () => {
    setResult('')
    setIsLoading(true)

    let testUser: any = null

    try {
      addToResult('📧 Testing Email/Password Authentication...\n')

      // Test email/password authentication (same as registration)
      addToResult('=== Email/Password Authentication Test ===')
      const testEmail = `test${Date.now()}@example.com`
      const testPassword = 'test123456'

      addToResult(`Creating user with email: ${testEmail}`)

      try {
        const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
        testUser = userCredential.user
        addToResult(`✅ Email/Password auth successful: ${testUser.uid}`)
        addToResult(`   Email: ${testUser.email}`)
        addToResult(`   Email verified: ${testUser.emailVerified}`)

        // Test Firestore document creation
        addToResult('\n=== Testing Firestore Document Creation ===')
        const userDocRef = doc(db, 'users', testUser.uid)

        const userData = {
          name: 'Test User',
          email: testEmail.toLowerCase(),
          mobile: '9876543210',
          referralCode: `TEST${Date.now().toString().slice(-4)}`,
          plan: 'Trial',
          joinedDate: Timestamp.now(),
          wallet: 0,
          status: 'active'
        }

        addToResult('Attempting Firestore document creation...')
        await setDoc(userDocRef, userData)
        addToResult('✅ Firestore document created successfully')

        // Verify document
        const verifyDoc = await getDoc(userDocRef)
        if (verifyDoc.exists()) {
          addToResult('✅ Document verification successful')
          const docData = verifyDoc.data()
          addToResult(`   Name: ${docData.name}`)
          addToResult(`   Email: ${docData.email}`)
          addToResult(`   Plan: ${docData.plan}`)

          addToResult('\n🎉 SUCCESS: Email/Password registration flow works!')
          addToResult('The registration issue might be in the form validation or error handling.')
        } else {
          addToResult('❌ Document verification failed')
        }

      } catch (authError: any) {
        addToResult(`❌ Email/Password auth failed: ${authError.message}`)
        addToResult(`   Error code: ${authError.code}`)

        if (authError.code === 'auth/operation-not-allowed') {
          addToResult('\n🔧 EMAIL/PASSWORD AUTHENTICATION DISABLED:')
          addToResult('   1. Go to Firebase Console → Authentication → Sign-in method')
          addToResult('   2. Find "Email/Password" in the list')
          addToResult('   3. Click "Enable" and save')
          addToResult('   4. Make sure both "Email/Password" and "Email link" are enabled')
        } else if (authError.code === 'auth/network-request-failed') {
          addToResult('\n🔧 NETWORK ISSUE DETECTED:')
          addToResult('   The original network issue is still present')
          addToResult('   Try the network diagnostic solutions')
        }
      }

    } catch (error: any) {
      addToResult(`❌ Email/Password test failed: ${error.message}`)
      addToResult(`   Error code: ${error.code}`)
    } finally {
      // Cleanup
      if (testUser) {
        try {
          addToResult('\n=== Cleanup ===')
          await deleteUser(testUser)
          addToResult('✅ Test user deleted')
        } catch (deleteError: any) {
          addToResult(`⚠️ User deletion failed: ${deleteError.message}`)
        }
      }

      try {
        await signOut(auth)
        addToResult('✅ Signed out')
      } catch (signOutError: any) {
        addToResult(`⚠️ Sign out failed: ${signOutError.message}`)
      }

      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Firebase Connectivity Test</h1>
        
        <div className="glass-card p-6 mb-6">
          <div className="flex flex-wrap gap-4 mb-4">
            <button
              onClick={testConnectivity}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Testing...' : 'Test Firebase Connectivity'}
            </button>

            <button
              onClick={testNetworkDiagnostics}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Testing...' : 'Test Network Diagnostics'}
            </button>

            <button
              onClick={testSpecificUID}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Testing...' : 'Test Specific UID'}
            </button>

            <button
              onClick={testEmailPasswordAuth}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Testing...' : 'Test Email/Password Auth'}
            </button>
          </div>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Click a test button to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
