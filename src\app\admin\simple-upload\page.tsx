'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { updateUser } from '@/lib/adminDataService'
import { searchUsers } from '@/lib/adminDataService'
import Swal from 'sweetalert2'

interface SimpleUploadData {
  email: string
  totalTranslations: number
  walletBalance: number
  activeDays: number
}

interface UploadResult {
  success: number
  failed: number
  errors: string[]
  notFound: number
}

export default function AdminSimpleUploadPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewData, setPreviewData] = useState<SimpleUploadData[]>([])
  const [showPreview, setShowPreview] = useState(false)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setPreviewData([])
      setShowPreview(false)
      setUploadResult(null)
    }
  }

  const handlePreviewFile = async () => {
    if (!selectedFile) return

    try {
      setIsUploading(true)
      const text = await selectedFile.text()
      
      // Parse CSV
      const lines = text.split('\n').filter(line => line.trim())
      if (lines.length < 2) {
        throw new Error('CSV file must have at least a header row and one data row')
      }

      // Detect delimiter (comma or tab)
      const firstLine = lines[0]
      const delimiter = firstLine.includes('\t') ? '\t' : ','

      const headers = firstLine.split(delimiter).map(h => h.trim().replace(/"/g, '').toLowerCase())
      
      // Validate headers
      const requiredHeaders = ['email', 'totaltranslations', 'walletbalance', 'activedays']
      const missingHeaders = requiredHeaders.filter(header =>
        !headers.some(h => h.includes(header.replace('totaltranslations', 'translations').replace('walletbalance', 'wallet').replace('activedays', 'active')))
      )

      if (missingHeaders.length > 0) {
        throw new Error(`Missing required columns. Expected: email, totalTranslations (or translations), walletBalance (or wallet), activeDays (or active)`)
      }

      const data: SimpleUploadData[] = lines.slice(1).map((line, index) => {
        const values = line.split(delimiter).map(v => v.trim().replace(/"/g, ''))
        const obj: any = {}
        headers.forEach((header, headerIndex) => {
          obj[header] = values[headerIndex] || ''
        })

        // Map to standard format
        const email = obj.email || ''
        const totalTranslations = parseInt(obj.totaltranslations || obj.translations || obj.totalTranslations || '0') || 0
        const walletBalance = parseFloat(obj.walletbalance || obj.wallet || obj.walletBalance || '0') || 0
        const activeDays = parseInt(obj.activedays || obj.active || obj.activeDays || '0') || 0

        if (!email) {
          throw new Error(`Row ${index + 2}: Email is required`)
        }

        if (!email.includes('@')) {
          throw new Error(`Row ${index + 2}: Invalid email format`)
        }

        return {
          email,
          totalTranslations,
          walletBalance,
          activeDays
        }
      })

      setPreviewData(data.slice(0, 10)) // Show first 10 for preview
      setShowPreview(true)

    } catch (error: any) {
      console.error('Error previewing file:', error)
      Swal.fire({
        icon: 'error',
        title: 'Preview Failed',
        text: error.message || 'Failed to preview file. Please check the format.',
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleUploadData = async () => {
    if (!selectedFile) return

    const result = await Swal.fire({
      icon: 'question',
      title: 'Confirm Data Upload',
      html: `
        <div class="text-left">
          <p><strong>Are you sure you want to update user data from this file?</strong></p>
          <br>
          <p>This will:</p>
          <ul>
            <li>Find users by email address</li>
            <li>Add to their existing total translations count</li>
            <li>Add to their existing wallet balance</li>
            <li>SET their active days to the specified value</li>
            <li>Skip users not found in the system</li>
          </ul>
          <br>
          <p class="text-yellow-600"><strong>Note:</strong> Translations and wallet will be ADDED, but active days will be SET (replaced)!</p>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Yes, Update Users',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#dc2626'
    })

    if (!result.isConfirmed) return

    try {
      setIsUploading(true)
      setUploadResult(null)

      // Show progress modal
      Swal.fire({
        title: 'Updating Users',
        html: `
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p>Processing user updates...</p>
            <p class="text-sm text-gray-600 mt-2">Please wait...</p>
          </div>
        `,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false
      })

      // Parse the full file
      const text = await selectedFile.text()
      const lines = text.split('\n').filter(line => line.trim())
      const firstLine = lines[0]
      const delimiter = firstLine.includes('\t') ? '\t' : ','
      const headers = firstLine.split(delimiter).map(h => h.trim().replace(/"/g, '').toLowerCase())

      const allData: SimpleUploadData[] = lines.slice(1).map(line => {
        const values = line.split(delimiter).map(v => v.trim().replace(/"/g, ''))
        const obj: any = {}
        headers.forEach((header, headerIndex) => {
          obj[header] = values[headerIndex] || ''
        })

        return {
          email: obj.email || '',
          totalTranslations: parseInt(obj.totaltranslations || obj.translations || obj.totalTranslations || '0') || 0,
          walletBalance: parseFloat(obj.walletbalance || obj.wallet || obj.walletBalance || '0') || 0,
          activeDays: parseInt(obj.activedays || obj.active || obj.activeDays || '0') || 0
        }
      }).filter(item => item.email)

      let success = 0
      let failed = 0
      let notFound = 0
      const errors: string[] = []

      // Process each user
      for (const userData of allData) {
        try {
          // Find user by email
          const users = await searchUsers(userData.email)
          const foundUser = users.find((u: any) => u.email?.toLowerCase() === userData.email.toLowerCase())

          if (!foundUser) {
            notFound++
            errors.push(`User not found: ${userData.email}`)
            continue
          }

          // Get wallet and translation data separately
          const { getWalletData, getVideoCountData: getTranslationCountData } = await import('@/lib/dataService')
          const [walletData, translationData] = await Promise.all([
            getWalletData(foundUser.id),
            getTranslationCountData(foundUser.id)
          ])

          // Update user data (add to existing values for translations/wallet, set active days)
          const updateData = {
            totalTranslations: (translationData.totalTranslations || 0) + userData.totalTranslations,
            wallet: (walletData.wallet || 0) + userData.walletBalance,
            activeDays: userData.activeDays // Set active days to the specified value
          }

          await updateUser(foundUser.id, updateData)
          success++

        } catch (error: any) {
          failed++
          errors.push(`Failed to update ${userData.email}: ${error.message}`)
        }
      }

      // Close progress modal
      Swal.close()
      
      const result: UploadResult = { success, failed, errors, notFound }
      setUploadResult(result)

      // Show results
      Swal.fire({
        icon: success > 0 ? (failed > 0 || notFound > 0 ? 'warning' : 'success') : 'error',
        title: 'Update Complete',
        html: `
          <div class="text-left">
            <p><strong>Update Summary:</strong></p>
            <ul>
              <li class="text-green-600">✓ Successfully updated: ${success} users</li>
              <li class="text-yellow-600">⚠ Not found: ${notFound} users</li>
              <li class="text-red-600">✗ Failed: ${failed} users</li>
            </ul>
            ${errors.length > 0 ? `<br><p><strong>First 5 errors:</strong></p><ul>${errors.slice(0, 5).map(error => `<li class="text-red-600 text-sm">${error}</li>`).join('')}</ul>` : ''}
          </div>
        `,
        timer: failed > 0 ? undefined : 5000,
        showConfirmButton: failed > 0
      })

    } catch (error: any) {
      console.error('Error updating users:', error)
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: error.message || 'Failed to update users. Please try again.',
      })
    } finally {
      setIsUploading(false)
    }
  }

  const downloadSampleCSV = () => {
    const sampleData = [
      'email,totalTranslations,walletBalance,activeDays',
      '<EMAIL>,100,500,15',
      '<EMAIL>,250,1200,25',
      '<EMAIL>,75,300,5'
    ].join('\n')

    const blob = new Blob([sampleData], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'simple-upload-sample.csv'
    a.click()
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Simple User Update</h1>
            <p className="text-white/80">Update user translations, wallet balance, and active days via CSV</p>
          </div>
          <Link href="/admin/users" className="btn-secondary">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Users
          </Link>
        </div>

        {/* Upload Form */}
        <div className="glass-card p-6 mb-6">
          <h2 className="text-xl font-bold text-white mb-4">
            <i className="fas fa-upload mr-2"></i>
            Upload CSV File
          </h2>

          {/* Sample Download */}
          <div className="mb-4">
            <label className="block text-white font-medium mb-2">Sample File</label>
            <button
              onClick={downloadSampleCSV}
              className="btn-secondary text-sm"
            >
              <i className="fas fa-download mr-2"></i>
              Download Sample CSV
            </button>
          </div>

          {/* File Upload */}
          <div className="mb-4">
            <label className="block text-white font-medium mb-2">Select CSV File</label>
            <input
              type="file"
              accept=".csv,.txt"
              onChange={handleFileSelect}
              className="form-input"
            />
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex space-x-4">
              <button
                onClick={handlePreviewFile}
                disabled={!selectedFile || isUploading}
                className="btn-secondary"
              >
                {isUploading ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-eye mr-2"></i>
                    Preview Data
                  </>
                )}
              </button>

              <button
                onClick={handleUploadData}
                disabled={!selectedFile || isUploading || !showPreview}
                className="btn-primary"
              >
                {isUploading ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <i className="fas fa-upload mr-2"></i>
                    Update Users
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Data Preview */}
        {showPreview && previewData.length > 0 && (
          <div className="glass-card p-6 mb-6">
            <h2 className="text-xl font-bold text-white mb-4">
              <i className="fas fa-table mr-2"></i>
              Data Preview (First 10 Records)
            </h2>

            <div className="overflow-x-auto">
              <table className="w-full text-white">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">Add Translations</th>
                    <th className="text-left p-2">Add Wallet (₹)</th>
                    <th className="text-left p-2">Set Active Days</th>
                  </tr>
                </thead>
                <tbody>
                  {previewData.map((item, index) => (
                    <tr key={index} className="border-b border-white/10">
                      <td className="p-2">{item.email}</td>
                      <td className="p-2">+{item.totalTranslations}</td>
                      <td className="p-2">+₹{item.walletBalance}</td>
                      <td className="p-2">{item.activeDays}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Upload Result */}
        {uploadResult && (
          <div className="glass-card p-6 mb-6">
            <h2 className="text-xl font-bold text-white mb-4">
              <i className="fas fa-chart-bar mr-2"></i>
              Update Results
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                <div className="text-green-400 text-2xl font-bold">{uploadResult.success}</div>
                <div className="text-green-300 text-sm">Successfully Updated</div>
              </div>

              <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
                <div className="text-yellow-400 text-2xl font-bold">{uploadResult.notFound}</div>
                <div className="text-yellow-300 text-sm">Users Not Found</div>
              </div>

              <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
                <div className="text-red-400 text-2xl font-bold">{uploadResult.failed}</div>
                <div className="text-red-300 text-sm">Failed Updates</div>
              </div>
            </div>

            {uploadResult.errors.length > 0 && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                <h3 className="text-red-400 font-bold mb-2">Errors:</h3>
                <ul className="text-red-300 text-sm space-y-1">
                  {uploadResult.errors.slice(0, 10).map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                  {uploadResult.errors.length > 10 && (
                    <li className="text-red-400">... and {uploadResult.errors.length - 10} more errors</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="glass-card p-6">
          <h2 className="text-xl font-bold text-white mb-4">
            <i className="fas fa-info-circle mr-2"></i>
            Instructions
          </h2>

          <div className="text-white/80 space-y-4">
            <div>
              <h3 className="font-bold text-white mb-2">CSV Format:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li><strong>email:</strong> User's email address (must exist in system)</li>
                <li><strong>totalTranslations:</strong> Number of translations to ADD to current count</li>
                <li><strong>walletBalance:</strong> Amount to ADD to current wallet balance</li>
                <li><strong>activeDays:</strong> Active days to SET (replace current value)</li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-white mb-2">Important Notes:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>Translations and wallet values are ADDED to existing data</li>
                <li>Active days value REPLACES the current active days</li>
                <li>Users must already exist in the system</li>
                <li>Email addresses are case-insensitive</li>
                <li>Use comma or tab as delimiter</li>
                <li>First row must be headers: email,totalTranslations,walletBalance,activeDays</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
