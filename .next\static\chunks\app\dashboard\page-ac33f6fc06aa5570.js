(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return n}});let a=s(8229),l=s(8883),i=s(3063),r=a._(s(1193));function n(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=i.Image},3282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(5155),l=s(2115),i=s(6874),r=s.n(i),n=s(6766),c=s(6681),o=s(7460),d=s(3592),x=s(12);function m(e){let{userId:t,isOpen:s,onClose:i}=e,[r,n]=(0,l.useState)([]),[c,o]=(0,l.useState)(!0);(0,l.useEffect)(()=>{s&&t&&x()},[s,t]);let x=async()=>{try{o(!0);let e=await (0,d.Ss)(t,20);n(e)}catch(e){console.error("Error loading notifications:",e)}finally{o(!1)}},m=e=>{e.id&&!(0,d.mv)(e.id,t)&&((0,d.bA)(e.id,t),n([...r]))},h=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},u=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-2"}),"Notifications"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:x,disabled:c,className:"text-gray-500 hover:text-gray-700 transition-colors p-1",title:"Refresh notifications",children:(0,a.jsx)("i",{className:"fas fa-sync-alt ".concat(c?"animate-spin":"")})}),(0,a.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[60vh]",children:c?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"spinner w-8 h-8"})}):0===r.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No notifications yet"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"You'll see important updates here"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:r.map(e=>{let s=!!e.id&&(0,d.mv)(e.id,t);return(0,a.jsx)("div",{onClick:()=>m(e),className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors ".concat(s?"":"bg-blue-50 border-l-4 border-l-blue-500"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,a.jsx)("i",{className:h(e.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium ".concat(s?"text-gray-700":"text-gray-900"),children:e.title}),!s&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-sm mt-1 ".concat(s?"text-gray-600":"text-gray-800"),children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:u(e.createdAt)})]})]})},e.id)})})}),r.length>0&&(0,a.jsx)("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:(0,a.jsx)("button",{onClick:()=>{r.forEach(e=>{e.id&&!(0,d.mv)(e.id,t)&&(0,d.bA)(e.id,t)}),n([...r])},className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Mark all as read"})})]})}):null}function h(e){let{userId:t,onClick:s}=e,[i,r]=(0,l.useState)([]),[n,c]=(0,l.useState)(0),[o,x]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if(t){m();let e=setInterval(m,15e3);return()=>clearInterval(e)}},[t]);let m=async()=>{try{x(!0);let e=await (0,d.Ss)(t,20);r(e);let s=(0,d.ul)(e,t);if(s>n&&n>0){let e=document.querySelector(".notification-bell");e&&(e.classList.add("animate-bounce"),setTimeout(()=>{e.classList.remove("animate-bounce")},1e3))}c(s),console.log("Loaded ".concat(e.length," notifications, ").concat(s," unread"))}catch(e){console.error("Error loading notifications for bell:",e)}finally{x(!1)}};return(0,a.jsxs)("button",{onClick:s,className:"relative p-2 text-white hover:text-yellow-300 transition-colors",title:"".concat(n," unread notifications"),children:[(0,a.jsx)("i",{className:"fas fa-bell text-xl notification-bell ".concat(o?"animate-pulse":"")}),n>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse",children:n>9?"9+":n}),o&&(0,a.jsx)("span",{className:"absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-sync-alt text-xs animate-spin"})})]})}var u=s(8647),f=s(4752),p=s.n(f);function g(e){let{userId:t,currentMonth:i,usedLeaves:r,maxLeaves:n,onLeaveCountChange:c}=e,[o,d]=(0,l.useState)([]),[x,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)(!1),[f,g]=(0,l.useState)(""),[b,v]=(0,l.useState)({date:"",reason:""});(0,l.useEffect)(()=>{N(),j()},[t]);let j=async()=>{try{let{getUserData:e,getPlanValidityDays:a}=await Promise.resolve().then(s.bind(s,3592)),l=await e(t);if(l){let e,t=new Date;if("Trial"===l.plan){let t=l.joinedDate||new Date;e=new Date(t.getTime()+1728e5)}else if(l.planExpiry)e=l.planExpiry;else{let s=a(l.plan),i=l.activeDays||1,r=Math.max(0,s-i);e=new Date(t.getTime()+24*r*36e5)}let s=new Date(t.getTime()+2592e6),i=e<s?e:s;g(i.toISOString().split("T")[0])}}catch(t){console.error("Error calculating max date:",t);let e=new Date;e.setDate(e.getDate()+30),g(e.toISOString().split("T")[0])}},N=async()=>{try{let{getUserLeaves:e}=await s.e(9567).then(s.bind(s,9567)),a=await e(t);d(a)}catch(e){console.error("Error loading user leaves:",e),d([])}},w=async()=>{try{if(!b.date||!b.reason.trim())return void p().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let e=new Date(b.date),a=new Date;a.setHours(0,0,0,0);let l=new Date(a);if(l.setDate(l.getDate()+1),e<=a)return void p().fire({icon:"error",title:"Invalid Date",text:"Cannot apply leave for today or past dates. Please select a future date."});try{let{isUserPlanExpired:l}=await Promise.resolve().then(s.bind(s,3592));if((await l(t)).expired)return void p().fire({icon:"error",title:"Plan Expired",text:"Your plan has expired. Cannot apply for leave."});let{getUserData:i,getPlanValidityDays:r}=await Promise.resolve().then(s.bind(s,3592)),n=await i(t);if(n){let t;if("Trial"===n.plan){let e=n.joinedDate||new Date;t=new Date(e.getTime()+1728e5)}else if(n.planExpiry)t=n.planExpiry;else{let e=r(n.plan),s=n.activeDays||1,l=Math.max(0,e-s);t=new Date(a.getTime()+24*l*36e5)}if(e>t)return void p().fire({icon:"error",title:"Date Outside Plan Period",text:"Cannot apply leave beyond your plan expiry date (".concat(t.toLocaleDateString(),").")})}}catch(e){console.error("Error checking plan expiry:",e)}if(r>=n)return void p().fire({icon:"error",title:"Leave Limit Exceeded",text:"You have already used all ".concat(n," leaves for this month.")});if(o.find(t=>t.date.toDateString()===e.toDateString()))return void p().fire({icon:"error",title:"Duplicate Application",text:"You have already applied for leave on this date."});u(!0);let{applyUserLeave:i}=await s.e(9567).then(s.bind(s,9567)),d=await i({userId:t,date:e,reason:b.reason.trim()});await N(),c&&c(),d.autoApproved?p().fire({icon:"success",title:"✅ Leave Auto-Approved!",html:'\n            <div class="text-left">\n              <p><strong>Your leave has been automatically approved!</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(b.reason.trim(),'</p>\n              <br>\n              <p class="text-green-600"><strong>Leave Quota:</strong> ').concat(d.usedLeaves,"/").concat(d.maxLeaves,' used this month</p>\n              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>\n            </div>\n          '),timer:6e3,showConfirmButton:!0,confirmButtonText:"Great!"}):p().fire({icon:"warning",title:"⏳ Leave Pending Approval",html:'\n            <div class="text-left">\n              <p><strong>Your leave application has been submitted.</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(b.reason.trim(),'</p>\n              <br>\n              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>\n              <p class="text-gray-600"><strong>Leave Quota:</strong> ').concat(d.maxLeaves,"/").concat(d.maxLeaves," used this month</p>\n            </div>\n          "),timer:6e3,showConfirmButton:!0,confirmButtonText:"Understood"}),v({date:"",reason:""}),m(!1)}catch(e){console.error("Error applying leave:",e),p().fire({icon:"error",title:"Application Failed",text:"Failed to apply for leave. Please try again."})}finally{u(!1)}},y=async e=>{try{let t=o.find(t=>t.id===e);if(!t||"pending"!==t.status)return void p().fire({icon:"error",title:"Cannot Cancel",text:"Only pending leave applications can be cancelled."});if((await p().fire({title:"Cancel Leave Application",text:"Are you sure you want to cancel this leave application?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Cancel",cancelButtonText:"Keep Application"})).isConfirmed){let{cancelUserLeave:t}=await s.e(9567).then(s.bind(s,9567));await t(e),await N(),c&&c(),p().fire({icon:"success",title:"Application Cancelled",text:"Your leave application has been cancelled.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error cancelling leave:",e),p().fire({icon:"error",title:"Cancellation Failed",text:"Failed to cancel leave application. Please try again."})}},k=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},C=(e,t)=>{switch(e){case"approved":return"system"===t?"fas fa-magic text-green-500":"fas fa-check-circle text-green-500";case"rejected":return"fas fa-times-circle text-red-500";case"pending":return"fas fa-clock text-yellow-500";default:return"fas fa-question-circle text-gray-500"}},S=n-r;return(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-2"}),"Leave Management"]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm text-white/80",children:[i," Leaves"]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:[S,"/",n," Available"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-400",children:n}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Monthly Quota"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-yellow-400",children:r}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Used"})]}),(0,a.jsxs)("div",{className:"bg-blue-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-blue-400",children:S}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Remaining"})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>m(!0),disabled:S<=0,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),S>0?"Apply for Leave":"No Leaves Available"]})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white/80",children:"Recent Applications"}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-4 text-white/60",children:[(0,a.jsx)("i",{className:"fas fa-calendar-check text-2xl mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No leave applications yet"})]}):(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:o.slice(-5).reverse().map(e=>(0,a.jsx)("div",{className:"bg-white/10 p-3 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:e.date.toLocaleDateString()}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat(k(e.status)),children:[(0,a.jsx)("i",{className:"".concat(C(e.status,e.reviewedBy)," mr-1")}),e.status.charAt(0).toUpperCase()+e.status.slice(1),"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsx)("span",{className:"ml-1",title:"Auto-approved",children:"⚡"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-white/70 mt-1",children:[e.reason,"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-magic mr-1"}),"Auto-approved (within quota)"]}),e.reviewNotes&&"system"!==e.reviewedBy&&(0,a.jsxs)("div",{className:"text-xs text-blue-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-comment mr-1"}),e.reviewNotes]})]})]}),"pending"===e.status&&(0,a.jsx)("button",{onClick:()=>y(e.id),className:"text-red-400 hover:text-red-300 text-sm",children:(0,a.jsx)("i",{className:"fas fa-times"})})]})},e.id))})]}),x&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Apply for Leave"}),(0,a.jsx)("button",{onClick:()=>m(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),f&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["Leave applications are allowed until ",new Date(f).toLocaleDateString()]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,a.jsx)("input",{type:"date",value:b.date,onChange:e=>v(t=>({...t,date:e.target.value})),min:(()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})(),max:f,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave can only be applied for future dates within your plan period"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,a.jsx)("textarea",{value:b.reason,onChange:e=>v(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"You have ",S," leave(s) remaining for ",i,"."]}),S>0&&(0,a.jsxs)("div",{className:"text-sm text-green-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("strong",{children:"Auto-Approval:"})," Your leave will be automatically approved since you have available quota."]}),S<=0&&(0,a.jsxs)("div",{className:"text-sm text-orange-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),(0,a.jsx)("strong",{children:"Manual Review:"})," Leave will require admin approval as quota is exceeded."]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,a.jsx)("button",{onClick:()=>m(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,a.jsx)("button",{onClick:w,disabled:h,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Applying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Apply Leave"]})})]})]})})]})}var b=s(8926);function v(){let{user:e,loading:t}=(0,c.Nu)(),{hasBlockingNotifications:i,isChecking:f,markAllAsRead:p}=(0,o.J)((null==e?void 0:e.uid)||null),[v,j]=(0,l.useState)(null),[N,w]=(0,l.useState)(null),[y,k]=(0,l.useState)(null),[C,S]=(0,l.useState)(!0),[D,A]=(0,l.useState)(!1),[L,E]=(0,l.useState)(0);(0,l.useEffect)(()=>{e&&T()},[e]);let T=async()=>{try{S(!0);let[t,s,a]=await Promise.all([(0,d.getUserData)(e.uid),(0,d.getWalletData)(e.uid),(0,d.getVideoCountData)(e.uid),I()]);j(t),w(s),k(a)}catch(e){console.error("Error loading dashboard data:",e)}finally{S(!1)}},I=async()=>{try{let{getUserMonthlyLeaveCount:t}=await s.e(9567).then(s.bind(s,9567)),a=new Date,l=a.getFullYear(),i=a.getMonth()+1,r=await t(e.uid,l,i);return E(r),console.log("User ".concat(e.uid," has used ").concat(r," leaves this month")),r}catch(e){return console.error("Error loading user leave count:",e),E(0),0}};return t||C||f?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":f?"Checking notifications...":"Loading dashboard..."})]})}):i&&e?(0,a.jsx)(u.A,{userId:e.uid,onAllRead:p}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:40,height:40,className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Instra Global Dashboard"}),(0,a.jsxs)("p",{className:"text-white/80",children:["Welcome back, ",(null==v?void 0:v.name)||"User"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsx)(h,{userId:e.uid,onClick:()=>A(!0)}),(0,a.jsxs)("button",{onClick:()=>{(0,x._f)(null==e?void 0:e.uid,"/login")},className:"glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6",children:[(0,a.jsxs)(r(),{href:"/work",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-play-circle text-3xl text-youtube-red mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Watch Videos"})]}),(0,a.jsxs)(r(),{href:"/wallet",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-wallet text-3xl text-green-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Wallet"})]}),(0,a.jsxs)(r(),{href:"/transactions",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-history text-3xl text-orange-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Transactions"})]}),(0,a.jsxs)(r(),{href:"/refer",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-users text-3xl text-blue-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Refer & Earn"})]}),(0,a.jsxs)(r(),{href:"/profile",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-user text-3xl text-purple-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Profile"})]}),(0,a.jsxs)(r(),{href:"/plans",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-crown text-3xl text-yellow-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Plans"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-wallet mr-2"}),"Wallet Overview"]}),(0,a.jsxs)("div",{className:"bg-green-500/20 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"My Wallet"}),(0,a.jsxs)("p",{className:"text-4xl font-bold text-white mb-2",children:["₹",((null==N?void 0:N.wallet)||0).toFixed(2)]}),(0,a.jsx)("p",{className:"text-white/60",children:"Total available balance"}),(null==v?void 0:v.plan)==="Trial"&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,a.jsx)("span",{className:"text-red-400 font-medium text-sm",children:"Withdrawal Restricted"})]}),(0,a.jsx)("p",{className:"text-white/80 text-xs mb-3",children:"Trial users cannot withdraw funds. Upgrade to enable withdrawals."}),(0,a.jsxs)(r(),{href:"/plans",className:"btn-secondary text-xs px-3 py-1",children:[(0,a.jsx)("i",{className:"fas fa-arrow-up mr-1"}),"Upgrade Plan"]})]}),(0,a.jsxs)(r(),{href:"/wallet",className:"btn-primary mt-4 inline-block",children:[(0,a.jsx)("i",{className:"fas fa-eye mr-2"}),"View Details"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-video mr-2"}),"Today's Progress"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-youtube-red",children:(null==y?void 0:y.todayVideos)||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Videos Watched"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-400",children:(null==y?void 0:y.remainingVideos)||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Remaining"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-400",children:(null==y?void 0:y.totalVideos)||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Videos"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"bg-white/20 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-youtube-red h-3 rounded-full transition-all duration-300",style:{width:"".concat(y?y.todayVideos/50*100:0,"%")}})}),(0,a.jsxs)("p",{className:"text-white/80 text-sm mt-2 text-center",children:[(null==y?void 0:y.todayVideos)||0," / 50 videos completed today"]})]})]}),e&&(0,a.jsx)(g,{userId:e.uid,currentMonth:new Date().toLocaleDateString("en-US",{month:"long",year:"numeric"}),usedLeaves:L,maxLeaves:4,onLeaveCountChange:I}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-headset mr-2"}),"Need Help?"]}),(0,a.jsx)("p",{className:"text-white/60 mb-6",children:"Our support team is here to help you with any questions about earning, withdrawals, or your account."}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("a",{href:"https://wa.me/************",target:"_blank",rel:"noopener noreferrer",className:"flex items-center bg-green-500/20 border border-green-500/30 rounded-lg p-4 hover:bg-green-500/30 transition-colors",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-green-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:"WhatsApp Support"}),(0,a.jsx)("div",{className:"text-green-400 text-sm",children:"+91 **********"}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:"9 AM - 6 PM (Working days)"})]})]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-500/30 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-blue-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:"Email Support"}),(0,a.jsx)("div",{className:"text-blue-400 text-sm",children:"<EMAIL>"}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:"9 AM - 6 PM (Working days)"})]})]})]})]}),(0,a.jsx)(b.A,{variant:"dashboard",className:"mb-6"}),e&&(0,a.jsx)(m,{userId:e.uid,isOpen:D,onClose:()=>A(!1)})]})}},5604:(e,t,s)=>{Promise.resolve().then(s.bind(s,3282))},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>l.a});var a=s(1469),l=s.n(a)},8926:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(5155),l=s(2115),i=s(4752),r=s.n(i);function n(e){let{variant:t="homepage",className:s=""}=e,{isInstallable:i,isInstalled:n,installApp:c,getInstallInstructions:o}=function(){let[e,t]=(0,l.useState)(null),[s,a]=(0,l.useState)(!1),[i,r]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=e=>{e.preventDefault(),t(e),a(!0)},s=()=>{r(!0),a(!1),t(null)};return window.matchMedia("(display-mode: standalone)").matches&&r(!0),window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",s),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",s)}},[]),{isInstallable:s,isInstalled:i,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:s}=await e.userChoice;if("accepted"===s)return r(!0),a(!1),t(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[d,x]=(0,l.useState)(!1),m=async()=>{await c()?r().fire({icon:"success",title:"App Installed!",text:"MyTube has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):i||x(!0)},h=()=>{let e=o();r().fire({title:"Install MyTube on ".concat(e.browser),html:'\n        <div class="text-left">\n          <p class="mb-4 text-gray-600">Follow these steps to install MyTube as an app:</p>\n          <ol class="list-decimal list-inside space-y-2">\n            '.concat(e.steps.map(e=>'<li class="text-gray-700">'.concat(e,"</li>")).join(""),'\n          </ol>\n          <div class="mt-6 p-4 bg-blue-50 rounded-lg">\n            <p class="text-sm text-blue-800">\n              <i class="fas fa-info-circle mr-2"></i>\n              Installing the app gives you faster access, offline capabilities, and a native app experience!\n            </p>\n          </div>\n        </div>\n      '),confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})};return n?(0,a.jsx)("div",{className:"".concat(s),children:"homepage"===t?(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,a.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,a.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===t?(0,a.jsxs)("div",{className:"glass-card p-8 hover:scale-105 transition-transform ".concat(s),children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,a.jsx)("div",{className:"space-y-3",children:i?(0,a.jsxs)("button",{onClick:m,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,a.jsxs)("button",{onClick:h,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,a.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,a.jsx)("div",{className:"glass-card p-4 ".concat(s),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),i?(0,a.jsxs)("button",{onClick:m,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,a.jsxs)("button",{onClick:h,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,3592,1018,8441,1684,7358],()=>t(5604)),_N_E=e.O()}]);