(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[481],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>l,_f:()=>o});var n=a(6104),r=a(4752),s=a.n(r);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await s().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await n.j2.signOut(),s().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await n.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},452:(e,t,a)=>{Promise.resolve().then(a.bind(a,4871))},4871:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var n=a(5155),r=a(2115),s=a(6874),i=a.n(s),o=a(6681),l=a(4752),c=a.n(l);let d=[{id:"trial",name:"Trial",price:0,duration:2,earningPerTranslation:25,languages:1,description:"Explore and test your translation potential before handling real jobs",features:["2 days access","₹25 per 50 translations","Basic support","Learn the platform"]},{id:"junior",name:"Junior",price:2999,duration:30,earningPerTranslation:150,languages:1,description:"Entry-level role for new freelancers starting their translation journey",features:["30 days access","Certified for 1 Language","₹150 per 50 translations","Basic support","Translation training materials"]},{id:"senior",name:"Senior",price:5999,duration:30,earningPerTranslation:250,languages:3,description:"Senior role for experienced translators handling multiple Languages",features:["30 days access","Certified for 3 Languages","₹250 per 50 translations","Priority support","Advanced translation tools","Quality assurance training"],popular:!0},{id:"expert",name:"Expert",price:9999,duration:30,earningPerTranslation:400,languages:5,description:"Top-tier role for expert translators with broad multi-language proficiency",features:["30 days access","Certified for 5 Languages","₹400 per 50 translations","VIP support","Premium translation tools","Dedicated account manager","Exclusive high-value projects"]}];function u(){let{user:e,loading:t}=(0,o.hD)(),[a,s]=(0,r.useState)(null),[l,u]=(0,r.useState)(!1),g=async t=>{if(!e)return void c().fire({icon:"info",title:"Login Required",text:"Please login to purchase a plan",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&(window.location.href="/login")});if("trial"===t.id)return void c().fire({icon:"info",title:"Trial Plan",text:"You are already on the trial plan. Upgrade to a paid plan for better earnings!"});s(t.id),u(!0);try{await new Promise(e=>setTimeout(e,2e3)),c().fire({icon:"info",title:"Payment Integration Required",html:"\n          <p>To complete your purchase of the <strong>".concat(t.name,"</strong> plan (₹").concat(t.price,'), please contact our support team.</p>\n          <br>\n          <p><strong>Plan Details:</strong></p>\n          <ul style="text-align: left; margin: 10px 0;">\n            <li>Duration: ').concat(t.duration," days</li>\n            <li>Earning: ₹").concat(t.earningPerTranslation," per 50 translations</li>\n            <li>Languages: Certified for ").concat(t.languages," language").concat(t.languages>1?"s":"","</li>\n          </ul>\n          <br>\n          <p><strong>Contact Options:</strong></p>\n          <p>\uD83D\uDCE7 Email: <strong><EMAIL></strong></p>\n        "),confirmButtonText:"Contact Support",showCancelButton:!0,cancelButtonText:"Cancel"})}catch(e){console.error("Error processing plan selection:",e),c().fire({icon:"error",title:"Error",text:"Failed to process plan selection. Please try again."})}finally{u(!1),s(null)}};return t?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsx)("div",{className:"spinner"})}):(0,n.jsxs)("div",{className:"min-h-screen p-4",children:[(0,n.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)(i(),{href:e?"/dashboard":"/",className:"glass-button px-4 py-2 text-white",children:[(0,n.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back"]}),(0,n.jsx)("h1",{className:"text-xl font-bold text-white",children:"Choose Your Plan"}),(0,n.jsx)("div",{className:"w-20"})," "]})}),(0,n.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Start Earning with Instra Global"}),(0,n.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your translation career. Translate text and earn money with our professional certification levels."})]}),(0,n.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:d.map(e=>(0,n.jsxs)("div",{className:"glass-card p-6 relative ".concat(e.popular?"ring-2 ring-yellow-400":""),children:[e.popular&&(0,n.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,n.jsx)("span",{className:"bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold",children:"Most Popular"})}),(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:e.name}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("span",{className:"text-3xl font-bold text-white",children:["₹",e.price]}),e.price>0&&(0,n.jsxs)("span",{className:"text-white/60 ml-2",children:["/ ",e.duration," days"]})]}),(0,n.jsxs)("p",{className:"text-green-400 font-semibold text-sm",children:["Earn ₹",e.earningPerTranslation," per 50 translations"]}),(0,n.jsx)("p",{className:"text-white/60 text-xs mt-2",children:e.description})]}),(0,n.jsx)("ul",{className:"space-y-2 mb-6 text-sm",children:e.features.map((e,t)=>(0,n.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,n.jsx)("i",{className:"fas fa-check text-green-400 mr-2 text-xs"}),e]},t))}),(0,n.jsx)("button",{onClick:()=>g(e),disabled:l&&a===e.id,className:"w-full py-2 rounded-lg font-semibold transition-all duration-300 text-sm ".concat(e.popular?"bg-yellow-400 text-black hover:bg-yellow-500":0===e.price?"bg-gray-600 text-white hover:bg-gray-700":"bg-purple-600 text-white hover:bg-purple-700"," disabled:opacity-50"),children:l&&a===e.id?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Processing..."]}):0===e.price?"Start Free Trial":"Choose ".concat(e.name)})]},e.id))}),(0,n.jsxs)("div",{className:"mt-12 glass-card p-8",children:[(0,n.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:[(0,n.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Translation Plan Benefits"]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Earning Structure"}),(0,n.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,n.jsx)("li",{children:"• Complete 50 translations daily to earn the full amount"}),(0,n.jsx)("li",{children:"• Each translation must be accurate and complete"}),(0,n.jsx)("li",{children:"• Earnings are credited to your wallet"}),(0,n.jsx)("li",{children:"• Higher plans offer better earning rates"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Language Certification"}),(0,n.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,n.jsx)("li",{children:"• Higher plans certify you for more languages"}),(0,n.jsx)("li",{children:"• Access to specialized translation projects"}),(0,n.jsx)("li",{children:"• Professional translator recognition"}),(0,n.jsx)("li",{children:"• Quality assurance and training included"})]})]})]})]}),(0,n.jsxs)("div",{className:"mt-8 text-center",children:[(0,n.jsx)("p",{className:"text-white/60 mb-4",children:"Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):"}),(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-white hover:text-blue-400 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-envelope mr-2"}),"<EMAIL>"]})})]})]})]})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>l});var n=a(3915),r=a(3004),s=a(5317),i=a(858);let o=(0,n.Dk)().length?(0,n.Sx)():(0,n.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,r.xI)(o),c=(0,s.aU)(o);(0,i.c7)(o)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>l,hD:()=>o,wC:()=>c});var n=a(2115),r=a(3004),s=a(6104),i=a(12);function o(){let[e,t]=(0,n.useState)(null),[a,o]=(0,n.useState)(!0);(0,n.useEffect)(()=>{try{let e=(0,r.hg)(s.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let l=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:l}}function l(){let{user:e,loading:t}=o();return(0,n.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[a,r]=(0,n.useState)(!1),[s,i]=(0,n.useState)(!0);return(0,n.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||s,isAdmin:a}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,8441,1684,7358],()=>t(452)),_N_E=e.O()}]);