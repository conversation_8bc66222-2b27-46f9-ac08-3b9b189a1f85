(()=>{var e={};e.id=2116,e.ids=[2116],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>c,j2:()=>l});var s=t(67989),i=t(63385),o=t(75535),n=t(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,i.xI)(a),c=(0,o.aU)(a);(0,n.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42170:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),i=t(48088),o=t(88170),n=t.n(o),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let c={children:["",{children:["admin",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,76158)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51278:(e,r,t)=>{"use strict";t.d(r,{M4:()=>a,_f:()=>n});var s=t(33784),i=t(77567);function o(e){try{Object.keys(localStorage).forEach(r=>{(r.includes(e)||r.startsWith("video_session_")||r.startsWith("watch_times_")||r.startsWith("video_refresh_")||r.startsWith("video_change_notification_")||r.startsWith("leave_")||r.includes("mytube_")||r.includes("user_"))&&localStorage.removeItem(r)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e,r="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&o(e),await s.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=r}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function a(e,r="/login"){try{e&&o(e),await s.j2.signOut(),window.location.href=r}catch(e){console.error("Quick logout error:",e),window.location.href=r}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64248:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),i=t(43210),o=t(85814),n=t.n(o),a=t(30474),l=t(63385),c=t(33784),d=t(87979),u=t(77567);function p(){let{user:e,loading:r}=(0,d.hD)(),[t,o]=(0,i.useState)(""),[p,m]=(0,i.useState)(""),[x,h]=(0,i.useState)(!1),[f,g]=(0,i.useState)(!1),v=async e=>{if(e.preventDefault(),!t||!p)return void u.A.fire({icon:"error",title:"Error",text:"Please fill in all fields"});h(!0);try{let e=(await (0,l.x9)(c.j2,t,p)).user;if(!["<EMAIL>","<EMAIL>"].includes(e.email||""))throw await c.j2.signOut(),Error("Access denied. Admin privileges required.")}catch(r){console.error("Admin login error:",r);let e="An error occurred during login";if(r.message.includes("Access denied"))e="Access denied. Admin privileges required.";else switch(r.code){case"auth/user-not-found":e="No admin account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This admin account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=r.message||"Admin login failed"}u.A.fire({icon:"error",title:"Admin Login Failed",text:e}),m("")}finally{h(!1)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(a.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Panel"}),(0,s.jsx)("p",{className:"text-white/80",children:"Sign in to access admin dashboard"})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:[(0,s.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Admin Email"]}),(0,s.jsx)("input",{type:"email",id:"email",value:t,onChange:e=>o(e.target.value),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock mr-2"}),"Password"]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:f?"text":"password",id:"password",value:p,onChange:e=>m(e.target.value),className:"form-input pr-12",placeholder:"Enter admin password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>g(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:(0,s.jsx)("i",{className:`fas ${f?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:x,className:"w-full btn-primary flex items-center justify-center",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Signing in..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]})})]}),(0,s.jsx)("div",{className:"mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30",children:(0,s.jsxs)("div",{className:"flex items-center text-red-300",children:[(0,s.jsx)("i",{className:"fas fa-shield-alt mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:"This is a secure admin area. Only authorized personnel can access this panel."})]})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(n(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\login\\page.tsx","default")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,r,t)=>{"use strict";t.d(r,{Nu:()=>n,hD:()=>o,wC:()=>a});var s=t(43210);t(63385),t(33784);var i=t(51278);function o(){let[e,r]=(0,s.useState)(null),[t,o]=(0,s.useState)(!0),n=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:n}}function n(){let{user:e,loading:r}=o();return{user:e,loading:r}}function a(){let{user:e,loading:r}=o(),[t,i]=(0,s.useState)(!1),[n,a]=(0,s.useState)(!0);return{user:e,loading:r||n,isAdmin:t}}},90252:(e,r,t)=>{Promise.resolve().then(t.bind(t,76158))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96332:(e,r,t)=>{Promise.resolve().then(t.bind(t,64248))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6204,2756,7567,5901],()=>t(42170));module.exports=s})();