// Translation Manager for handling translation datasets with local storage batching

export interface TranslationData {
  id: string
  english: string
  hindi?: string
  spanish?: string
  french?: string
  german?: string
  italian?: string
  portuguese?: string
  russian?: string
  japanese?: string
  korean?: string
  chinese?: string
  arabic?: string
  dutch?: string
  swedish?: string
  norwegian?: string
  category?: string
  batchIndex?: number
}

export interface TranslationBatch {
  batchNumber: number
  translations: TranslationData[]
  totalTranslations: number
  lastUpdated: number
}

const STORAGE_KEYS = {
  CURRENT_BATCH: 'instra_translation_current_batch',
  BATCH_PREFIX: 'instra_translation_batch_',
  TRANSLATION_INDEX: 'instra_translation_index',
  TOTAL_TRANSLATIONS: 'instra_total_translations',
  LAST_PROCESSED: 'instra_translation_last_processed'
}

const BATCH_SIZE = 100
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

// Available target languages
export const AVAIL<PERSON>LE_LANGUAGES = [
  { code: 'hindi', name: 'Hindi', flag: '🇮🇳' },
  { code: 'spanish', name: 'Spanish', flag: '🇪🇸' },
  { code: 'french', name: 'French', flag: '🇫🇷' },
  { code: 'german', name: 'German', flag: '🇩🇪' },
  { code: 'italian', name: 'Italian', flag: '🇮🇹' },
  { code: 'portuguese', name: 'Portuguese', flag: '🇵🇹' },
  { code: 'russian', name: 'Russian', flag: '🇷🇺' },
  { code: 'japanese', name: 'Japanese', flag: '🇯🇵' },
  { code: 'korean', name: 'Korean', flag: '🇰🇷' },
  { code: 'chinese', name: 'Chinese', flag: '🇨🇳' },
  { code: 'arabic', name: 'Arabic', flag: '🇸🇦' },
  { code: 'dutch', name: 'Dutch', flag: '🇳🇱' }
]

// Generate translation title from English text
function generateTitle(englishText: string, index: number): string {
  const truncated = englishText.length > 50 ? englishText.substring(0, 50) + '...' : englishText
  return `Translation ${index + 1}: ${truncated}`
}

// Process raw translation data from instradata.json
export function processTranslationData(rawData: any[]): TranslationData[] {
  const processedTranslations: TranslationData[] = []

  if (Array.isArray(rawData)) {
    rawData.forEach((item, index) => {
      if (item.english) {
        processedTranslations.push({
          id: `translation_${index}_${Date.now()}`,
          english: item.english,
          hindi: item.hindi,
          spanish: item.spanish,
          french: item.french,
          german: item.german,
          italian: item.italian,
          portuguese: item.portuguese,
          russian: item.russian,
          japanese: item.japanese,
          korean: item.korean,
          chinese: item.chinese,
          arabic: item.arabic,
          dutch: item.dutch,
          swedish: item.swedish,
          norwegian: item.norwegian,
          category: 'General',
          batchIndex: Math.floor(processedTranslations.length / BATCH_SIZE)
        })
      }
    })
  }

  return processedTranslations
}

// Save translation batch to localStorage
function saveBatchToStorage(batchNumber: number, translations: TranslationData[]): void {
  try {
    const batch: TranslationBatch = {
      batchNumber,
      translations,
      totalTranslations: translations.length,
      lastUpdated: Date.now()
    }
    
    localStorage.setItem(
      `${STORAGE_KEYS.BATCH_PREFIX}${batchNumber}`,
      JSON.stringify(batch)
    )
  } catch (error) {
    console.error(`Error saving translation batch ${batchNumber}:`, error)
  }
}

// Load translation batch from localStorage
function loadBatchFromStorage(batchNumber: number): TranslationBatch | null {
  try {
    const stored = localStorage.getItem(`${STORAGE_KEYS.BATCH_PREFIX}${batchNumber}`)
    if (!stored) return null
    
    const batch: TranslationBatch = JSON.parse(stored)
    
    // Check if cache is still valid
    if (Date.now() - batch.lastUpdated > CACHE_DURATION) {
      localStorage.removeItem(`${STORAGE_KEYS.BATCH_PREFIX}${batchNumber}`)
      return null
    }
    
    return batch
  } catch (error) {
    console.error(`Error loading translation batch ${batchNumber}:`, error)
    return null
  }
}

// Split translations into batches and save to localStorage
export function saveTranslationsToBatches(translations: TranslationData[]): void {
  const totalBatches = Math.ceil(translations.length / BATCH_SIZE)
  
  for (let i = 0; i < totalBatches; i++) {
    const startIndex = i * BATCH_SIZE
    const endIndex = Math.min(startIndex + BATCH_SIZE, translations.length)
    const batchTranslations = translations.slice(startIndex, endIndex)
    
    saveBatchToStorage(i, batchTranslations)
  }
  
  // Save metadata
  localStorage.setItem(STORAGE_KEYS.TOTAL_TRANSLATIONS, translations.length.toString())
  localStorage.setItem(STORAGE_KEYS.CURRENT_BATCH, '0')
  localStorage.setItem(STORAGE_KEYS.LAST_PROCESSED, Date.now().toString())
  
  console.log(`Saved ${translations.length} translations in ${totalBatches} batches`)
}

// Load translations from a specific batch
export function loadTranslationBatch(batchNumber: number): TranslationData[] {
  const batch = loadBatchFromStorage(batchNumber)
  return batch ? batch.translations : []
}

// Get current batch translations
export function getCurrentBatchTranslations(): TranslationData[] {
  const currentBatch = parseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_BATCH) || '0')
  return loadTranslationBatch(currentBatch)
}

// Move to next batch
export function moveToNextBatch(): TranslationData[] {
  const currentBatch = parseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_BATCH) || '0')
  const totalTranslations = parseInt(localStorage.getItem(STORAGE_KEYS.TOTAL_TRANSLATIONS) || '0')
  const totalBatches = Math.ceil(totalTranslations / BATCH_SIZE)
  
  const nextBatch = (currentBatch + 1) % totalBatches
  localStorage.setItem(STORAGE_KEYS.CURRENT_BATCH, nextBatch.toString())
  
  return loadTranslationBatch(nextBatch)
}

// Get translation statistics
export function getTranslationStats(): {
  totalTranslations: number
  currentBatch: number
  totalBatches: number
  translationsInCurrentBatch: number
} {
  const totalTranslations = parseInt(localStorage.getItem(STORAGE_KEYS.TOTAL_TRANSLATIONS) || '0')
  const currentBatch = parseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_BATCH) || '0')
  const totalBatches = Math.ceil(totalTranslations / BATCH_SIZE)
  const currentBatchTranslations = loadTranslationBatch(currentBatch)
  
  return {
    totalTranslations,
    currentBatch,
    totalBatches,
    translationsInCurrentBatch: currentBatchTranslations.length
  }
}

// Clear all translation data from localStorage
export function clearTranslationStorage(): void {
  const keys = Object.keys(localStorage)
  keys.forEach(key => {
    if (key.startsWith(STORAGE_KEYS.BATCH_PREFIX) || 
        Object.values(STORAGE_KEYS).includes(key)) {
      localStorage.removeItem(key)
    }
  })
  console.log('Cleared all translation storage')
}

// Check if translations need to be reloaded
export function shouldReloadTranslations(): boolean {
  const lastProcessed = localStorage.getItem(STORAGE_KEYS.LAST_PROCESSED)
  if (!lastProcessed) return true
  
  const timeSinceLastUpdate = Date.now() - parseInt(lastProcessed)
  return timeSinceLastUpdate > CACHE_DURATION
}

// Load translations from the instradata.json file
export async function loadTranslationsFromFile(): Promise<TranslationData[]> {
  try {
    const response = await fetch('/instradata.json')
    if (!response.ok) {
      throw new Error(`Failed to load translations: ${response.statusText}`)
    }

    const rawData = await response.json()
    console.log('Raw translation data loaded:', rawData.length, 'entries')

    // Process the data
    return processTranslationData(rawData)
  } catch (error) {
    console.error('Error loading translations from file:', error)
    throw error
  }
}

// Initialize translation system
export async function initializeTranslationSystem(): Promise<TranslationData[]> {
  try {
    // Check if we need to reload translations
    if (shouldReloadTranslations()) {
      console.log('Loading fresh translation data...')
      const translations = await loadTranslationsFromFile()
      saveTranslationsToBatches(translations)
      return getCurrentBatchTranslations()
    } else {
      console.log('Using cached translation data...')
      return getCurrentBatchTranslations()
    }
  } catch (error) {
    console.error('Error initializing translation system:', error)
    // Fallback to cached data if available
    const cachedTranslations = getCurrentBatchTranslations()
    if (cachedTranslations.length > 0) {
      console.log('Using cached translations as fallback')
      return cachedTranslations
    }
    throw error
  }
}

// Get random translation from current batch
export function getRandomTranslation(): TranslationData | null {
  const translations = getCurrentBatchTranslations()
  if (translations.length === 0) return null
  
  const randomIndex = Math.floor(Math.random() * translations.length)
  return translations[randomIndex]
}

// Get random target language
export function getRandomTargetLanguage(): string {
  const randomIndex = Math.floor(Math.random() * AVAILABLE_LANGUAGES.length)
  return AVAILABLE_LANGUAGES[randomIndex].code
}
