{"name": "instra-global-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18", "react-dom": "^18", "next": "15.3.3", "firebase": "^10.8.0", "sweetalert2": "^11.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "15.3.3", "postcss": "^8", "tailwindcss": "^3.4.1", "autoprefixer": "^10.0.1"}}