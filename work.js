// Work Page JavaScript
// This file contains the functionality for the work page
// Note: Text checking is case-insensitive - capital vs. small letters are allowed

// Function to prevent paste events for users without permission
function preventPaste(e) {
    // If user has permission, allow paste
    if (userHasCopyPastePermission) {
        // Allow the paste event to proceed naturally
        return true;
    }

    e.preventDefault();
    e.stopPropagation();

    // Show a message to the user
    Swal.fire({
        title: 'Paste Not Allowed',
        text: 'You do not have permission to paste text. Please type the text manually or contact support for paste access.',
        icon: 'warning',
        timer: 3000,
        showConfirmButton: false
    });

    return false;
}

/**
 * Normalizes text for comparison by:
 * - Converting to lowercase
 * - Removing special characters
 * - Normalizing spaces (replacing multiple spaces with single spaces)
 * - Focusing only on words
 *
 * @param {string} text - The text to normalize
 * @returns {string} - The normalized text
 */
function normalizeTextForComparison(text) {
    if (!text) return '';

    // Convert to lowercase
    let normalized = text.toLowerCase();

    // Remove special characters, keeping only letters, numbers, and spaces
    normalized = normalized.replace(/[^\w\s]/g, '');

    // Replace multiple spaces with a single space
    normalized = normalized.replace(/\s+/g, ' ');

    // Trim leading and trailing spaces
    normalized = normalized.trim();

    return normalized;
}

/**
 * Highlights the error position in the expected text
 *
 * @param {number} position - The position of the error
 */
function highlightErrorInExpectedText(position) {
    // Get the English text element
    const englishTextElement = document.getElementById("englishText");
    if (!englishTextElement) return;

    // Get the text
    const text = currentEnglishText;

    // Create a highlighted version of the text
    let highlightedText = '';

    // Add text before the error position
    if (position > 0) {
        highlightedText += escapeHTML(text.substring(0, position));
    }

    // Add the error character with highlighting
    if (position < text.length) {
        highlightedText += `<span class="error-highlight">${escapeHTML(text.charAt(position))}</span>`;
    }

    // Add text after the error position
    if (position + 1 < text.length) {
        highlightedText += escapeHTML(text.substring(position + 1));
    }

    // Update the English text element
    englishTextElement.innerHTML = highlightedText;
}

/**
 * Removes error highlighting from the expected text
 */
function removeErrorHighlighting() {
    // Get the English text element
    const englishTextElement = document.getElementById("englishText");
    if (!englishTextElement) return;

    // Reset to plain text
    englishTextElement.textContent = currentEnglishText;
}

/**
 * Helper function to escape HTML special characters
 * to prevent XSS when displaying user input
 *
 * @param {string} text - The text to escape
 * @returns {string} - The escaped text
 */
function escapeHTML(text) {
    if (!text) return '';
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

// Function to add text checking event listeners to the typed text input
function addTextCheckingEventListeners() {
    if (!typedText || !checkBtn) return;

    // Add input event listener for real-time validation
    typedText.addEventListener('input', function() {
        // Get the current typed text and the expected text
        const typedTextValue = typedText.value;
        const displayedText = currentEnglishText;

        // Check if we're adding a character (not deleting)
        const isAddingCharacter = typedTextValue.length > (typedText.dataset.prevLength || 0);
        typedText.dataset.prevLength = typedTextValue.length;

        // Only validate if we're adding characters
        if (isAddingCharacter && typedTextValue.length > 0) {
            // Check if what's typed so far matches the beginning of the expected text
            const expectedSubstring = displayedText.substring(0, typedTextValue.length);

            // Compare the typed text with the expected substring
            if (typedTextValue !== expectedSubstring) {
                // Find the position of the first error
                let errorPosition = 0;
                while (errorPosition < typedTextValue.length &&
                       errorPosition < expectedSubstring.length &&
                       typedTextValue[errorPosition] === expectedSubstring[errorPosition]) {
                    errorPosition++;
                }

                // Prevent the new character from being added if it's incorrect
                if (errorPosition < typedTextValue.length) {
                    // Keep only the correct part of the text
                    typedText.value = typedTextValue.substring(0, errorPosition);

                    // Show error message
                    checkResult.textContent = `Error at position ${errorPosition + 1}. Expected "${expectedSubstring[errorPosition] || ''}" but got "${typedTextValue[errorPosition] || ''}"`;
                    checkResult.classList.remove("hidden", "success");
                    checkResult.classList.add("error");

                    // Highlight the error position in the expected text
                    highlightErrorInExpectedText(errorPosition);
                }
            } else {
                // Text is correct so far
                checkResult.classList.add("hidden");

                // Remove any error highlighting
                removeErrorHighlighting();
            }
        }

        // Enable/disable the check button based on whether there's any text
        checkBtn.disabled = typedText.value.trim().length === 0;
    });

    // Add click event listener to the check button
    checkBtn.addEventListener('click', function() {
        // First check if user is online
        if (!isUserOnline()) {
            console.log('Device is offline, cannot check text');
            Swal.fire({
                icon: 'warning',
                title: 'You\'re Offline',
                text: 'Cannot verify text while offline. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // First, check if the typed text matches the displayed English text
        const typedTextValue = typedText.value.trim();
        const displayedText = currentEnglishText.trim();

        console.log('--- Text Comparison Debug ---');
        console.log('Typed text (raw):', typedTextValue);
        console.log('Displayed text (raw):', displayedText);

        // Normalize both texts for more lenient comparison
        const normalizedTypedText = normalizeTextForComparison(typedTextValue);
        const normalizedDisplayedText = normalizeTextForComparison(displayedText);

        console.log('Normalized typed text:', normalizedTypedText);
        console.log('Normalized displayed text:', normalizedDisplayedText);
        console.log('Are they equal?', normalizedTypedText === normalizedDisplayedText);

        // Try a simpler comparison if the normalized comparison fails
        // This removes all whitespace and special characters and compares just the alphanumeric characters
        const simpleTypedText = typedTextValue.toLowerCase().replace(/[^a-z0-9]/gi, '');
        const simpleDisplayedText = displayedText.toLowerCase().replace(/[^a-z0-9]/gi, '');
        console.log('Simple comparison equal?', simpleTypedText === simpleDisplayedText);

        // Check if either the normalized text or the simple text comparison is correct
        const isTextCorrect = (normalizedTypedText === normalizedDisplayedText) ||
                             (simpleTypedText === simpleDisplayedText && simpleTypedText.length > 0);

        // Show text validation result
        if (isTextCorrect) {
            console.log('Text validation result: CORRECT');
            checkResult.textContent = "Text Correct!";
            checkResult.classList.remove("error");
            checkResult.classList.add("success");

            // Enable the language dropdown when text is correct
            languageDropdown.disabled = false;
        } else {
            console.log('Text validation result: INCORRECT');
            checkResult.textContent = "Text Incorrect! Please type the exact text shown above, including punctuation and spacing.";
            checkResult.classList.remove("success");
            checkResult.classList.add("error");

            // Keep the language dropdown disabled if text is incorrect
            languageDropdown.disabled = true;
        }
        checkResult.classList.remove("hidden");

        // Now, check if the selected language matches the displayed language
        const selectedLanguage = languageDropdown.value;
        const displayedLanguage = randomLanguage.textContent;

        // Check if the language selection is correct
        const isLanguageCorrect = selectedLanguage === displayedLanguage;

        // Show language validation result
        if (!isLanguageCorrect) {
            languageError.textContent = `Please select ${displayedLanguage} from the dropdown.`;
            languageError.classList.remove("hidden");

            // Highlight the dropdown to draw attention
            languageDropdown.classList.add("error-border");
        } else {
            // Hide language error
            languageError.classList.add("hidden");
            languageDropdown.classList.remove("error-border");
        }

        // Only enable the translate button if both text and language are correct
        translateBtn.disabled = !(isTextCorrect && isLanguageCorrect);
    });
}



// Function to handle text input functionality based on user permissions
function setupTextInputFunctionality(element, hasCopyPastePermission = false) {
    if (!element) return;

    // Initialize the previousLength property to the current length
    element.dataset.previousLength = element.value.length.toString();
    element.dataset.prevLength = element.value.length.toString();

    // For users with copy/paste permission
    if (hasCopyPastePermission) {
        console.log('User has copy/paste permission, enabling paste functionality and disabling typing');

        // Make the textarea read-only but allow paste operations
        // We'll temporarily make it not read-only during paste operations
        element.readOnly = true;
        element.classList.add('paste-only-mode');

        // Add a placeholder to indicate paste functionality
        element.placeholder = 'Click here and paste the text (Ctrl+V)';

        // Allow paste events for enabled users
        element.addEventListener('paste', function(e) {
            // Allow paste to proceed naturally
            console.log('Paste event allowed for enabled user');

            // After paste, trigger the check button if the text matches
            setTimeout(() => {
                // Use the normalized text comparison for more lenient validation
                const typedValue = element.value.trim();
                const displayedValue = currentEnglishText.trim();

                const normalizedTypedText = normalizeTextForComparison(typedValue);
                const normalizedDisplayedText = normalizeTextForComparison(displayedValue);

                // Try a simpler comparison if the normalized comparison fails
                const simpleTypedText = typedValue.toLowerCase().replace(/[^a-z0-9]/gi, '');
                const simpleDisplayedText = displayedValue.toLowerCase().replace(/[^a-z0-9]/gi, '');

                // Check if either the normalized text or the simple text comparison is correct
                const isTextCorrect = (normalizedTypedText === normalizedDisplayedText) ||
                                     (simpleTypedText === simpleDisplayedText && simpleTypedText.length > 0);

                if (isTextCorrect) {
                    checkBtn.click();
                }
            }, 100);

            // Show success feedback
            const pasteTextBtn = document.getElementById("pasteTextBtn");
            if (pasteTextBtn) {
                const originalIcon = pasteTextBtn.innerHTML;
                pasteTextBtn.innerHTML = '<i class="fas fa-check"></i>';
                pasteTextBtn.style.color = '#4CAF50';

                // Reset the button after a short delay
                setTimeout(() => {
                    pasteTextBtn.innerHTML = originalIcon;
                    pasteTextBtn.style.color = '';
                }, 1500);
            }
        });

        // Add click handler to make it easier to paste
        element.addEventListener('click', function() {
            // Temporarily make the field not read-only to allow paste
            element.readOnly = false;

            // Focus the element
            element.focus();

            // Show a toast notification
            Swal.fire({
                icon: 'info',
                title: 'Paste Now',
                text: 'Press Ctrl+V to paste the text',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2000
            });

            // Set a timeout to make it read-only again
            setTimeout(() => {
                // Only make it read-only again if it doesn't have content
                if (!element.value.trim()) {
                    element.readOnly = true;
                }
            }, 5000); // Give user 5 seconds to paste
        });

        return;
    }

    // For users without copy/paste permission - disable all paste methods
    console.log('User does not have copy/paste permission, disabling paste functionality');

    // Make sure the textarea is not read-only to allow typing
    element.readOnly = false;
    element.classList.remove('paste-only-mode');

    // Standard paste event
    element.addEventListener('paste', preventPaste);

    // Input event to detect mobile keyboard paste
    element.addEventListener('input', function(e) {
        // Check if multiple characters were added at once (likely a paste)
        const inputType = e.inputType;
        const previousLength = parseInt(element.dataset.previousLength || '0', 10);
        const currentLength = e.target.value.length;
        const lengthDifference = currentLength - previousLength;

        // Store current length for next comparison
        element.dataset.previousLength = currentLength.toString();

        // Log input event details for debugging
        console.log('Input event:', {
            inputType,
            previousLength,
            currentLength,
            lengthDifference,
            data: e.data
        });

        // Detect paste operations using both input type and length-based detection
        if (
            // These input types are ONLY associated with paste operations
            inputType === 'insertFromPaste' ||
            inputType === 'insertFromDrop' ||
            inputType === 'insertReplacementText' ||
            // Length-based detection: 4 or more characters added at once
            (lengthDifference >= 4 && e.data && e.data.length >= 4)
        ) {
            // Clear the input
            e.target.value = '';
            element.dataset.previousLength = '0';
            element.dataset.prevLength = '0';

            // Show message
            Swal.fire({
                title: 'Paste Not Allowed',
                text: 'You do not have permission to paste text. Please type the text manually or contact support for paste access.',
                icon: 'warning',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });

    // Disable context menu to prevent right-click paste
    element.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable keyboard shortcuts
    element.addEventListener('keydown', preventCopyPasteShortcuts);

    // Disable clipboard API access for users without permission
    element.addEventListener('beforeinput', function(e) {
        // Log beforeinput event details for debugging
        console.log('BeforeInput event:', {
            inputType: e.inputType,
            data: e.data
        });

        // Only block actual paste operations
        if (e.inputType === 'insertFromPaste') {
            e.preventDefault();
            preventPaste(e);
        }
    });

    // Disable drag and drop
    element.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });

    // Disable speech recognition input (voice typing)
    element.addEventListener('speechstart', function(e) {
        e.preventDefault();
        return false;
    });

    element.addEventListener('speechend', function(e) {
        e.preventDefault();
        return false;
    });

    // Add attribute to disable speech recognition in browsers that support it
    element.setAttribute('x-webkit-speech', 'false');
    element.setAttribute('speech', 'false');

    // Add composition event handlers to properly handle IME input
    element.addEventListener('compositionstart', function(e) {
        console.log('Composition started');
        // Mark that we're in composition mode
        element.dataset.isComposing = 'true';
    });

    element.addEventListener('compositionend', function(e) {
        console.log('Composition ended');
        // Mark that we're no longer in composition mode
        element.dataset.isComposing = 'false';

        // Update the previous length after composition is complete
        setTimeout(() => {
            element.dataset.previousLength = element.value.length.toString();
            element.dataset.prevLength = element.value.length.toString();
        }, 0);
    });
}

// Function to prevent right-click context menu
function preventContextMenu(e) {
    e.preventDefault();
    e.stopPropagation();

    // Show a message to the user
    Swal.fire({
        title: 'Copy Not Allowed',
        text: 'You do not have permission to copy text. Please contact support for copy/paste access.',
        icon: 'warning',
        timer: 3000,
        showConfirmButton: false
    });

    return false;
}

// Global variable to track if user has copy/paste permission
let userHasCopyPastePermission = false;

// Function to prevent keyboard shortcuts for copy/paste
function preventCopyPasteShortcuts(e) {
    // If user has permission, allow copy/paste shortcuts
    if (userHasCopyPastePermission) {
        return true;
    }

    // Check for Ctrl+C, Ctrl+V, Cmd+C, Cmd+V
    if ((e.ctrlKey || e.metaKey) && (e.key === 'c' || e.key === 'v' || e.key === 'x')) {
        e.preventDefault();
        e.stopPropagation();

        // Show appropriate message based on the key
        let title, message;
        if (e.key === 'c' || e.key === 'x') {
            title = 'Copy Not Allowed';
            message = 'You do not have permission to copy text. Please contact support for copy/paste access.';
        } else { // e.key === 'v'
            title = 'Paste Not Allowed';
            message = 'You do not have permission to paste text. Please type the text manually or contact support for paste access.';
        }

        Swal.fire({
            title: title,
            text: message,
            icon: 'warning',
            timer: 3000,
            showConfirmButton: false
        });

        return false;
    }
}

import { initializeApp, getApps, getApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import { getFirestore, doc, getDoc, updateDoc, arrayUnion, serverTimestamp, collection, addDoc } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import { checkPlanExpiration } from "./plan-expiration.js";
import { checkDailyTranslationLimit, incrementDailyTranslationCount, markTranslationsAsSubmitted, updateWalletWithEarnings } from "./translation-limits.js";
import { initTranslationProgress, updateTranslationProgress } from "./logout-protection.js";
import { resetTranslationProgress } from "./storage-cleanup.js";
import { getRandomTranslationItem, getNextTranslationItem, loadTranslationData, loadNextChunk } from "./chunked-translation-data.js";
import { isLeaveDay, getLeaveReason, getNextWorkingDay, formatDateForDisplay } from "./leave-system.js";
import { checkCopyPastePermission } from "./copy-paste-permission.js";
import {
    loadUserDataWithCache,
    getWalletDataWithCache,
    getTranslationCountWithCache,
    cacheTranslationContent,
    getCachedTranslationContent,
    getTotalTranslationsCount
} from "./work-cache.js";

// Initialize Firebase
const firebaseConfig = {
    apiKey: "AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",
    authDomain: "instra-global.firebaseapp.com",
    projectId: "instra-global",
    storageBucket: "instra-global.firebasestorage.app",
    messagingSenderId: "725774700748",
    appId: "1:725774700748:web:4cdac03d835a7e2e133269",
    measurementId: "G-QGHBLY3DLQ"
};

const app = getApps().length ? getApp() : initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// We'll use the pre-loaded translation data from translation-data.js
// Define available languages
const availableLanguages = [
    "Spanish", "French", "German", "Italian", "Portuguese", "Russian",
    "Arabic", "Hindi", "Chinese (Simplified)", "Japanese", "Korean",
    "Turkish", "Dutch", "Swedish", "Polish", "Ukrainian", "Greek",
    "Hebrew", "Vietnamese", "Thai"
];

let currentEnglishText = "";
let currentTargetLanguage = "";
let todayTranslationCount = 0;

// DOM Elements
const englishTextElement = document.getElementById("englishText");
let typedText = document.getElementById("typedText"); // Changed to let since it's reassigned later
const checkBtn = document.getElementById("checkBtn");
const checkResult = document.getElementById("checkResult");
const languageDropdown = document.getElementById("languageDropdown");
const randomLanguage = document.getElementById("randomLanguage");
const languageError = document.getElementById("languageError");
const translateBtn = document.getElementById("translateBtn");
const translatedOutput = document.getElementById("translatedOutput");
const saveBtn = document.getElementById("saveBtn");
const submitButton = document.getElementById("submitBtn");
const todayTranslationsElement = document.querySelector('.stat-item:nth-child(1) .stat-value');
const totalTranslationsElement = document.querySelector('.stat-item:nth-child(2) .stat-value');
const translationsLeftElement = document.getElementById("translationsLeft");
const copyTextBtn = document.getElementById("copyTextBtn");
const pasteTextBtn = document.getElementById("pasteTextBtn");

// Add global document-level paste prevention
document.addEventListener('paste', function(e) {
    // Only prevent paste if the target is the typedText element or its descendants
    // and the user doesn't have copy/paste permission
    if ((e.target === typedText || e.target.closest('#typedText')) && !userHasCopyPastePermission) {
        preventPaste(e);
    }
});

// Loading overlay elements
const loadingOverlay = document.getElementById('loadingOverlay');

// Function to show the loading overlay
function showLoading() {
    console.log('Showing loading overlay...');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

// Function to hide the loading overlay and show ready message
function hideLoading() {
    console.log('Hiding loading overlay...');
    if (loadingOverlay) {
        // Force the overlay to be hidden
        loadingOverlay.style.display = 'none';
        loadingOverlay.style.visibility = 'hidden';
        loadingOverlay.style.opacity = '0';

        console.log('Loading overlay hidden');

        // Create and show a ready message
        const readyMessage = document.createElement('div');
        readyMessage.className = 'ready-message';
        readyMessage.innerHTML = '<i class="fas fa-check-circle"></i> Page is ready for translation';
        document.body.appendChild(readyMessage);
        console.log('Ready message displayed');

        // Remove the message after animation completes
        setTimeout(() => {
            if (readyMessage && readyMessage.parentNode) {
                readyMessage.parentNode.removeChild(readyMessage);
                console.log('Ready message removed');
            }
        }, 6000); // 3s display + 3s fade out
    } else {
        console.warn('Loading overlay element not found');
    }
}

/**
 * Disable the work interface during leave days
 * @param {string} message - Message to display
 */
function disableWorkInterface(message) {
    console.log('Disabling work interface for leave day');

    // Create a leave day banner
    const container = document.querySelector('.container');
    if (container) {
        const leaveBanner = document.createElement('div');
        leaveBanner.className = 'leave-banner';
        leaveBanner.innerHTML = `
            <div class="leave-banner-content">
                <div class="leave-banner-icon">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <div class="leave-banner-text">
                    <div class="leave-banner-title">Platform on Leave Today</div>
                    <div class="leave-banner-message">${message}</div>
                </div>
            </div>
        `;

        // Insert at the top of the container
        container.insertBefore(leaveBanner, container.firstChild);
    }

    // Disable all interactive elements
    const interactiveElements = [
        typedText,
        checkBtn,
        languageDropdown,
        translateBtn,
        saveBtn,
        submitButton
    ];

    interactiveElements.forEach(element => {
        if (element) {
            element.disabled = true;
            element.classList.add('leave-disabled');
        }
    });

    // Add a visual indicator to the work form
    const workForm = document.querySelector('.work-form');
    if (workForm) {
        workForm.classList.add('leave-disabled');

        // Add a message at the top of the form
        const leaveMessage = document.createElement('div');
        leaveMessage.className = 'leave-message';
        leaveMessage.innerHTML = `
            <div class="next-working-day">
                <i class="fas fa-info-circle"></i>
                Translations will be available on the next working day.
            </div>
        `;

        workForm.insertBefore(leaveMessage, workForm.firstChild);
    }
}

// Show loading immediately
showLoading();

// Utility function to check if the user is online
function isUserOnline() {
    // Check both navigator.onLine and OfflineManager if available
    return navigator.onLine && (typeof OfflineManager === 'undefined' || OfflineManager.isOnline);
}

// Function to get a random item from an array
function getRandomItem(arr) {
    return arr[Math.floor(Math.random() * arr.length)];
}

// Function to populate the language dropdown
function populateLanguages() {
    // Clear existing options first (except the first one)
    while (languageDropdown.options.length > 1) {
        languageDropdown.remove(1);
    }

    // Add language options
    availableLanguages.forEach(lang => {
        const opt = document.createElement("option");
        opt.value = lang;
        opt.textContent = lang;
        languageDropdown.appendChild(opt);
    });
}

// Function to reset the page for a new task
function resetPage(skipCountRefresh = false) {
    try {
        // Get the latest count from localStorage only if skipCountRefresh is false
        // This prevents overriding the count when called from the save button
        if (!skipCountRefresh) {
            const storedCount = localStorage.getItem('instra_translation_count');
            if (storedCount !== null) {
                todayTranslationCount = parseInt(storedCount, 10) || 0;
                console.log('Reset page: loaded count from localStorage:', todayTranslationCount);
            }
        } else {
            console.log('Reset page: skipping count refresh, using current count:', todayTranslationCount);
        }

        // Update the UI with the current count
        if (todayTranslationsElement) {
            todayTranslationsElement.textContent = todayTranslationCount;
        }

        // Update counter in the progress circle
        const dayNumberElement = document.querySelector('.day-number');
        if (dayNumberElement) {
            dayNumberElement.textContent = todayTranslationCount;
        }

        // Update translations left
        const translationsLeftElement = document.getElementById('translationsLeft');
        if (translationsLeftElement) {
            translationsLeftElement.textContent = (50 - todayTranslationCount).toString();
        }

        // Check if we've reached the daily limit of 50 translations
        if (todayTranslationCount >= 50) {
            // Disable all interactive elements
            typedText.disabled = true;
            checkBtn.disabled = true;
            translateBtn.disabled = true;
            saveBtn.disabled = true;
            languageDropdown.disabled = true;

            // Enable the submit button
            if (submitButton) {
                submitButton.disabled = false;
                // Add a pulsing effect to draw attention
                submitButton.classList.add('pulse-button');
            }

            // Show message to submit translations
            Swal.fire({
                icon: 'info',
                title: 'Daily Limit Reached',
                html: `
                    <p>You have completed 50 translations for today.</p>
                    <p>Please click the <strong>Submit</strong> button to submit your work and earn your payment.</p>
                    <p>You will not be able to do more translations until tomorrow (12:00 AM).</p>
                `,
                confirmButtonText: 'OK'
            });

            return; // Exit the function early
        }

        // Get a random translation item
        const translationItem = getRandomTranslationItem();

        // Get the English text
        currentEnglishText = translationItem.english || "Translation data is loading. Please wait a moment...";

        // Get a random target language
        currentTargetLanguage = getRandomItem(availableLanguages);

        // Update the UI
        englishTextElement.textContent = currentEnglishText;
        randomLanguage.textContent = currentTargetLanguage;

        // Make sure the sticky text container is visible when loading new text
        const stickyTextContainer = document.querySelector('.sticky-english-text');
        if (stickyTextContainer) {
            stickyTextContainer.style.display = 'block';
        }

        // Reset form elements
        typedText.value = "";
        typedText.disabled = false;
        checkResult.classList.add("hidden");
        languageError.classList.add("hidden");
        translatedOutput.classList.add("hidden");
        translatedOutput.textContent = ""; // Clear previous translation text
        saveBtn.classList.add("hidden");
        saveBtn.disabled = true; // Always disable save button until translation is done
        languageDropdown.value = "";
        languageDropdown.disabled = true; // Disable dropdown until text is checked
        languageDropdown.classList.remove("error-border");

        // Clear any character error messages and highlighting
        const charErrorMessage = document.getElementById('charErrorMessage');
        if (charErrorMessage) {
            charErrorMessage.classList.remove('show');
        }

        // Reset the English text display to remove any error highlighting
        if (englishTextElement) {
            englishTextElement.textContent = currentEnglishText;
        }

        // Disable translate button until text is checked
        translateBtn.disabled = true;
        checkBtn.disabled = false;

        console.log('Page reset successfully');
    } catch (error) {
        console.error('Error resetting page:', error);
        // Use a default message in case of error
        currentEnglishText = "An error occurred while loading translation data. Please refresh the page.";
        currentTargetLanguage = getRandomItem(availableLanguages);

        // Update the UI with error message
        englishTextElement.textContent = currentEnglishText;
        randomLanguage.textContent = currentTargetLanguage;
    }
}

// Function to load new text for translation (now uses resetPage)
function loadNewText(skipCountRefresh = false) {
    // Check if online before loading new text
    if (!isUserOnline()) {
        console.log('Device is offline, cannot load new text');
        Swal.fire({
            icon: 'warning',
            title: 'You\'re Offline',
            text: 'Cannot load new translation text while offline. Please check your internet connection and try again.',
            confirmButtonText: 'OK'
        });
        return;
    }

    resetPage(skipCountRefresh);
}

// Function to update the plan information in the UI
function updatePlanInfo(planInfo) {
    // Update plan name
    const currentPlanElement = document.getElementById('currentPlan');
    if (currentPlanElement) {
        currentPlanElement.textContent = planInfo.planType || 'Trial';
    }

    // Update days left
    const daysLeftElement = document.getElementById('daysLeft');
    if (daysLeftElement) {
        daysLeftElement.textContent = `${planInfo.daysLeft || 0} days left`;
    }

    // Update translations left
    const translationsLeftElement = document.getElementById('translationsLeft');
    if (translationsLeftElement) {
        translationsLeftElement.textContent = planInfo.translationsLeft || 0;
    }

    // Update active days
    const activeDaysElement = document.querySelector('.stat-item:nth-child(4) .stat-value');
    if (activeDaysElement) {
        // Use the activeDays from planInfo, or default to 1
        const activeDays = planInfo.activeDays || 1;
        const validDays = planInfo.validDays || (planInfo.planType === 'Trial' ? 2 : 30);

        // Display active days with valid days in parentheses
        activeDaysElement.textContent = `${activeDays}/${validDays}`;

        // Add a tooltip to explain the active days
        activeDaysElement.title = `Day ${activeDays} of your ${validDays}-day ${planInfo.planType} plan`;
    }
}

// Function to check if the user can work (plan is active and not a leave day)
async function checkPlanStatus() {
    try {
        // First check if today is a leave day
        const todayIsLeaveDay = await isLeaveDay();

        if (todayIsLeaveDay) {
            // Get the reason for the leave
            const leaveReason = await getLeaveReason() || 'System Leave';

            // Get the next working day
            const nextWorkDay = await getNextWorkingDay();
            const formattedNextWorkDay = formatDateForDisplay(nextWorkDay);

            // Show leave day message
            Swal.fire({
                icon: 'info',
                title: 'Platform on Leave Today',
                html: `
                    <div class="leave-banner-content">
                        <p><strong>${leaveReason}</strong></p>
                        <p>Translations are not available during leave days.</p>
                        <p>Please come back on <strong>${formattedNextWorkDay}</strong> to continue your work.</p>
                        <p>Note: Your active days counter will not increase during leave days.</p>
                    </div>
                `,
                confirmButtonText: 'I Understand'
            });

            // Disable all interactive elements
            disableWorkInterface('Translations are not available during leave days.');

            return false; // Don't load work
        }

        // Check if the user's plan has expired
        const planStatus = await checkPlanExpiration();

        // Update the plan information in the UI
        updatePlanInfo(planStatus);

        if (planStatus.expired) {
            // Plan has expired or there's an error, show appropriate message

            // Handle system errors first
            if (planStatus.error) {
                Swal.fire({
                    icon: 'error',
                    title: 'System Error',
                    text: planStatus.message,
                    confirmButtonText: 'Refresh Page',
                    allowOutsideClick: false
                }).then(() => {
                    window.location.reload();
                });
                return false;
            }

            if (planStatus.planType === 'Trial') {
                // Trial plan expired, show plan selection
                Swal.fire({
                    title: 'Trial Period Expired',
                    html: `
                        <p class="plan-selection-text">Your 2-day Trial period has expired. Please choose a plan to continue translating.</p>
                        <div class="plans-grid">
                            <div class="plan-card" data-plan="junior">
                                <div class="plan-header">
                                    <h4>Junior</h4>
                                    <div class="plan-price">Certified Users Only</div>
                                </div>
                                <div class="plan-features">
                                    <ul>
                                        <li><i class="fas fa-certificate"></i> <span>Certified for 1 Language</span></li>
                                        <li><i class="fas fa-check"></i> <span>Entry-level role for new freelancers</span></li>
                                        <li><i class="fas fa-check"></i> <span>30 days access</span></li>
                                        <li><i class="fas fa-info-circle"></i> <span><b>Estimated Earnings:</b> ₹150 for 50 translations</span></li>
                                    </ul>
                                </div>
                                <button class="upgrade-plan-btn" onclick="Swal.clickConfirm()">
                                    <i class="fas fa-crown"></i> Select
                                </button>
                            </div>
                            <div class="plan-card" data-plan="senior">
                                <div class="plan-header">
                                    <h4>Senior</h4>
                                    <div class="plan-price">Certified Users Only</div>
                                </div>
                                <div class="plan-features">
                                    <ul>
                                        <li><i class="fas fa-certificate"></i> <span>Certified for 3 Languages</span></li>
                                        <li><i class="fas fa-check"></i> <span>Mid-level role for experienced translators</span></li>
                                        <li><i class="fas fa-check"></i> <span>30 days access</span></li>
                                        <li><i class="fas fa-info-circle"></i> <span><b>Estimated Earnings:</b> ₹250 for 50 translations</span></li>
                                    </ul>
                                </div>
                                <button class="upgrade-plan-btn" onclick="Swal.clickConfirm()">
                                    <i class="fas fa-crown"></i> Select
                                </button>
                            </div>
                            <div class="plan-card" data-plan="executive">
                                <div class="plan-header">
                                    <h4>Executive</h4>
                                    <div class="plan-price">Certified Users Only</div>
                                </div>
                                <div class="plan-features">
                                    <ul>
                                        <li><i class="fas fa-certificate"></i> <span>Certified for 5 Languages</span></li>
                                        <li><i class="fas fa-check"></i> <span>Top-tier role for expert translators</span></li>
                                        <li><i class="fas fa-check"></i> <span>30 days access</span></li>
                                        <li><i class="fas fa-info-circle"></i> <span><b>Estimated Earnings:</b> ₹400 for 50 translations</span></li>
                                    </ul>
                                </div>
                                <button class="upgrade-plan-btn" onclick="Swal.clickConfirm()">
                                    <i class="fas fa-crown"></i> Select
                                </button>
                            </div>
                        </div>
                    `,
                    showCancelButton: true,
                    cancelButtonText: 'Cancel',
                    showConfirmButton: false,
                    width: '800px',
                    background: 'var(--bg-card)',
                    color: 'var(--text-light)',
                    customClass: {
                        title: 'gradient-text',
                        popup: 'glass-card',
                        cancelButton: 'swal2-cancel-button-themed'
                    },
                    didOpen: () => {
                        // Add hover effect to plan cards
                        document.querySelectorAll('.plan-card').forEach(card => {
                            card.addEventListener('mouseenter', function() {
                                this.classList.add('plan-card-hover');
                            });
                            card.addEventListener('mouseleave', function() {
                                this.classList.remove('plan-card-hover');
                            });
                        });
                    }
                }).then(() => {
                    window.location.href = 'dashboard.html';
                });
            } else {
                // Other plan expired, show contact support message
                Swal.fire({
                    icon: 'warning',
                    title: 'Plan Limit Reached',
                    html: `
                        <p>Your 30-day plan has expired. Please contact customer support to continue your translation work.</p>
                        <p><a href="mailto:<EMAIL>" class="support-link"><EMAIL></a></p>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'Contact Support',
                    cancelButtonText: 'Go to Dashboard'
                }).then(() => {
                    window.location.href = 'dashboard.html';
                });
            }

            return false; // Don't load work
        }

        // Check if the user has reached their daily translation limit
        const limitStatus = await checkDailyTranslationLimit();

        if (limitStatus.limitReached) {
            // Daily limit reached, show message
            const now = new Date();
            const midnight = new Date(now);
            midnight.setHours(24, 0, 0, 0);
            const timeUntilReset = midnight - now;
            const hoursUntilReset = Math.floor(timeUntilReset / (1000 * 60 * 60));
            const minutesUntilReset = Math.floor((timeUntilReset % (1000 * 60 * 60)) / (1000 * 60));

            Swal.fire({
                icon: 'info',
                title: 'Daily Limit Reached',
                html: `
                    <p>You've completed your daily limit of ${limitStatus.dailyLimit} translations.</p>
                    <p>The counter will reset at midnight (in ${hoursUntilReset}h ${minutesUntilReset}m).</p>
                    <p>You can still view the work page, but you won't be able to submit new translations until tomorrow.</p>
                `,
                confirmButtonText: 'I Understand'
            });

            // Disable the translate button
            if (translateBtn) {
                translateBtn.disabled = true;
                translateBtn.classList.add('disabled');
                translateBtn.title = 'Daily limit reached. Try again tomorrow.';
            }
        }

        return true; // Load work
    } catch (error) {
        console.error('Error checking plan status:', error);
        // Don't show error dialog, just log the error and return false
        return false; // Don't load work
    }
}

// Function to check if user is authenticated and came from dashboard
async function checkAuthAndReferrer() {
    return new Promise((resolve) => {
        // First check if we have an admin password login stored in localStorage
        const adminPasswordLogin = localStorage.getItem('adminPasswordLogin');
        const userId = localStorage.getItem('userId');

        if (adminPasswordLogin === 'true' && userId) {
            console.log('User is authenticated with admin-set password');
            // Continue with the check for referrer
            const fromDashboard = sessionStorage.getItem('fromDashboard') === 'true';
            if (!fromDashboard) {
                console.warn('User did not come from dashboard, redirecting');
                Swal.fire({
                    icon: 'warning',
                    title: 'Access Denied',
                    text: 'Please access the work page from your dashboard.',
                    confirmButtonText: 'Go to Dashboard',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.location.href = 'dashboard.html';
                });
                resolve(false);
                return;
            }

            // User is authenticated and came from dashboard
            resolve(true);
            return;
        }

        // Check if user is authenticated with Firebase Auth
        const unsubscribe = auth.onAuthStateChanged((user) => {
            unsubscribe(); // Unsubscribe immediately to prevent memory leaks

            if (!user) {
                console.warn('User not authenticated, redirecting to login page');
                Swal.fire({
                    icon: 'error',
                    title: 'Authentication Required',
                    text: 'You must be logged in to access the work page.',
                    confirmButtonText: 'Go to Login',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.location.href = 'login.html';
                });
                resolve(false);
                return;
            }

            // Check if user came from dashboard (referrer check)
            const referrer = document.referrer;
            const isDashboardReferrer = referrer.includes('dashboard.html');
            const hasReferrerFlag = sessionStorage.getItem('fromDashboard') === 'true';

            // Allow access if came from dashboard or has the referrer flag
            if (isDashboardReferrer || hasReferrerFlag) {
                // Set a flag in session storage to allow refreshing the page
                sessionStorage.setItem('fromDashboard', 'true');
                resolve(true);
                return;
            }

            // If not from dashboard, redirect back to dashboard
            console.warn('Direct access to work page detected, redirecting to dashboard');
            Swal.fire({
                icon: 'warning',
                title: 'Access Denied',
                text: 'Please access the work page from your dashboard.',
                confirmButtonText: 'Go to Dashboard',
                allowOutsideClick: false,
                allowEscapeKey: false
            }).then(() => {
                window.location.href = 'dashboard.html';
            });
            resolve(false);
        });
    });
}

// Function to format currency
function formatCurrency(value) {
    return `₹${parseFloat(value || 0).toFixed(2)}`;
}

// Function to update wallet displays
function updateWalletDisplays(wallet = {}) {
    console.log('Updating wallet displays with:', wallet);

    try {
        // Parse all wallet values for consistency
        const earning = parseFloat(wallet.earning) || 0;
        const bonus = parseFloat(wallet.bonus) || 0;
        const balance = parseFloat(wallet.balance) || 0; // Main wallet balance (not displayed on work page)

        console.log('Parsed wallet values:', { earning, bonus, balance });

        // Update earning wallet display
        const earningWalletElement = document.querySelector('.earnings-card:nth-child(1) .amount');
        if (earningWalletElement) {
            earningWalletElement.textContent = formatCurrency(earning);
            console.log('Updated earning wallet display:', earningWalletElement.textContent);
        } else {
            console.error('Earning wallet element not found in the DOM');
        }

        // Update bonus wallet display
        const bonusWalletElement = document.querySelector('.earnings-card:nth-child(2) .amount');
        if (bonusWalletElement) {
            bonusWalletElement.textContent = formatCurrency(bonus);
            console.log('Updated bonus wallet display:', bonusWalletElement.textContent);
        } else {
            console.error('Bonus wallet element not found in the DOM');
        }

        // Note: Main wallet (wallet.balance) is not displayed on the work page
        // It's only displayed on the wallet page using the element with ID 'mainBalance'
    } catch (error) {
        console.error('Error updating wallet displays:', error);
    }
}

// Initial load
async function initializeWork() {
    try {
        console.log('Initializing work page...');

        // Check authentication and referrer before proceeding
        const isAuthorized = await checkAuthAndReferrer();
        if (!isAuthorized) {
            console.log('Authorization check failed, stopping initialization');
            return;
        }

        // We'll handle paste functionality after checking user permissions
        // Don't disable paste immediately, wait until we check user permissions
        console.log('Paste functionality will be configured after checking user permissions...');

        // Add text checking event listeners
        addTextCheckingEventListeners();

        // Try to load translation data directly
        try {
            console.log('Loading translation data...');
            await loadTranslationData();
            console.log('Translation data loaded successfully');
        } catch (error) {
            console.warn('Error loading translation data, continuing anyway:', error);
        }

        // Initialize the language dropdown
        populateLanguages();

        // Fetch user data to update wallet displays using cache
        try {
            const currentUser = auth.currentUser;
            if (currentUser) {
                console.log('Fetching user data to update wallet displays with cache...');

                // Get wallet data with caching
                const walletData = await getWalletDataWithCache();

                // Update wallet displays using wallet.earning field
                updateWalletDisplays(walletData);

                // Load full user data in the background
                loadUserDataWithCache().then(userData => {
                    console.log('Full user data loaded in background');

                    // Update total translations count in the UI
                    if (userData && totalTranslationsElement) {
                        // STANDARDIZED: Use only stats.totalTranslationsCompleted
                        const totalTranslations = userData.stats?.totalTranslationsCompleted || 0;
                        totalTranslationsElement.textContent = totalTranslations;
                        console.log('Updated total translations count in UI:', totalTranslations);
                    }
                });

                // DIRECT APPROACH: Fetch total translations count directly from Firebase
                // This is similar to how the dashboard does it
                try {
                    // Use the new function from work-cache.js
                    const totalTranslations = await getTotalTranslationsCount();
                    if (totalTranslationsElement) {
                        totalTranslationsElement.textContent = totalTranslations;
                        console.log('DIRECT: Updated total translations count in UI:', totalTranslations);
                    }
                } catch (directError) {
                    console.error('Error directly fetching total translations count:', directError);
                    // This is just a fallback, so we don't need to handle the error
                }
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
        }

        // Get the current translation count and check if translations have been submitted for today
        try {
            // Check if we need to clear stale submission data
            const now = new Date();
            // Use the same standardized date format as in translation-limits.js
            const currentDate = now.toLocaleDateString('en-US', {
                month: 'numeric',
                day: 'numeric',
                year: 'numeric'
            });
            const submissionDate = localStorage.getItem('instra_submission_date');
            const translationsSubmitted = localStorage.getItem('instra_translations_submitted') === 'true';

            // If submission flag is set but it's for a previous day, clear it
            if (translationsSubmitted && submissionDate && submissionDate !== currentDate) {
                console.log('Found stale submission flag from a previous day, clearing it');
                localStorage.removeItem('instra_translations_submitted');
                localStorage.removeItem('instra_submission_date');
                localStorage.removeItem('instra_translation_count');
                localStorage.removeItem('instra_daily_translation_date');
            }

            // Check daily translation limit which will also tell us if translations have been submitted
            const limitStatus = await checkDailyTranslationLimit();
            todayTranslationCount = limitStatus.currentCount || 0;
            console.log('Loaded translation count from Firebase:', todayTranslationCount);

            // Store in localStorage for future use
            localStorage.setItem('instra_translation_count', todayTranslationCount.toString());
            localStorage.setItem('instra_daily_translation_date', currentDate);

            // Check if translations have already been submitted for today
            if (limitStatus.alreadySubmitted) {
                console.log('Translations have already been submitted for today');

                // Store submission date in local storage
                localStorage.setItem('instra_submission_date', currentDate);
                localStorage.setItem('instra_translations_submitted', 'true');

                // Disable all interactive elements
                const interactiveElements = [
                    typedText,
                    checkBtn,
                    languageDropdown,
                    translateBtn,
                    saveBtn
                ];

                interactiveElements.forEach(element => {
                    if (element) {
                        element.disabled = true;
                    }
                });

                // Show message that translations have been submitted for today
                setTimeout(() => {
                    Swal.fire({
                        icon: 'info',
                        title: 'Daily Limit Reached',
                        html: `
                            <p>You have already submitted your 50 translations for today.</p>
                            <p>Your account will be ready for more translations at 12:00 AM.</p>
                        `,
                        confirmButtonText: 'OK'
                    });
                }, 1000);

                // Set the submit button to disabled
                if (submitButton) {
                    submitButton.disabled = true;
                }
            } else if (todayTranslationCount === 0) {
                // It's a new day, make sure we start fresh
                console.log('Starting fresh with 0 translations for today');

                // Clear any stale data
                localStorage.removeItem('instra_translations_submitted');
                localStorage.removeItem('instra_submission_date');

                // Enable all interactive elements
                const interactiveElements = [
                    typedText,
                    checkBtn,
                    languageDropdown,
                    translateBtn,
                    saveBtn
                ];

                interactiveElements.forEach(element => {
                    if (element) {
                        element.disabled = false;
                    }
                });

                // Disable submit button until 50 translations are completed
                if (submitButton) {
                    submitButton.disabled = true;
                }
            }
        } catch (error) {
            console.error('Error loading translation count:', error);

            // Check if we have a stored date that matches today
            const now = new Date();
            const currentDate = now.toLocaleDateString('en-US');
            const storedDate = localStorage.getItem('instra_daily_translation_date');

            if (storedDate === currentDate) {
                // Use stored count for today
                todayTranslationCount = parseInt(localStorage.getItem('instra_translation_count'), 10) || 0;
            } else {
                // It's a new day or no stored date, start fresh
                todayTranslationCount = 0;
                localStorage.setItem('instra_translation_count', '0');
                localStorage.setItem('instra_daily_translation_date', currentDate);
            }
        }

        // Update the UI with the current count
        if (todayTranslationsElement) {
            todayTranslationsElement.textContent = todayTranslationCount;
        }

        // Update counter in the progress circle
        const dayNumberElement = document.querySelector('.day-number');
        if (dayNumberElement) {
            dayNumberElement.textContent = todayTranslationCount;
        }

        // Update translations left
        const translationsLeftElement = document.getElementById('translationsLeft');
        if (translationsLeftElement) {
            translationsLeftElement.textContent = (50 - todayTranslationCount).toString();
        }

        // Enable/disable the submit button based on the count
        if (submitButton) {
            submitButton.disabled = todayTranslationCount < 50;
        }

        console.log('Checking plan status...');
        const canWork = await checkPlanStatus();

        // Check if online before loading initial text
        if (!isUserOnline()) {
            console.log('Device is offline, cannot load initial text');
            Swal.fire({
                icon: 'warning',
                title: 'You\'re Offline',
                text: 'Some features require an internet connection. Please check your connection to access all translation features.',
                confirmButtonText: 'OK'
            });
        } else if (canWork) {
            console.log('Loading new text...');
            loadNewText();
        }

        // Initialize translation progress tracking
        initTranslationProgress();

        // Check if user has copy/paste permission using the new permission system
        if (auth.currentUser) {
            checkCopyPastePermission().then(permissionStatus => {
                console.log('Copy/paste permission status:', permissionStatus);

                // Set the permission flag based on the check result
                let hasCopyPastePermission = permissionStatus.hasPermission;

                // Show appropriate messages based on permission status
                if (hasCopyPastePermission) {
                    // Permission is active
                    if (permissionStatus.reason === 'initial_enabled') {
                        // Just enabled initial permission
                        console.log(`Initial copy/paste permission enabled for ${permissionStatus.daysGranted} days based on ${permissionStatus.planName} plan`);

                        // Show notification
                        Swal.fire({
                            title: 'Copy/Paste Enabled',
                            html: `
                                <p>Based on your ${permissionStatus.planName} plan, you now have copy/paste access for <strong>${permissionStatus.daysGranted} days</strong>.</p>
                                <p>After this period, you'll need to have <strong>3 successful referrals</strong> to maintain copy/paste access.</p>
                            `,
                            icon: 'success',
                            confirmButtonText: 'Got it'
                        });
                    } else if (permissionStatus.reason === 'referral_enabled' || permissionStatus.reason === 'referral_extended') {
                        // Enabled or extended based on referrals
                        console.log('Copy/paste permission enabled/extended based on referrals');
                    } else {
                        // Regular active permission
                        if (permissionStatus.daysRemaining) {
                            console.log(`Copy/paste permission active. Expires in ${permissionStatus.daysRemaining} days`);
                        }
                    }
                } else {
                    // No permission
                    if (permissionStatus.reason === 'expired_no_referrals') {
                        console.log(`Copy/paste permission expired. Need ${permissionStatus.requiredReferrals} referrals to regain access`);

                        // Show notification if this is the first time seeing this message
                        const expiredShown = localStorage.getItem('copyPasteExpiredShown');
                        if (!expiredShown) {
                            Swal.fire({
                                title: 'Copy/Paste Access Expired',
                                html: `
                                    <p>Your copy/paste access has expired.</p>
                                    <p>To regain access, you need to complete <strong>${permissionStatus.requiredReferrals} successful referrals</strong> during your plan validity period.</p>
                                    <p>Until then, you'll need to manually type the verification text.</p>
                                `,
                                icon: 'info',
                                confirmButtonText: 'Got it'
                            });
                            localStorage.setItem('copyPasteExpiredShown', 'true');
                        }
                    } else if (permissionStatus.reason === 'no_permission') {
                        console.log(`No copy/paste permission. Have ${permissionStatus.currentReferrals}/${permissionStatus.requiredReferrals} referrals`);
                    } else if (permissionStatus.reason === 'trial_plan') {
                        console.log('Trial plan does not have copy/paste permission');
                    }
                }

                // Show or hide copy/paste buttons based on permission
                if (copyTextBtn) {
                    copyTextBtn.style.display = hasCopyPastePermission ? 'inline-flex' : 'none';
                }
                if (pasteTextBtn) {
                    pasteTextBtn.style.display = hasCopyPastePermission ? 'inline-flex' : 'none';
                }

                // Update textarea placeholder for users with copy/paste permission
                if (typedText) {
                    if (hasCopyPastePermission) {
                        typedText.placeholder = 'Typing is disabled. Please use paste functionality.';
                    } else {
                        typedText.placeholder = 'Type the text manually or paste if you have permission';
                    }
                }

                // Check if permission was just granted (by comparing with localStorage)
                const previousPermission = localStorage.getItem('copyPastePermission') === 'true';
                if (hasCopyPastePermission && !previousPermission) {
                    // Show notification that copy/paste is now enabled
                    Swal.fire({
                        title: 'Copy/Paste Enabled',
                        text: 'You can now use copy and paste functionality in the work page. Note that typing has been disabled - you can only paste text.',
                        icon: 'success',
                        timer: 5000,
                        showConfirmButton: false
                    });
                }
                // Save current permission state to localStorage
                localStorage.setItem('copyPastePermission', hasCopyPastePermission);

                // Get the English text container and the typed text input
                const englishTextContainer = document.querySelector('.sticky-english-text');

                // Apply or remove the no-select class based on permission
                if (englishTextContainer) {
                    // Remove any existing event listeners
                    englishTextContainer.removeEventListener('contextmenu', preventContextMenu);

                    if (hasCopyPastePermission) {
                        englishTextContainer.classList.remove('no-select');
                    } else {
                        englishTextContainer.classList.add('no-select');

                        // Prevent right-click context menu for non-approved users
                        englishTextContainer.addEventListener('contextmenu', preventContextMenu);
                    }
                }

                // Update the global permission flag
                userHasCopyPastePermission = hasCopyPastePermission;

                // Add paste event prevention for the typed text input
                if (typedText) {
                    // First, remove all existing event listeners
                    // This is a simplified approach - in a production environment, you'd want to
                    // keep track of which listeners were added and remove only those
                    const newTypedText = typedText.cloneNode(true);
                    typedText.parentNode.replaceChild(newTypedText, typedText);
                    typedText = newTypedText; // Update the reference

                    // Re-add event listeners for checking text
                    addTextCheckingEventListeners();

                    // Apply text input functionality based on user permission
                    setupTextInputFunctionality(typedText, hasCopyPastePermission);

                    // Add keyboard shortcut prevention (the function will check permissions internally)
                    document.addEventListener('keydown', preventCopyPasteShortcuts);

                    // If user has permission, add the paste button functionality
                    if (hasCopyPastePermission && pasteTextBtn) {
                        pasteTextBtn.addEventListener('click', async () => {
                            try {
                                const text = await navigator.clipboard.readText();
                                typedText.value = text;
                                typedText.dispatchEvent(new Event('input'));

                                // Show success message
                                Swal.fire({
                                    title: 'Text Pasted',
                                    text: 'Text has been pasted successfully.',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                });
                            } catch (err) {
                                console.error('Failed to read clipboard contents: ', err);
                                Swal.fire({
                                    title: 'Paste Failed',
                                    text: 'Could not access clipboard. Please try again.',
                                    icon: 'error',
                                    timer: 3000,
                                    showConfirmButton: false
                                });
                            }
                        });
                    }
                }

                // Show or hide the permission messages
                const copyPasteDisabledMsg = document.getElementById('copyPasteDisabledMsg');
                const copyPasteEnabledMsg = document.getElementById('copyPasteEnabledMsg');

                if (copyPasteDisabledMsg) {
                    copyPasteDisabledMsg.style.display = hasCopyPastePermission ? 'none' : 'inline-flex';

                    // Add click event to show contact info
                    if (!hasCopyPastePermission) {
                        // Customize message based on reason
                        let infoMessage = '';

                        if (permissionStatus.reason === 'admin_disabled') {
                            infoMessage = `
                                <p>Copy/paste functionality has been disabled by an administrator.</p>
                                <p>Please contact support for assistance:</p>
                                <p><a href="https://wa.me/+919741032211" target="_blank" class="support-link"><i class="fab fa-whatsapp"></i> WhatsApp Support</a></p>
                            `;
                        } else if (permissionStatus.reason === 'admin_permission_expired') {
                            infoMessage = `
                                <p>Your admin-granted copy/paste access has expired.</p>
                                <p>Please contact support for assistance:</p>
                                <p><a href="https://wa.me/+919741032211" target="_blank" class="support-link"><i class="fab fa-whatsapp"></i> WhatsApp Support</a></p>
                            `;
                        } else {
                            infoMessage = `
                                <p>Copy/paste functionality is only available to approved users.</p>
                                <p>To get copy/paste access:</p>
                                <p>1. Upgrade to Junior, Senior, or Expert plan</p>
                                <p>2. Complete 3 successful referrals during your plan validity</p>
                                <p>Or contact support for assistance:</p>
                                <p><a href="https://wa.me/+919741032211" target="_blank" class="support-link"><i class="fab fa-whatsapp"></i> WhatsApp Support</a></p>
                            `;
                        }

                        copyPasteDisabledMsg.addEventListener('click', () => {
                            Swal.fire({
                                title: 'Copy/Paste Access',
                                html: infoMessage,
                                icon: 'info',
                                confirmButtonText: 'OK'
                            });
                        });
                    }
                }

                // Show or hide the enabled message with days remaining
                if (copyPasteEnabledMsg) {
                    if (hasCopyPastePermission) {
                        // Get days remaining from permission status
                        let daysText = '';
                        if (permissionStatus.daysRemaining) {
                            daysText = ` (${permissionStatus.daysRemaining} days left)`;
                        } else if (permissionStatus.expiryDate) {
                            const expiryDate = permissionStatus.expiryDate;
                            const currentDate = new Date();
                            const daysRemaining = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
                            daysText = ` (${daysRemaining} days left)`;
                        }

                        // Add source information if available
                        const sourceText = permissionStatus.source === 'admin' ? ' (admin granted)' : '';

                        // Update the message text and show it
                        copyPasteEnabledMsg.innerHTML = `<i class="fas fa-check-circle"></i> Copy/paste enabled${daysText}${sourceText} (typing disabled)`;
                        copyPasteEnabledMsg.style.display = 'inline-flex';

                        // Highlight the message if permission was just granted
                        if (permissionStatus.reason === 'initial_enabled' || permissionStatus.reason === 'referral_enabled') {
                            copyPasteEnabledMsg.classList.add('highlight-permission');
                            setTimeout(() => {
                                copyPasteEnabledMsg.classList.remove('highlight-permission');
                            }, 5000);
                        }
                    } else {
                        copyPasteEnabledMsg.style.display = 'none';
                    }
                }

                console.log('Copy/paste permission:', hasCopyPastePermission ? 'Enabled' : 'Disabled');
            }).catch(error => {
                console.error('Error checking copy/paste permission:', error);
            });
        }

        // Load and update total translations count directly from Firebase
        try {
            const user = auth.currentUser;
            if (user && totalTranslationsElement) {
                // Fetch directly from Firebase for the most accurate count
                getTotalTranslationsCount().then(totalTranslations => {
                    if (totalTranslationsElement) {
                        totalTranslationsElement.textContent = totalTranslations;
                        console.log('Updated total translations count directly from Firebase:', totalTranslations);
                    }
                }).catch(error => {
                    console.error('Error fetching total translations count directly:', error);
                });
            }
        } catch (error) {
            console.error('Error loading total translations count:', error);
        }

        console.log('Work initialization complete, hiding loading overlay...');
        // Hide loading overlay and show ready message
        hideLoading();
    } catch (error) {
        console.error('Error initializing work:', error);
        console.log('Error occurred, still hiding loading overlay...');
        hideLoading(); // Still hide loading overlay even if there's an error
    }
}

// Language Dropdown Change Event
if (languageDropdown) {
    languageDropdown.addEventListener('change', () => {
        // Check if online before validating language selection
        if (!isUserOnline()) {
            console.log('Device is offline, cannot validate language selection');
            Swal.fire({
                icon: 'warning',
                title: 'You\'re Offline',
                text: 'Cannot validate language selection while offline. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Check if the selected language matches the displayed language
        const selectedLanguage = languageDropdown.value;
        const displayedLanguage = randomLanguage.textContent;

        // Check if the language selection is correct
        const isLanguageCorrect = selectedLanguage === displayedLanguage;

        // Show language validation result
        if (!isLanguageCorrect) {
            languageError.textContent = `Please select ${displayedLanguage} from the dropdown.`;
            languageError.classList.remove("hidden");

            // Highlight the dropdown to draw attention
            languageDropdown.classList.add("error-border");
        } else {
            // Hide language error
            languageError.classList.add("hidden");
            languageDropdown.classList.remove("error-border");
        }

        // Check if the typed text is correct using normalized comparison
        const typedTextValue = typedText.value.trim();
        const displayedText = currentEnglishText.trim();
        const normalizedTypedText = normalizeTextForComparison(typedTextValue);
        const normalizedDisplayedText = normalizeTextForComparison(displayedText);
        const isTextCorrect = normalizedTypedText === normalizedDisplayedText;

        // Only enable the translate button if both text and language are correct
        translateBtn.disabled = !(isTextCorrect && isLanguageCorrect);
    });
}

// Note: Check Button Logic is now handled by addTextCheckingEventListeners()

// Translate Button Logic
if (translateBtn) {
    translateBtn.addEventListener('click', async () => {
        try {
            // Check if online before translating
            if (!isUserOnline()) {
                console.log('Device is offline, cannot translate');
                Swal.fire({
                    icon: 'warning',
                    title: 'You\'re Offline',
                    text: 'Cannot process translations while offline. Please check your internet connection and try again.',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Clear any previous translation results first
            translatedOutput.classList.add("hidden");
            translatedOutput.textContent = "";

            // Disable the translate button during translation
            translateBtn.disabled = true;

            // Show loading animation with SweetAlert2
            Swal.fire({
                title: 'Translating',
                html: `
                    <div class="loading-spinner"></div>
                    <p style="margin-top: 15px; color: #666;">Translating from English to ${currentTargetLanguage}...</p>
                `,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                timer: 3000, // Show for exactly 3 seconds
                timerProgressBar: false, // Hide the timer progress bar
                didOpen: () => {
                    // Hide the default loading indicator and use our custom spinner
                    Swal.hideLoading();
                }
            });

            // Get the translation
            const translationItem = getRandomTranslationItem();
            let translatedText = "";

            if (translationItem && translationItem[currentTargetLanguage]) {
                translatedText = translationItem[currentTargetLanguage];
            } else {
                translatedText = `[Translation to ${currentTargetLanguage}]: ${currentEnglishText.substring(0, 50)}...`;
            }

            // Wait for the 3-second timer to complete
            await new Promise(resolve => {
                const checkSwalClosed = setInterval(() => {
                    if (!Swal.isVisible()) {
                        clearInterval(checkSwalClosed);
                        resolve();
                    }
                }, 100);
            });

            // Display the translation result
            translatedOutput.textContent = translatedText;
            translatedOutput.classList.remove("hidden");

            // Show and enable the save button only if we haven't reached 50 translations
            if (todayTranslationCount < 50) {
                saveBtn.classList.remove("hidden");
                saveBtn.disabled = false;
            } else {
                // If we've reached 50, keep save button hidden and disabled
                saveBtn.classList.add("hidden");
                saveBtn.disabled = true;
            }

            // Re-enable the translate button
            translateBtn.disabled = false;
        } catch (error) {
            console.error('Error translating text:', error);

            // Close any open SweetAlert
            Swal.close();

            // Show error message
            translatedOutput.textContent = "Error translating text. Please try again.";
            translatedOutput.classList.remove("hidden");

            // Re-enable the translate button
            translateBtn.disabled = false;
        }
    });
}

// Save Button Logic
if (saveBtn) {
    saveBtn.addEventListener('click', async () => {
        try {
            // Check if online before saving
            if (!isUserOnline()) {
                console.log('Device is offline, cannot save translation');
                Swal.fire({
                    icon: 'warning',
                    title: 'You\'re Offline',
                    text: 'Cannot save translations while offline. Please check your internet connection and try again.',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Check if we've already reached 50 translations
            if (todayTranslationCount >= 50) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Daily Limit Reached',
                    html: `
                        <p>You have already completed 50 translations for today.</p>
                        <p>Please click the <strong>Submit</strong> button to submit your work and earn your payment.</p>
                    `,
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Disable the save button immediately to prevent double-clicks
            saveBtn.disabled = true;

            // Increment the counter
            todayTranslationCount++;

            // IMPORTANT: We're only storing the count locally until submit is clicked
            // No API call is made here to improve performance and reduce API usage
            // API calls to update the count in Firebase are ONLY made when the submit button is clicked
            console.log('Incremented local translation count to:', todayTranslationCount);

            // Check if we've reached 50 translations
            if (todayTranslationCount >= 50) {
                // Enable the submit button
                if (submitButton) {
                    submitButton.disabled = false;
                    // Add a pulsing effect to draw attention
                    submitButton.classList.add('pulse-button');
                }
            }

            // Save the count to localStorage
            localStorage.setItem('instra_translation_count', todayTranslationCount.toString());

            // Update translation progress for logout protection
            updateTranslationProgress(todayTranslationCount);

            // Update the UI
            if (todayTranslationsElement) {
                todayTranslationsElement.textContent = todayTranslationCount;
            }

            // Update counter in the progress circle
            const dayNumberElement = document.querySelector('.day-number');
            if (dayNumberElement) {
                dayNumberElement.textContent = todayTranslationCount;
            }

            // Update translations left
            const translationsLeftElement = document.getElementById('translationsLeft');
            if (translationsLeftElement) {
                translationsLeftElement.textContent = (50 - todayTranslationCount).toString();
            }

            // Enable the submit button if we've reached 50 translations
            if (todayTranslationCount >= 50 && submitButton) {
                submitButton.disabled = false;
            }

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Saved!',
                html: `
                    <p>Translation saved successfully!</p>
                    <p>Progress: <strong>${todayTranslationCount}/50</strong></p>
                    ${todayTranslationCount === 50 ? '<p><strong>You have completed all 50 translations!</strong> Please submit your work.</p>' : ''}
                `,
                timer: 1500,
                showConfirmButton: false
            });

            // Get the next translation item from our pre-loaded data
            try {
                // Use the global function that was made available
                if (typeof getNextTranslationItem === 'function') {
                    const nextItem = getNextTranslationItem();
                    if (nextItem) {
                        currentEnglishText = nextItem.english;
                        currentTargetLanguage = getRandomItem(availableLanguages);
                    } else {
                        console.error('No next translation item available');
                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'Data Loading Error',
                            text: 'Failed to load next translation. Please refresh the page.',
                            confirmButtonText: 'Refresh Page',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.reload();
                            }
                        });
                    }
                } else {
                    console.error('getNextTranslationItem function not available');
                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Data Loading Error',
                        text: 'Failed to load next translation. Please refresh the page.',
                        confirmButtonText: 'Refresh Page',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.reload();
                        }
                    });
                }
            } catch (error) {
                console.error('Error getting next translation:', error);
            }

            // Reset the page for a new translation, but skip refreshing the count from localStorage
            // This ensures we keep the incremented count we just saved
            resetPage(true);

            // If we've reached 50 translations, show a message to submit
            if (todayTranslationCount === 50) {
                setTimeout(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Daily Limit Reached',
                        html: `
                            <p>Congratulations! You have completed all 50 translations for today.</p>
                            <p>Please click the <strong>Submit</strong> button to submit your work and earn your payment.</p>
                            <p>You will not be able to do more translations until tomorrow (12:00 AM).</p>
                        `,
                        confirmButtonText: 'OK'
                    });

                    // Disable all interactive elements
                    typedText.disabled = true;
                    checkBtn.disabled = true;
                    translateBtn.disabled = true;
                    saveBtn.disabled = true;
                    languageDropdown.disabled = true;
                }, 1800); // Show after the success message disappears
            }
        } catch (error) {
            console.error('Error saving translation:', error);

            // Show error message
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while saving the translation',
                confirmButtonText: 'OK'
            });

            // Re-enable the save button in case of error
            saveBtn.disabled = false;
        }
    });
}

// Submit Button Logic
if (submitButton) {
    submitButton.addEventListener('click', async () => {
        // Check if online before submitting
        if (!isUserOnline()) {
            console.log('Device is offline, cannot submit translations');
            Swal.fire({
                icon: 'warning',
                title: 'You\'re Offline',
                text: 'Cannot submit translations while offline. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Double-check that we have 50 translations before submitting
        if (todayTranslationCount < 50) {
            console.log('Not enough translations to submit:', todayTranslationCount);
            Swal.fire({
                icon: 'warning',
                title: 'Not Ready to Submit',
                html: `
                    <p>You have only completed ${todayTranslationCount} out of 50 required translations.</p>
                    <p>Please complete all 50 translations before submitting.</p>
                `,
                confirmButtonText: 'OK'
            });
            return;
        }

        // First check if translations have already been submitted for today
        const limitStatus = await checkDailyTranslationLimit();
        if (limitStatus.alreadySubmitted) {
            Swal.fire({
                icon: 'warning',
                title: 'Already Submitted',
                html: `
                    <p>You have already submitted your translations for today.</p>
                    <p>The submit button can only be used once per day.</p>
                    <p>Please come back tomorrow for more translations.</p>
                `,
                confirmButtonText: 'OK'
            });

            // Disable the submit button
            submitButton.disabled = true;
            return;
        }

        // Check if the user has completed 50 translations
        if (todayTranslationCount < 50) {
            Swal.fire({
                icon: 'warning',
                title: 'Incomplete Work',
                html: `
                    <p>You have only completed <strong>${todayTranslationCount}/50</strong> translations.</p>
                    <p>Please complete all 50 translations before submitting.</p>
                `,
                confirmButtonText: 'OK'
            });
            return;
        }

        // Show confirmation dialog
        const result = await Swal.fire({
            title: 'Submit Translations',
            text: 'Are you sure you want to submit your translations?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Submit',
            cancelButtonText: 'Cancel'
        });

        if (result.isConfirmed) {
            try {
                // Show loading message
                Swal.fire({
                    title: 'Processing',
                    text: 'Submitting your translations and updating earnings...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Calculate earnings based on plan type
                // Trial: ₹25, Junior: ₹150, Senior: ₹250, Executive/Expert: ₹400
                // Default to Trial plan if not specified
                let earningsAmount = 25; // Default to Trial plan rate

                // Get user's plan from the header if available
                const planElement = document.querySelector('.active-plan');
                if (planElement) {
                    const planName = planElement.textContent.trim();
                    if (planName === 'Junior') {
                        earningsAmount = 150;
                    } else if (planName === 'Senior') {
                        earningsAmount = 250;
                    } else if (planName === 'Executive' || planName === 'Expert') {
                        earningsAmount = 400;
                    }
                }

                // IMPORTANT: This is where the API call to Firebase is made
                // We only update Firebase when the submit button is clicked, not on each save
                // This reduces API usage and improves performance
                try {
                    // Get current user
                    const user = auth.currentUser;
                    if (user) {
                        const userRef = doc(db, 'users', user.uid);
                        const userSnap = await getDoc(userRef);

                        if (userSnap.exists()) {
                            const userData = userSnap.data();
                            const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

                            // Calculate total translations completed
                            // STANDARDIZED: Use only stats.totalTranslationsCompleted
                            const totalTranslations = (userData.stats?.totalTranslationsCompleted || 0) + 50;

                            // Update the total translations count in the UI
                            if (totalTranslationsElement) {
                                totalTranslationsElement.textContent = totalTranslations;
                                console.log('Updated total translations count in UI after submission:', totalTranslations);
                            }

                            // Calculate total earnings (for reference only)
                            const currentEarnings = userData.wallet?.earning || 0;
                            console.log(`Current earnings: ₹${currentEarnings}, Adding: ₹${earningsAmount}, New total: ₹${currentEarnings + earningsAmount}`);

                            // Mark translations as submitted in Firebase with earnings amount
                            // This will update all required fields in one call:
                            // - Set count to 50
                            // - Mark as submitted
                            // - Update total translations
                            // - Record earnings
                            const submissionResult = await markTranslationsAsSubmitted(earningsAmount);

                            if (!submissionResult.success) {
                                console.error('Failed to mark translations as submitted:', submissionResult.message);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Submission Error',
                                    text: submissionResult.message || 'Failed to mark translations as submitted. Please try again.',
                                    confirmButtonText: 'OK'
                                });
                                return;
                            }

                            // Update wallet with earnings (separate call for wallet updates)
                            const walletResult = await updateWalletWithEarnings(earningsAmount);

                            if (!walletResult.success) {
                                console.error('Failed to update wallet with earnings:', walletResult.message);
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Wallet Update Warning',
                                    html: `
                                        <p>Your translations were submitted successfully, but there was an issue updating your wallet.</p>
                                        <p>Error: ${walletResult.message}</p>
                                        <p>Please contact support if your earnings are not reflected in your wallet.</p>
                                    `,
                                    confirmButtonText: 'OK'
                                });
                                // Continue with the process even if wallet update fails
                            }

                            console.log('Translations marked as submitted with count=50 and earnings updated');

                            // Create a transaction record in the transactions collection
                            try {
                                console.log('Creating transaction record for earnings...');
                                const transactionRef = await addDoc(collection(db, 'transactions'), {
                                    userId: user.uid,
                                    timestamp: serverTimestamp(),
                                    type: 'earnings',  // Use 'earnings' to match the filter in transactions.js
                                    amount: earningsAmount,
                                    status: 'completed',
                                    description: `Completed 50 translations on ${currentDate}`
                                });
                                console.log('Transaction record created successfully with ID:', transactionRef.id);
                            } catch (transactionError) {
                                console.error('Error creating transaction record:', transactionError);
                                console.error('Transaction error details:', JSON.stringify(transactionError));
                                // Continue even if transaction creation fails - the wallet has already been updated
                            }

                            console.log('Updated Firebase with all 50 translations at once');

                            console.log('Successfully updated user data in Firebase');

                            // After a short delay, fetch the latest total translations count directly from Firebase
                            // This ensures the UI is updated with the most accurate count after the update completes
                            setTimeout(async () => {
                                try {
                                    const latestTotalTranslations = await getTotalTranslationsCount();
                                    if (totalTranslationsElement) {
                                        totalTranslationsElement.textContent = latestTotalTranslations;
                                        console.log('Updated total translations count from Firebase after submission:', latestTotalTranslations);
                                    }
                                } catch (error) {
                                    console.error('Error fetching latest total translations count:', error);
                                }
                            }, 2000); // Wait 2 seconds to allow the Firebase update to complete
                        } else {
                            console.warn('User document not found in Firestore');
                        }
                    } else {
                        console.warn('User not authenticated, skipping Firebase update');
                    }
                } catch (error) {
                    console.error('Error updating Firebase:', error);
                    // Continue anyway to not block the user
                }

                // Update the earnings display using the wallet.earning field with cache
                try {
                    // Get the updated wallet data using cache
                    const walletData = await getWalletDataWithCache();

                    // Update wallet displays with the latest data
                    updateWalletDisplays(walletData);

                    // Invalidate user data cache to force a refresh next time
                    if (auth.currentUser) {
                        // This will happen in the background in getWalletDataWithCache
                        console.log('Wallet data updated from cache');
                    }
                } catch (error) {
                    console.error('Error updating wallet display:', error);

                    // Fallback to the old method if there's an error
                    const earningWalletElement = document.querySelector('.earnings-card:nth-child(1) .amount');
                    if (earningWalletElement) {
                        // Get current earnings
                        const currentEarnings = parseFloat(earningWalletElement.textContent.replace('₹', '')) || 0;
                        // Add new earnings
                        const newEarnings = currentEarnings + earningsAmount;
                        // Update display
                        earningWalletElement.textContent = formatCurrency(newEarnings);
                    }
                }

                // Store the submission time to enforce daily limit
                const now = new Date();
                // Use the same standardized date format as in translation-limits.js
                const currentDate = now.toLocaleDateString('en-US', {
                    month: 'numeric',
                    day: 'numeric',
                    year: 'numeric'
                });
                console.log('Setting submission date in local storage:', currentDate);

                localStorage.setItem('instra_last_submission', now.toISOString());
                localStorage.setItem('instra_next_available', new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString());
                localStorage.setItem('instra_submission_date', currentDate);

                // Set the count to 50 to ensure consistency
                localStorage.setItem('instra_translation_count', '50');
                localStorage.setItem('instra_daily_translation_date', currentDate);

                // Reset translation progress
                resetTranslationProgress();

                // Set the submitted flag to true
                localStorage.setItem('instra_translations_submitted', 'true');

                // Close loading dialog
                Swal.close();

                // Load the next chunk of translation data for tomorrow
                try {
                    console.log('Loading next chunk of translation data...');
                    Swal.fire({
                        title: 'Loading New Data',
                        text: 'Loading new translation data for tomorrow...',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    await loadNextChunk();
                    console.log('Next chunk loaded successfully');
                    Swal.close();
                } catch (error) {
                    console.error('Error loading next chunk:', error);
                    Swal.close();
                }

                // Get fresh data from Firebase after submit
                try {
                    // Add a small delay to ensure Firebase updates have propagated
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Refresh user data from Firebase with cache invalidation
                    const freshUserData = await loadUserDataWithCache(true); // Pass true to force a fresh fetch

                    // Update the UI with fresh data
                    if (freshUserData) {
                        console.log('Got fresh user data after submission:', freshUserData);

                        // Update total translations count
                        if (totalTranslationsElement && freshUserData.stats?.totalTranslationsCompleted) {
                            totalTranslationsElement.textContent = freshUserData.stats.totalTranslationsCompleted;
                            console.log('Updated total translations count to:', freshUserData.stats.totalTranslationsCompleted);
                        }

                        // Update today's translations count (should be 50)
                        if (todayTranslationsElement && freshUserData.stats?.dailyTranslations?.count) {
                            todayTranslationsElement.textContent = freshUserData.stats.dailyTranslations.count;
                            todayTranslationCount = freshUserData.stats.dailyTranslations.count;
                            console.log('Updated today\'s translation count to:', freshUserData.stats.dailyTranslations.count);
                        } else {
                            // Fallback to 50 if not available
                            todayTranslationCount = 50;
                            if (todayTranslationsElement) {
                                todayTranslationsElement.textContent = '50';
                            }
                            console.log('Using fallback today\'s translation count: 50');
                        }

                        // Update counter in the progress circle
                        const dayNumberElement = document.querySelector('.day-number');
                        if (dayNumberElement) {
                            dayNumberElement.textContent = todayTranslationCount.toString();
                        }

                        // Update translations left (should be 0)
                        const translationsLeftElement = document.getElementById('translationsLeft');
                        if (translationsLeftElement) {
                            translationsLeftElement.textContent = '0';
                        }

                        // Check if the wallet was updated correctly
                        if (freshUserData.wallet && freshUserData.wallet.earning !== undefined) {
                            console.log('Wallet earning balance after submission:', freshUserData.wallet.earning);
                            // Update wallet displays with the latest data
                            updateWalletDisplays(freshUserData.wallet);
                        } else {
                            console.warn('Wallet data not found in fresh user data');
                        }
                    } else {
                        console.warn('Could not get fresh user data, using default values');
                        // Set default values if fresh data not available
                        todayTranslationCount = 50;
                        if (todayTranslationsElement) {
                            todayTranslationsElement.textContent = '50';
                        }

                        // Update counter in the progress circle
                        const dayNumberElement = document.querySelector('.day-number');
                        if (dayNumberElement) {
                            dayNumberElement.textContent = '50';
                        }

                        // Update translations left
                        const translationsLeftElement = document.getElementById('translationsLeft');
                        if (translationsLeftElement) {
                            translationsLeftElement.textContent = '0';
                        }
                    }
                } catch (error) {
                    console.error('Error getting fresh data from Firebase:', error);
                    // Set default values if there's an error
                    todayTranslationCount = 50;
                    if (todayTranslationsElement) {
                        todayTranslationsElement.textContent = '50';
                    }

                    // Try to get wallet data separately
                    try {
                        const walletData = await getWalletDataWithCache(true); // Force fresh fetch
                        if (walletData) {
                            console.log('Got wallet data separately:', walletData);
                            updateWalletDisplays(walletData);
                        }
                    } catch (walletError) {
                        console.error('Error getting wallet data separately:', walletError);
                    }
                }

                // Show success message
                Swal.fire({
                    title: 'Success!',
                    html: `
                        <p>Your translations have been submitted successfully.</p>
                        <p>You earned <strong>₹${earningsAmount}</strong> for completing 50 translations!</p>
                        <p>Your earnings have been added to your wallet.</p>
                        <p>New translation data has been loaded for tomorrow.</p>
                    `,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Disable all interactive elements to prevent further translations today
                    const interactiveElements = [
                        typedText,
                        checkBtn,
                        languageDropdown,
                        translateBtn,
                        saveBtn,
                        submitButton
                    ];

                    interactiveElements.forEach(element => {
                        if (element) {
                            element.disabled = true;
                        }
                    });

                    // Show daily limit message
                    Swal.fire({
                        title: 'Daily Limit Reached',
                        html: `
                            <p>You have reached your daily limit of 50 translations.</p>
                            <p>Your account will be ready for more translations at 12:00 AM.</p>
                            <p>New translation data has been loaded for tomorrow.</p>
                        `,
                        icon: 'info',
                        confirmButtonText: 'Go to Dashboard',
                    }).then(() => {
                        // Redirect to dashboard
                        window.location.href = 'dashboard.html';
                    });
                });
            } catch (error) {
                console.error('Error submitting translations:', error);

                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred while submitting your translations. Please try again.',
                    confirmButtonText: 'OK'
                });
            }
        }
    });
}

// Copy Button Logic
if (copyTextBtn) {
    copyTextBtn.addEventListener('click', async (e) => {
        e.preventDefault(); // Prevent any default action

        // Check if online before copying text
        if (!isUserOnline()) {
            console.log('Device is offline, cannot copy text');
            Swal.fire({
                icon: 'warning',
                title: 'You\'re Offline',
                text: 'Cannot copy text while offline. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Check permission first
        const user = auth.currentUser;
        if (!user) {
            console.warn('User not logged in, cannot use copy feature');
            return;
        }

        // Verify permission from Firebase
        try {
            const userDoc = await getDoc(doc(db, 'users', user.uid));
            if (!userDoc.exists() || userDoc.data().copyPastePermission !== true) {
                console.warn('User does not have copy/paste permission');
                Swal.fire({
                    icon: 'error',
                    title: 'Permission Denied',
                    text: 'You do not have permission to use the copy feature. Please contact support if you need this feature.',
                    timer: 3000,
                    showConfirmButton: false
                });
                return;
            }
        } catch (error) {
            console.error('Error checking copy permission:', error);
            return;
        }

        // Get the text to copy
        const textToCopy = currentEnglishText;

        try {
            // Create a temporary textarea element
            const tempTextArea = document.createElement('textarea');
            tempTextArea.value = textToCopy;

            // Make the textarea out of the viewport
            tempTextArea.style.position = 'fixed';
            tempTextArea.style.left = '-999999px';
            tempTextArea.style.top = '-999999px';
            document.body.appendChild(tempTextArea);

            // Select and copy the text
            tempTextArea.focus();
            tempTextArea.select();
            document.execCommand('copy');

            // Remove the temporary element
            document.body.removeChild(tempTextArea);

            // Show success feedback
            const originalIcon = copyTextBtn.innerHTML;
            copyTextBtn.innerHTML = '<i class="fas fa-check"></i>';
            copyTextBtn.style.color = '#4CAF50';

            // Reset the button after a short delay
            setTimeout(() => {
                copyTextBtn.innerHTML = originalIcon;
                copyTextBtn.style.color = '';
            }, 1500);

            console.log('Text copied to clipboard using execCommand');
        } catch (err) {
            console.error('Failed to copy text: ', err);

            // Fallback to clipboard API if execCommand fails
            try {
                await navigator.clipboard.writeText(textToCopy);

                // Show success feedback
                const originalIcon = copyTextBtn.innerHTML;
                copyTextBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyTextBtn.style.color = '#4CAF50';

                // Reset the button after a short delay
                setTimeout(() => {
                    copyTextBtn.innerHTML = originalIcon;
                    copyTextBtn.style.color = '';
                }, 1500);

                console.log('Text copied to clipboard using Clipboard API');
            } catch (clipboardErr) {
                console.error('Failed to copy text with Clipboard API: ', clipboardErr);

                // Show error feedback
                const originalIcon = copyTextBtn.innerHTML;
                copyTextBtn.innerHTML = '<i class="fas fa-times"></i>';
                copyTextBtn.style.color = '#ff6b6b';

                // Reset the button after a short delay
                setTimeout(() => {
                    copyTextBtn.innerHTML = originalIcon;
                    copyTextBtn.style.color = '';
                }, 1500);
            }
        }
    });
}

// Paste Button Logic
if (pasteTextBtn) {
    pasteTextBtn.addEventListener('click', async (e) => {
        e.preventDefault(); // Prevent any default action

        // Check if online before pasting text
        if (!isUserOnline()) {
            console.log('Device is offline, cannot paste text');
            Swal.fire({
                icon: 'warning',
                title: 'You\'re Offline',
                text: 'Cannot paste text while offline. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Verify permission from Firebase
        try {
            const user = auth.currentUser;
            if (!user) {
                console.warn('User not authenticated');
                return;
            }

            const userDoc = await getDoc(doc(db, 'users', user.uid));
            if (!userDoc.exists() || userDoc.data().copyPastePermission !== true) {
                console.warn('User does not have copy/paste permission');
                Swal.fire({
                    icon: 'error',
                    title: 'Permission Denied',
                    text: 'You do not have permission to use the paste feature. Please contact support if you need this feature.',
                    timer: 3000,
                    showConfirmButton: false
                });
                return;
            }
        } catch (error) {
            console.error('Error checking paste permission:', error);
            return;
        }

        try {
            // Make the text area not read-only to allow paste
            if (typedText) {
                typedText.readOnly = false;
                typedText.focus();
            }

            // Try to read from clipboard directly
            navigator.clipboard.readText()
                .then(text => {
                    if (text) {
                        // Set the text in the textarea
                        typedText.value = text;

                        // Show success feedback
                        const originalIcon = pasteTextBtn.innerHTML;
                        pasteTextBtn.innerHTML = '<i class="fas fa-check"></i>';
                        pasteTextBtn.style.color = '#4CAF50';

                        // Reset the button after a short delay
                        setTimeout(() => {
                            pasteTextBtn.innerHTML = originalIcon;
                            pasteTextBtn.style.color = '';
                        }, 1500);

                        console.log('Text pasted directly from clipboard');

                        // Trigger the check button if the text matches using normalized comparison
                        const normalizedTypedText = normalizeTextForComparison(text.trim());
                        const normalizedDisplayedText = normalizeTextForComparison(currentEnglishText.trim());

                        if (normalizedTypedText === normalizedDisplayedText) {
                            checkBtn.click();
                        }
                    }
                })
                .catch(err => {
                    console.error('Failed to read clipboard contents: ', err);

                    // Instead of showing a dialog, just focus the text area and show a notification
                    // to encourage the user to paste directly
                    if (typedText) {
                        // Make sure the text area is not read-only for this operation
                        const wasReadOnly = typedText.readOnly;
                        if (wasReadOnly) {
                            typedText.readOnly = false;
                        }

                        // Focus the text area
                        typedText.focus();

                        // Show a toast notification
                        Swal.fire({
                            icon: 'info',
                            title: 'Paste Directly',
                            text: 'Please paste the text directly into the input field using Ctrl+V or right-click > Paste',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 4000
                        });

                        // Restore read-only state if needed
                        setTimeout(() => {
                            if (wasReadOnly) {
                                typedText.readOnly = true;
                            }
                        }, 100);
                    }
                });
        } catch (err) {
            console.error('Failed to use clipboard API: ', err);

            // Show error feedback
            const originalIcon = pasteTextBtn.innerHTML;
            pasteTextBtn.innerHTML = '<i class="fas fa-times"></i>';
            pasteTextBtn.style.color = '#ff6b6b';

            // Reset the button after a short delay
            setTimeout(() => {
                pasteTextBtn.innerHTML = originalIcon;
                pasteTextBtn.style.color = '';
            }, 1500);

            // Show error message to user
            Swal.fire({
                icon: 'error',
                title: 'Paste Failed',
                text: 'Please try pasting manually into the text area.',
                timer: 3000,
                showConfirmButton: false
            });

            // Focus the text area so user can paste manually
            if (typedText) {
                typedText.focus();
            }
        }
    });
}

// Import the shared plan upgrade dialog
import { showPlanUpgradeDialog } from './plan-upgrade-dialog.js';

// Upgrade Plan Button Logic
const upgradeBtn = document.querySelector('.upgrade-btn');
if (upgradeBtn) {
    upgradeBtn.addEventListener('click', function() {
        showPlanUpgradeDialog();
    });
}

// The plan upgrade dialog and WhatsApp support functions have been moved to plan-upgrade-dialog.js

// Start the initialization process
initializeWork();

// Add a fallback to ensure the loading overlay is hidden
// This will hide the overlay after 15 seconds even if initialization fails
setTimeout(() => {
    if (loadingOverlay && loadingOverlay.style.display !== 'none') {
        console.warn('Fallback: Forcing loading overlay to hide after timeout');
        hideLoading();
    }
}, 15000);
