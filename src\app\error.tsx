'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="text-center">
        <div className="mb-8">
          <Image
            src="/img/instra-logo.svg"
            alt="Instra Global Logo"
            width={80}
            height={80}
            className="mx-auto mb-4"
          />
          <h1 className="text-4xl font-bold text-white mb-4">Oops!</h1>
          <h2 className="text-xl font-semibold text-white mb-2">Something went wrong</h2>
          <p className="text-white/80 mb-8 max-w-md mx-auto">
            We encountered an unexpected error. Please try again or contact support if the problem persists.
          </p>

          {/* Support Contact */}
          <div className="mb-8">
            <p className="text-white/60 mb-4">Need immediate help?</p>
            <div className="flex justify-center">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <i className="fas fa-envelope mr-2"></i>
                Email Support
              </a>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <button
            onClick={reset}
            className="btn-primary inline-flex items-center"
          >
            <i className="fas fa-redo mr-2"></i>
            Try Again
          </button>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="btn-secondary inline-flex items-center"
            >
              <i className="fas fa-home mr-2"></i>
              Go Home
            </Link>
            <Link
              href="/dashboard"
              className="btn-secondary inline-flex items-center"
            >
              <i className="fas fa-tachometer-alt mr-2"></i>
              Dashboard
            </Link>
          </div>
        </div>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-8 text-left">
            <summary className="text-white/60 cursor-pointer">Error Details (Development)</summary>
            <pre className="mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto">
              {error.message}
              {error.stack && '\n\n' + error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  )
}
