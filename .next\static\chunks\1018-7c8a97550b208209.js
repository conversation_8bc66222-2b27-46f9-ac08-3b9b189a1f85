"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1018],{12:(e,t,s)=>{s.d(t,{M4:()=>l,_f:()=>o});var a=s(6104),n=s(4752),r=s.n(n);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await r().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await a.j2.signOut(),r().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),r().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},6104:(e,t,s)=>{s.d(t,{db:()=>c,j2:()=>l});var a=s(3915),n=s(3004),r=s(5317),i=s(858);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,n.xI)(o),c=(0,r.aU)(o);(0,i.c7)(o)},6681:(e,t,s)=>{s.d(t,{Nu:()=>l,hD:()=>o,wC:()=>c});var a=s(2115),n=s(3004),r=s(6104),i=s(12);function o(){let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,n.hg)(r.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let l=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:l}}function l(){let{user:e,loading:t}=o();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[s,n]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");n(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||r,isAdmin:s}}},7460:(e,t,s)=>{s.d(t,{J:()=>r});var a=s(2115),n=s(3592);function r(e){let[t,s]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{e?o():i(!1)},[e]);let o=async()=>{try{i(!0);let t=await (0,n.iA)(e);s(t)}catch(e){console.error("Error checking for blocking notifications:",e),s(!1)}finally{i(!1)}};return{hasBlockingNotifications:t,isChecking:r,checkForBlockingNotifications:o,markAllAsRead:()=>{s(!1)}}}},8647:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(5155),n=s(2115),r=s(3592);function i(e){let{userId:t,onAllRead:s}=e,[i,o]=(0,n.useState)([]),[l,c]=(0,n.useState)(0),[d,u]=(0,n.useState)(!0);(0,n.useEffect)(()=>{t&&f()},[t]);let f=async()=>{try{u(!0);let e=await (0,r.AX)(t);o(e),0===e.length&&s()}catch(e){console.error("Error loading notifications:",e),s()}finally{u(!1)}},h=async()=>{let e=i[l];(null==e?void 0:e.id)&&(await (0,r.bA)(e.id,t),l<i.length-1?c(l+1):s())};if(d)return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===i.length)return null;let g=i[l];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(g.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,a.jsxs)("p",{className:"text-blue-100 text-sm",children:[l+1," of ",i.length," notifications"]})]})]}),(0,a.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:g.title}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-gray-800 leading-relaxed",children:g.message})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,a.jsxs)("span",{children:["From: ",g.createdBy]}),(0,a.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(g.createdAt)})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[l+1,"/",i.length]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((l+1)/i.length*100,"%")}})})]}),(0,a.jsxs)("button",{onClick:h,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,a.jsx)("i",{className:"fas fa-check"}),(0,a.jsx)("span",{children:l<i.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)("i",{className:"fas fa-info-circle"}),(0,a.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}}]);