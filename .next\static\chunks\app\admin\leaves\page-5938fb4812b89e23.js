(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9451],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>l,_f:()=>o});var r=a(6104),n=a(4752),s=a.n(n);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await s().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),s().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},1577:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(5155),n=a(2115),s=a(6874),i=a.n(s),o=a(6681),l=a(3737),c=a(4752),d=a.n(c);function u(){let{user:e,loading:t,isAdmin:s}=(0,o.wC)(),[c,u]=(0,n.useState)([]),[m,x]=(0,n.useState)(!0),[g,f]=(0,n.useState)(!1),[h,y]=(0,n.useState)(!1),[p,b]=(0,n.useState)({date:"",reason:"",type:"holiday"});(0,n.useEffect)(()=>{s&&v()},[s]);let v=async()=>{try{x(!0);let{getAdminLeaves:e}=await a.e(9567).then(a.bind(a,9567)),t=await e();u(t)}catch(e){console.error("Error loading leaves:",e),u([]),d().fire({icon:"error",title:"Error",text:"Failed to load leaves. Please try again."})}finally{x(!1)}},D=async()=>{try{if(!p.date||!p.reason.trim())return void d().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let t=new Date(p.date),r=new Date;if(r.setHours(0,0,0,0),t<r)return void d().fire({icon:"error",title:"Invalid Date",text:"Cannot create leave for past dates."});if(c.find(e=>e.date.toDateString()===t.toDateString()))return void d().fire({icon:"error",title:"Duplicate Leave",text:"Leave already exists for this date."});y(!0);let{createAdminLeave:n}=await a.e(9567).then(a.bind(a,9567));await n({date:t,reason:p.reason.trim(),type:p.type,createdBy:(null==e?void 0:e.email)||"admin"}),await v(),d().fire({icon:"success",title:"Leave Created!",text:"Admin leave created for ".concat(t.toLocaleDateString(),"."),timer:3e3,showConfirmButton:!1}),b({date:"",reason:"",type:"holiday"}),f(!1)}catch(e){console.error("Error creating leave:",e),d().fire({icon:"error",title:"Creation Failed",text:"Failed to create leave. Please try again."})}finally{y(!1)}},w=async e=>{try{if((await d().fire({title:"Delete Leave",text:"Are you sure you want to delete this leave?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Delete",cancelButtonText:"Cancel"})).isConfirmed){let{deleteAdminLeave:t}=await a.e(9567).then(a.bind(a,9567));await t(e),await v(),d().fire({icon:"success",title:"Leave Deleted",text:"Leave has been deleted successfully.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error deleting leave:",e),d().fire({icon:"error",title:"Delete Failed",text:"Failed to delete leave. Please try again."})}},j=e=>{switch(e){case"holiday":return"bg-green-100 text-green-800";case"maintenance":return"bg-blue-100 text-blue-800";case"emergency":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"holiday":return"fas fa-calendar-day text-green-500";case"maintenance":return"fas fa-tools text-blue-500";case"emergency":return"fas fa-exclamation-triangle text-red-500";default:return"fas fa-calendar text-gray-500"}};return t||m?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading leaves..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leave Management"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Total: ",c.length]}),(0,r.jsxs)("button",{onClick:()=>{if(0===c.length)return void d().fire({icon:"warning",title:"No Data",text:"No leaves to export."});let e=c.map(e=>({Date:e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),Reason:e.reason,Type:e.type.charAt(0).toUpperCase()+e.type.slice(1),"Created By":e.createdBy,"Created At":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():new Date(e.createdAt).toLocaleDateString()}));(0,l.Bf)(e,"admin-leaves"),d().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(c.length," leaves to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>f(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Leave"]}),(0,r.jsxs)("button",{onClick:v,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===c.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:"fas fa-calendar-times text-gray-300 text-6xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No leaves scheduled"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first admin leave to block work and withdrawals"}),(0,r.jsxs)("button",{onClick:()=>f(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Add First Leave"]})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reason"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created By"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.date.toLocaleDateString()}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.date.toLocaleDateString("en-US",{weekday:"long"})})]}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 max-w-xs",children:e.reason})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(j(e.type)),children:[(0,r.jsx)("i",{className:"".concat(N(e.type)," mr-1")}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.createdBy}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.createdAt.toLocaleDateString()})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("button",{onClick:()=>w(e.id),className:"text-red-600 hover:text-red-900",children:[(0,r.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete"]})})]},e.id))})]})})})}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Add Admin Leave"}),(0,r.jsx)("button",{onClick:()=>f(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,r.jsx)("input",{type:"date",value:p.date,onChange:e=>b(t=>({...t,date:e.target.value})),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsxs)("select",{value:p.type,onChange:e=>b(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"holiday",children:"Holiday"}),(0,r.jsx)("option",{value:"maintenance",children:"Maintenance"}),(0,r.jsx)("option",{value:"emergency",children:"Emergency"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,r.jsx)("textarea",{value:p.reason,onChange:e=>b(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,r.jsx)("button",{onClick:()=>f(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,r.jsx)("button",{onClick:D,disabled:h,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Leave"]})})]})]})})]})}},3737:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],s=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=n.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):r&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(s);i.setAttribute("href",e),i.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function n(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Advantage Days":e.quickVideoAdvantageDays||"","Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function s(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>{var t,a,r,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(r=e.bankDetails)?void 0:r.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>n,Pe:()=>o,dB:()=>i,sL:()=>s})},5410:(e,t,a)=>{Promise.resolve().then(a.bind(a,1577))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>l});var r=a(3915),n=a(3004),s=a(5317),i=a(858);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,n.xI)(o),c=(0,s.aU)(o);(0,i.c7)(o)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>l,hD:()=>o,wC:()=>c});var r=a(2115),n=a(3004),s=a(6104),i=a(12);function o(){let[e,t]=(0,r.useState)(null),[a,o]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,n.hg)(s.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let l=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:l}}function l(){let{user:e,loading:t}=o();return(0,r.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=o(),[a,n]=(0,r.useState)(!1),[s,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");n(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||s,isAdmin:a}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,8441,1684,7358],()=>t(5410)),_N_E=e.O()}]);