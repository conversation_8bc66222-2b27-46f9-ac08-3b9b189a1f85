(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4246,9567],{2040:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var n=a(5155),s=a(2115),r=a(6874),o=a.n(r),l=a(6681),i=a(7460),c=a(6572),d=a(3592),u=a(9567),g=a(3663),h=a(8647),m=a(4752),f=a.n(m);function p(){let{user:e,loading:t}=(0,l.Nu)(),{hasBlockingNotifications:a,isChecking:r,markAllAsRead:m}=(0,i.J)((null==e?void 0:e.uid)||null),{isBlocked:p,leaveStatus:x}=(0,c.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e}),[w,b]=(0,s.useState)(null),[y,v]=(0,s.useState)(0),[N,D]=(0,s.useState)(0),[k,j]=(0,s.useState)(0),[S,T]=(0,s.useState)(!1),[C,_]=(0,s.useState)(!1),[E,L]=(0,s.useState)([]),[P,A]=(0,s.useState)(!0),[I,M]=(0,s.useState)(""),[B,R]=(0,s.useState)([]),[O,U]=(0,s.useState)(!1),[H,F]=(0,s.useState)(!1),[W,G]=(0,s.useState)(""),[z,K]=(0,s.useState)(!1),[Q,X]=(0,s.useState)(!1),[Y,J]=(0,s.useState)(0),[q,V]=(0,s.useState)(!1),[Z,$]=(0,s.useState)({earningPerBatch:10,plan:"Trial"}),[ee,et]=(0,s.useState)(null),[ea,en]=(0,s.useState)(0),[es,er]=(0,s.useState)(0);(0,s.useEffect)(()=>{e&&eo()},[e]),(0,s.useEffect)(()=>{T(k>=50)},[k]);let eo=async()=>{try{console.log("\uD83D\uDD0D Checking work access for user:",e.uid);let t=await (0,d.isUserPlanExpired)(e.uid);if(console.log("\uD83D\uDCC5 Plan status result:",t),t.expired){console.log("\uD83D\uDEAB Work access blocked - Plan expired:",t.reason),f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">'.concat(t.reason,'</p>\n              <p class="text-sm text-gray-600">\n                Active Days: ').concat(t.activeDays||0," | Days Left: ").concat(t.daysLeft||0,"\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let a=await (0,d.getVideoCountData)(e.uid);if(console.log("\uD83D\uDCCA Translation data check:",a),a.todayTranslations>=50){console.log("\uD83D\uDEAB Work access blocked - Daily session completed"),f().fire({icon:"info",title:"Daily Session Completed",html:'\n            <div class="text-center">\n              <p class="mb-3">You have already completed your daily session of 50 translations!</p>\n              <p class="text-sm text-gray-600">\n                Translations completed today: '.concat(a.todayTranslations,'/50\n              </p>\n              <p class="text-sm text-green-600 mt-2">\n                Come back tomorrow for your next session.\n              </p>\n            </div>\n          '),confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"});return}let n=await (0,u.q8)(e.uid);if(console.log("\uD83D\uDCCA Work status result:",n),n.blocked){console.log("\uD83D\uDEAB Work access blocked:",n.reason),f().fire({icon:"warning",title:"Work Not Available",text:n.reason||"Work is currently blocked.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}console.log("✅ Work access allowed, proceeding with normal loading"),el(),ei(),ec(),eu(),ed()}catch(e){console.error("❌ Error checking work access (allowing work to proceed):",e),el(),ei(),ec(),eu(),ed()}},el=async()=>{try{console.log("\uD83D\uDCCA Loading translation data for user:",e.uid);let t=await (0,d.getVideoCountData)(e.uid);console.log("\uD83D\uDCCA Translation data loaded:",t),v(t.todayTranslations),D(t.totalTranslations)}catch(e){console.error("Error loading translation data:",e)}},ei=async()=>{try{let t=await (0,d.Q6)(e.uid);$({earningPerBatch:t.earningPerBatch,plan:t.plan}),F(t.hasQuickAdvantage||!1)}catch(e){console.error("Error loading translation settings:",e)}},ec=async()=>{try{let t=await (0,d.getUserData)(e.uid);if(et(t),t){try{await (0,d.iF)(e.uid)}catch(e){console.error("Error updating active days:",e)}let a=await (0,d.isUserPlanExpired)(e.uid);en(a.daysLeft||0),er(a.activeDays||0),console.log("\uD83D\uDCCA Plan status loaded:",{plan:t.plan,expired:a.expired,daysLeft:a.daysLeft,activeDays:a.activeDays,reason:a.reason})}}catch(e){console.error("Error loading user data:",e)}},ed=()=>{let t=new Date().toDateString(),a="translation_session_".concat(e.uid,"_").concat(t),n=localStorage.getItem(a);n&&j(parseInt(n))},eu=async()=>{try{A(!0);let e=(await (0,g.PO)()).map(e=>({english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian}));L(e),eg(e)}catch(e){console.error("Error loading translations:",e),f().fire({icon:"error",title:"Loading Error",text:"Failed to load translation data. Please refresh the page."})}finally{A(!1)}},eg=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:E;if(0===e.length)return;let t=Math.floor(Math.random()*e.length),a=e[t],n=(0,g.jQ)(),s=g.cb.find(e=>e.code===n);b({id:"step_".concat(Date.now(),"_").concat(Math.random()),englishText:a.english,targetLanguage:n,targetLanguageName:(null==s?void 0:s.name)||"Unknown",targetTranslation:a[n]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),M(""),R([]),U(!1),G(""),K(!1),X(!1),V(!1)},eh=(0,s.useCallback)(e=>{if(!w||O||q)return;let t=e.target.value,a=Date.now();if(em(t,a)&&(V(!0),f().fire({icon:"error",title:"Paste Detected!",text:H?"Copy-paste is allowed for your account.":"Copy-paste is not allowed. Please type the text manually.",timer:3e3}),!H)){e.target.value=I;return}let n=ew(t,w.englishText);R(n),M(t),t===w.englishText&&0===n.length&&(U(!0),f().fire({icon:"success",title:"Perfect!",text:"Text typed correctly. Now select the target language.",timer:2e3,showConfirmButton:!1})),J(a)},[w,O,q,H,I]),em=(e,t)=>{let a=t-Y,n=e.length-I.length;return n>3&&a<100?(console.log("\uD83D\uDEAB Paste detected: Multiple characters at once"),!0):n>1&&a<50?(console.log("\uD83D\uDEAB Paste detected: Unrealistic typing speed"),!0):n>10?(console.log("\uD83D\uDEAB Paste detected: Large text block"),!0):!!(e.length>20)&&e===(null==w?void 0:w.englishText.substring(0,e.length))&&!!(e.length/(t-(Y||t))>.1)&&(console.log("\uD83D\uDEAB Paste detected: Perfect match with high speed"),!0)},ef=(0,s.useCallback)(e=>{!H&&((e.ctrlKey||e.metaKey)&&"v"===e.key&&(e.preventDefault(),V(!0),f().fire({icon:"error",title:"Paste Not Allowed!",text:"Keyboard paste shortcuts are disabled. Please type manually.",timer:2e3})),e.repeat&&(console.log("\uD83D\uDEAB Long press detected"),"Backspace"!==e.key&&"Delete"!==e.key&&e.preventDefault()))},[H]),ep=(0,s.useCallback)(e=>{H||(e.preventDefault(),V(!0),f().fire({icon:"error",title:"Drag & Drop Not Allowed!",text:"Please type the text manually.",timer:2e3}))},[H]),ex=(0,s.useCallback)(e=>{H||(e.preventDefault(),f().fire({icon:"warning",title:"Context Menu Disabled",text:"Right-click menu is disabled to prevent paste operations.",timer:1500}))},[H]),ew=(e,t)=>{let a=[];for(let n=0;n<e.length;n++)(n>=t.length||e[n]!==t[n])&&a.push(n);return a},eb=e=>{w&&O&&(G(e),e===w.targetLanguage?(K(!0),f().fire({icon:"success",title:"Correct Language!",text:"You selected the correct language. Click Convert to see the translation.",timer:2e3,showConfirmButton:!1})):(K(!1),f().fire({icon:"error",title:"Wrong Language!",text:"Please select ".concat(w.targetLanguageName," language."),timer:2e3,showConfirmButton:!1})))},ey=async()=>{if(S&&!C&&!(k<50)){if(p)return void f().fire({icon:"warning",title:"Submission Not Available",text:x.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{_(!0);let t=Z.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"translation_earning",amount:t,description:"Batch completion reward - 50 translations completed"});let a=Math.min(y+50,50);v(a),D(N+50);let n=new Date().toDateString(),s="translation_session_".concat(e.uid,"_").concat(n);localStorage.removeItem(s),j(0),T(!1),f().fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:'\n          <div class="text-center">\n            <p class="text-lg font-bold text-green-600 mb-2">₹'.concat(t,' Earned!</p>\n            <p class="mb-2">50 translations completed and submitted</p>\n            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>\n            <p class="text-sm text-blue-600 font-semibold">\n              \uD83C\uDF89 Your daily session is complete! Come back tomorrow for your next session.\n            </p>\n          </div>\n        '),confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),f().fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{_(!1)}}};return t||P||r?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"spinner mb-4"}),(0,n.jsx)("p",{className:"text-white",children:t?"Loading...":r?"Checking notifications...":"Loading translations..."})]})}):a&&e?(0,n.jsx)(h.A,{userId:e.uid,onAllRead:m}):(0,n.jsxs)("div",{className:"min-h-screen p-4",children:[(0,n.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)(o(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,n.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,n.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,n.jsxs)("div",{className:"text-white text-right",children:[(0,n.jsxs)("p",{className:"text-sm",children:["Plan: ",Z.plan]}),(0,n.jsxs)("p",{className:"text-sm",children:["₹",Z.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:ea}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-blue-400",children:y}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Translations"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-green-400",children:N}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Total Translations"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-k)}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Translations Left"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[es,"/","Trial"===Z.plan?"2":"30"]}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,n.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,n.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,n.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to get new translation",children:[(0,n.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"New Translation"]})]}),w&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,n.jsx)("i",{className:"fas fa-keyboard mr-2"}),"Step 1: Type the English text below"]}),(0,n.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3",children:(0,n.jsx)("p",{className:"text-white text-lg font-mono",children:w.englishText})}),(0,n.jsx)("textarea",{value:I,onChange:eh,onKeyDown:ef,onDrop:ep,onContextMenu:ex,disabled:O||q,placeholder:H?"Type or paste the English text here...":"Type the English text here (copy-paste not allowed)...",className:"w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono ".concat(B.length>0?"border-red-500":""," ").concat(O?"border-green-500 bg-green-500/10":""),onPaste:e=>{H||(e.preventDefault(),V(!0),f().fire({icon:"error",title:"Paste Not Allowed!",text:"Please type the text manually.",timer:2e3}))},onDragOver:e=>{H||e.preventDefault()},spellCheck:!1,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off"}),B.length>0&&(0,n.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),"Typing errors detected. Please correct them to continue."]}),O&&(0,n.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Perfect! Text typed correctly."]}),q&&!H&&(0,n.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-ban mr-1"}),"Paste detected! Please type manually.",(0,n.jsx)("button",{onClick:()=>{V(!1),M("")},className:"ml-2 text-blue-400 underline",children:"Reset"})]})]}),O&&(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,n.jsx)("i",{className:"fas fa-globe mr-2"}),"Step 2: Select the target language - ",w.targetLanguageName]}),(0,n.jsxs)("select",{value:W,onChange:e=>eb(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:[(0,n.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select target language..."}),g.cb.map(e=>(0,n.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))]}),W&&!z&&(0,n.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-times-circle mr-1"}),"Wrong language! Please select ",w.targetLanguageName,"."]}),z&&(0,n.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Correct language selected!"]})]}),z&&(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("button",{onClick:()=>{w&&z&&(X(!0),b(e=>e?{...e,isConverted:!0}:null))},disabled:Q,className:"px-8 py-3 rounded-lg font-semibold transition-all duration-300 ".concat(Q?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"),children:[(0,n.jsx)("i",{className:"fas fa-exchange-alt mr-2"}),"Convert to ",w.targetLanguageName]})}),Q&&(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,n.jsx)("i",{className:"fas fa-language mr-2"}),w.targetLanguageName," Translation:"]}),(0,n.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-green-400",children:(0,n.jsx)("p",{className:"text-white text-lg",children:w.targetTranslation})}),(0,n.jsx)("div",{className:"text-center mt-4",children:(0,n.jsxs)("button",{onClick:()=>{if(!w||!Q)return;b(e=>e?{...e,isSubmitted:!0}:null);let t=k+1;j(t);let a=new Date().toDateString(),n="translation_session_".concat(e.uid,"_").concat(a);localStorage.setItem(n,t.toString()),t<50?f().fire({icon:"success",title:"Translation Submitted!",text:"Progress: ".concat(t,"/50 translations completed."),timer:2e3,showConfirmButton:!1}).then(()=>{eg()}):f().fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},className:"btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105",children:[(0,n.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Translation"]})})]}),S&&(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("button",{onClick:ey,disabled:C,className:"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105",children:[(0,n.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit All 50 Translations & Earn ₹",Z.earningPerBatch]})}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsxs)("p",{className:"text-white/80",children:["Progress: ",k,"/50 translations completed"]}),(0,n.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,n.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:"".concat(k/50*100,"%")}})})]})]})]})]})}},3663:(e,t,a)=>{"use strict";a.d(t,{Dt:()=>l,PO:()=>d,Qy:()=>i,bQ:()=>c,cb:()=>s,jQ:()=>u});let n={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},s=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}];function r(e){let t=function(e){try{let t=localStorage.getItem("".concat(n.BATCH_PREFIX).concat(e));if(!t)return null;let a=JSON.parse(t);if(Date.now()-a.lastUpdated>864e5)return localStorage.removeItem("".concat(n.BATCH_PREFIX).concat(e)),null;return a}catch(t){return console.error("Error loading translation batch ".concat(e,":"),t),null}}(e);return t?t.translations:[]}function o(){return r(parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0"))}function l(){let e=parseInt(localStorage.getItem(n.TOTAL_TRANSLATIONS)||"0"),t=parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0"),a=Math.ceil(e/100),s=r(t);return{totalTranslations:e,currentBatch:t,totalBatches:a,translationsInCurrentBatch:s.length}}function i(){Object.keys(localStorage).forEach(e=>{(e.startsWith(n.BATCH_PREFIX)||Object.values(n).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function c(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error("Failed to load translations: ".concat(e.statusText));let t=await e.json();console.log("Raw translation data loaded:",t.length,"entries");let a=[];return Array.isArray(t)&&t.forEach((e,t)=>{e.english&&a.push({id:"translation_".concat(t,"_").concat(Date.now()),english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian,category:"General",batchIndex:Math.floor(a.length/100)})}),a}catch(e){throw console.error("Error loading translations from file:",e),e}}async function d(){try{if(!function(){let e=localStorage.getItem(n.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached translation data..."),o();{console.log("Loading fresh translation data...");let e=await c();return!function(e){let t=Math.ceil(e.length/100);for(let s=0;s<t;s++){let t=100*s,r=Math.min(t+100,e.length),o=e.slice(t,r);var a=s;try{let e={batchNumber:a,translations:o,totalTranslations:o.length,lastUpdated:Date.now()};localStorage.setItem("".concat(n.BATCH_PREFIX).concat(a),JSON.stringify(e))}catch(e){console.error("Error saving translation batch ".concat(a,":"),e)}}localStorage.setItem(n.TOTAL_TRANSLATIONS,e.length.toString()),localStorage.setItem(n.CURRENT_BATCH,"0"),localStorage.setItem(n.LAST_PROCESSED,Date.now().toString()),console.log("Saved ".concat(e.length," translations in ").concat(t," batches"))}(e),o()}}catch(t){console.error("Error initializing translation system:",t);let e=o();if(e.length>0)return console.log("Using cached translations as fallback"),e;throw t}}function u(){let e=Math.floor(Math.random()*s.length);return s[e].code}},6572:(e,t,a)=>{"use strict";a.d(t,{l:()=>r});var n=a(2115),s=a(9567);function r(e){let{userId:t,checkInterval:a=3e4,enabled:r=!0}=e,[o,l]=(0,n.useState)({blocked:!1,lastChecked:new Date}),[i,c]=(0,n.useState)(!1),d=(0,n.useCallback)(async()=>{if(t&&r)try{c(!0);let e=await (0,s.q8)(t);return l({blocked:e.blocked,reason:e.reason,lastChecked:new Date}),e}catch(e){return console.error("Error checking leave status:",e),l(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{c(!1)}},[t,r]);return(0,n.useEffect)(()=>{t&&r&&d()},[t,r,d]),(0,n.useEffect)(()=>{if(!t||!r||a<=0)return;let e=setInterval(()=>{d()},a);return()=>clearInterval(e)},[t,r,a,d]),{leaveStatus:o,isChecking:i,checkLeaveStatus:d,isBlocked:o.blocked}}},8763:(e,t,a)=>{Promise.resolve().then(a.bind(a,2040))},9567:(e,t,a)=>{"use strict";a.d(t,{applyUserLeave:()=>d,calculateActiveDays:()=>f,cancelUserLeave:()=>g,createAdminLeave:()=>o,deleteAdminLeave:()=>i,getAdminLeaves:()=>l,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>h,isAdminLeaveDay:()=>c,isUserOnLeave:()=>m,q8:()=>p});var n=a(6104),s=a(5317);let r={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function o(e){try{return(await (0,s.gS)((0,s.collection)(n.db,r.adminLeaves),{...e,date:s.Dc.fromDate(e.date),createdAt:s.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function l(){try{let e=(0,s.P)((0,s.collection)(n.db,r.adminLeaves),(0,s.My)("date","asc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function i(e){try{await (0,s.kd)((0,s.H9)(n.db,r.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function c(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let o=(0,s.P)((0,s.collection)(n.db,r.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),l=await (0,s.getDocs)(o),i=!l.empty;return i?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),i}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,a,o,l=new Date,i=l.getFullYear(),c=l.getMonth()+1,d=await h(e.userId,i,c),u="pending";return d<4&&(u="approved",t="system",o=s.Dc.now(),a="Auto-approved: ".concat(d+1,"/").concat(4," monthly leaves used")),{id:(await (0,s.gS)((0,s.collection)(n.db,r.userLeaves),{...e,date:s.Dc.fromDate(e.date),status:u,appliedAt:s.Dc.now(),...t&&{reviewedBy:t},...o&&{reviewedAt:o},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,s.P)((0,s.collection)(n.db,r.userLeaves),(0,s._M)("userId","==",e),(0,s.My)("date","desc"));return(await (0,s.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(t=e.data().reviewedAt)?void 0:t.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function g(e){try{await (0,s.kd)((0,s.H9)(n.db,r.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function h(e,t,a){try{let o=new Date(t,a-1,1),l=new Date(t,a,0,23,59,59,999),i=(0,s.P)((0,s.collection)(n.db,r.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(o)),(0,s._M)("date","<=",s.Dc.fromDate(l)));return(await (0,s.getDocs)(i)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function m(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let o=new Date(t);o.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",o.toISOString());let l=(0,s.P)((0,s.collection)(n.db,r.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(a)),(0,s._M)("date","<=",s.Dc.fromDate(o))),i=await (0,s.getDocs)(l),c=!i.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function f(e,t){let a=new Date,o=Math.floor((a.getTime()-t.getTime())/864e5);try{let l=(0,s.P)((0,s.collection)(n.db,r.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),i=(await (0,s.getDocs)(l)).size,c=(0,s.P)((0,s.collection)(n.db,r.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),d=(await (0,s.getDocs)(c)).size;return Math.max(0,o-i-d)}catch(e){return console.error("Error calculating active days:",e),Math.max(0,o)}}async function p(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await c(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await m(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,1018,8441,1684,7358],()=>t(8763)),_N_E=e.O()}]);