(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8171],{2385:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>l});var s=r(5155),c=r(2115);function l(){let[e,a]=(0,c.useState)(""),[r,l]=(0,c.useState)(!1),i=e=>{a(a=>a+e+"\n"),console.log(e)},t=async()=>{a(""),l(!0);try{if(i("\uD83E\uDDF9 Starting cache clearing process..."),"caches"in window){let e=await caches.keys();for(let a of(i("\uD83D\uDCE6 Found ".concat(e.length," caches: ").concat(e.join(", "))),e))await caches.delete(a),i("✅ Deleted cache: ".concat(a))}else i("❌ Cache API not supported");if("serviceWorker"in navigator){let e=await navigator.serviceWorker.getRegistrations();for(let a of(i("\uD83D\uDD27 Found ".concat(e.length," service worker registrations")),e))await a.unregister(),i("✅ Unregistered service worker: ".concat(a.scope))}else i("❌ Service Worker API not supported");localStorage.clear(),i("✅ Cleared localStorage"),sessionStorage.clear(),i("✅ Cleared sessionStorage"),"indexedDB"in window&&i("✅ IndexedDB available (manual clearing may be needed)"),i("\n\uD83C\uDF89 Cache clearing completed!"),i("\uD83D\uDCA1 Refresh the page to see changes")}catch(e){i("❌ Cache clearing failed: ".concat(e.message)),console.error("Cache clearing error:",e)}finally{l(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Clear Cache & Service Worker"}),(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,s.jsx)("button",{onClick:t,disabled:r,className:"btn-primary w-full",children:r?"Clearing Cache...":"Clear All Cache & Service Workers"}),(0,s.jsx)("button",{onClick:()=>{i("\uD83D\uDD04 Performing hard refresh..."),window.location.reload()},className:"btn-secondary w-full",children:"Hard Refresh Page"})]}),(0,s.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Click the button to clear cache and service workers..."}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-yellow-500/20 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,s.jsxs)("ol",{className:"text-white/80 text-sm space-y-1",children:[(0,s.jsx)("li",{children:'1. Click "Clear All Cache & Service Workers"'}),(0,s.jsx)("li",{children:"2. Wait for completion"}),(0,s.jsx)("li",{children:'3. Click "Hard Refresh Page" or press Ctrl+Shift+R'}),(0,s.jsx)("li",{children:"4. Try registration again"})]})]}),(0,s.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,s.jsx)("a",{href:"/register",className:"btn-primary inline-block",children:"Go to Registration"}),(0,s.jsx)("a",{href:"/debug-registration-simple",className:"btn-secondary inline-block ml-4",children:"Go to Debug Registration"})]})]})})})}},3150:(e,a,r)=>{Promise.resolve().then(r.bind(r,2385))}},e=>{var a=a=>e(e.s=a);e.O(0,[8441,1684,7358],()=>a(3150)),_N_E=e.O()}]);