"use strict";exports.id=1391,exports.ids=[1391],exports.modules={91391:(t,e,a)=>{a.d(e,{CF:()=>c,Pn:()=>d,TK:()=>D,getWithdrawals:()=>w,hG:()=>u,lo:()=>i,nQ:()=>E,updateWithdrawalStatus:()=>L,x5:()=>l});var r=a(75535),o=a(33784),s=a(3582);let n=new Map;async function d(){let t="dashboard-stats",e=function(t){let e=n.get(t);return e&&Date.now()-e.timestamp<3e5?e.data:null}(t);if(e)return e;try{let e=new Date;e.setHours(0,0,0,0);let a=r.Dc.fromDate(e),d=await (0,r.getDocs)((0,r.collection)(o.db,s.COLLECTIONS.users)),i=d.size,l=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r._M)(s.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,r.getDocs)(l)).size,E=0,w=0,D=0,u=0;d.forEach(t=>{let a=t.data();E+=a[s.FIELD_NAMES.totalTranslations]||0,w+=a[s.FIELD_NAMES.wallet]||0;let r=a[s.FIELD_NAMES.lastTranslationDate]?.toDate();r&&r.toDateString()===e.toDateString()&&(D+=a[s.FIELD_NAMES.todayTranslations]||0)});try{let t=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.transactions),(0,r._M)(s.FIELD_NAMES.type,"==","translation_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(t)).forEach(t=>{let a=t.data(),r=a[s.FIELD_NAMES.date]?.toDate();r&&r>=e&&(u+=a[s.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let L=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),h=(await (0,r.getDocs)(L)).size,S=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),p=(await (0,r.getDocs)(S)).size,C={totalUsers:i,totalTranslations:E,totalEarnings:w,pendingWithdrawals:h,todayUsers:c,todayTranslations:D,todayEarnings:u,todayWithdrawals:p};return n.set(t,{data:C,timestamp:Date.now()}),C}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(t=50,e=null){try{let a=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(t));e&&(a=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(e),(0,r.AB)(t)));let n=await (0,r.getDocs)(a);return{users:n.docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let e=t.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})).filter(t=>{let a=String(t[s.FIELD_NAMES.name]||"").toLowerCase(),r=String(t[s.FIELD_NAMES.email]||"").toLowerCase(),o=String(t[s.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(t[s.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(e)||r.includes(e)||o.includes(e)||n.includes(e)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()}))}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users));return(await (0,r.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(t=50,e=null){try{let a=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));e&&(a=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(e),(0,r.AB)(t)));let n=await (0,r.getDocs)(a);return{withdrawals:n.docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function D(t,e){try{await (0,r.mZ)((0,r.H9)(o.db,s.COLLECTIONS.users,t),e),n.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function u(t){try{await (0,r.kd)((0,r.H9)(o.db,s.COLLECTIONS.users,t)),n.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function L(t,e,d){try{let i=await (0,r.x7)((0,r.H9)(o.db,s.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:E}=i.data(),w={status:e,updatedAt:r.Dc.now()};if(d&&(w.adminNotes=d),await (0,r.mZ)((0,r.H9)(o.db,s.COLLECTIONS.withdrawals,t),w),"approved"===e&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(a.bind(a,3582));await t(l,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===e&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:e}=await Promise.resolve().then(a.bind(a,3582));await t(l,c),await e(l,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}n.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};