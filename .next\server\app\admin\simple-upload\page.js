(()=>{var e={};e.id=773,e.ids=[773,1391],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16071:(e,t,s)=>{Promise.resolve().then(s.bind(s,93429))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32943:(e,t,s)=>{Promise.resolve().then(s.bind(s,73495))},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>d,j2:()=>n});var r=s(67989),a=s(63385),i=s(75535),l=s(70146);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),n=(0,a.xI)(o),d=(0,i.aU)(o);(0,l.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,s)=>{"use strict";s.d(t,{M4:()=>o,_f:()=>l});var r=s(33784),a=s(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,t="/login"){try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73495:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\simple-upload\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\simple-upload\\page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74452:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let d={children:["",{children:["admin",{children:["simple-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73495)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\simple-upload\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\simple-upload\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/simple-upload/page",pathname:"/admin/simple-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>l,hD:()=>i,wC:()=>o});var r=s(43210);s(63385),s(33784);var a=s(51278);function i(){let[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)(!0),l=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:l}}function l(){let{user:e,loading:t}=i();return{user:e,loading:t}}function o(){let{user:e,loading:t}=i(),[s,a]=(0,r.useState)(!1),[l,o]=(0,r.useState)(!0);return{user:e,loading:t||l,isAdmin:s}}},91391:(e,t,s)=>{"use strict";s.d(t,{CF:()=>c,Pn:()=>o,TK:()=>m,getWithdrawals:()=>p,hG:()=>h,lo:()=>n,nQ:()=>u,updateWithdrawalStatus:()=>x,x5:()=>d});var r=s(75535),a=s(33784),i=s(3582);let l=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=l.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=r.Dc.fromDate(t),o=await (0,r.getDocs)((0,r.collection)(a.db,i.COLLECTIONS.users)),n=o.size,d=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r._M)(i.FIELD_NAMES.joinedDate,">=",s)),c=(await (0,r.getDocs)(d)).size,u=0,p=0,m=0,h=0;o.forEach(e=>{let s=e.data();u+=s[i.FIELD_NAMES.totalVideos]||0,p+=s[i.FIELD_NAMES.wallet]||0;let r=s[i.FIELD_NAMES.lastVideoDate]?.toDate();r&&r.toDateString()===t.toDateString()&&(m+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.transactions),(0,r._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{let s=e.data(),r=s[i.FIELD_NAMES.date]?.toDate();r&&r>=t&&(h+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let x=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),w=(await (0,r.getDocs)(x)).size,f=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r._M)("date",">=",s)),g=(await (0,r.getDocs)(f)).size,v={totalUsers:n,totalVideos:u,totalEarnings:p,pendingWithdrawals:w,todayUsers:c,todayVideos:m,todayEarnings:h,todayWithdrawals:g};return l.set(e,{data:v,timestamp:Date.now()}),v}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(e=50,t=null){try{let s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(e));t&&(s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let l=await (0,r.getDocs)(s);return{users:l.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(s)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let s=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),l=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||r.includes(t)||a.includes(t)||l.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(s=(0,r.P)((0,r.collection)(a.db,i.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let l=await (0,r.getDocs)(s);return{withdrawals:l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function m(e,t){try{await (0,r.mZ)((0,r.H9)(a.db,i.COLLECTIONS.users,e),t),l.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function h(e){try{await (0,r.kd)((0,r.H9)(a.db,i.COLLECTIONS.users,e)),l.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function x(e,t,o){try{let n=await (0,r.x7)((0,r.H9)(a.db,i.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=n.data(),p={status:t,updatedAt:r.Dc.now()};if(o&&(p.adminNotes=o),await (0,r.mZ)((0,r.H9)(a.db,i.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}l.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},93429:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687),a=s(43210),i=s(85814),l=s.n(i),o=s(87979),n=s(91391),d=s(77567);function c(){let{user:e,loading:t,isAdmin:i}=(0,o.wC)(),[c,u]=(0,a.useState)(!1),[p,m]=(0,a.useState)(null),[h,x]=(0,a.useState)(null),[w,f]=(0,a.useState)([]),[g,v]=(0,a.useState)(!1),b=async()=>{if(h)try{u(!0);let e=(await h.text()).split("\n").filter(e=>e.trim());if(e.length<2)throw Error("CSV file must have at least a header row and one data row");let t=e[0],s=t.includes("	")?"	":",",r=t.split(s).map(e=>e.trim().replace(/"/g,"").toLowerCase());if(["email","totalvideos","walletbalance","activedays"].filter(e=>!r.some(t=>t.includes(e.replace("totalvideos","videos").replace("walletbalance","wallet").replace("activedays","active")))).length>0)throw Error("Missing required columns. Expected: email, totalVideos (or videos), walletBalance (or wallet), activeDays (or active)");let a=e.slice(1).map((e,t)=>{let a=e.split(s).map(e=>e.trim().replace(/"/g,"")),i={};r.forEach((e,t)=>{i[e]=a[t]||""});let l=i.email||"",o=parseInt(i.totalvideos||i.videos||i.totalVideos||"0")||0,n=parseFloat(i.walletbalance||i.wallet||i.walletBalance||"0")||0,d=parseInt(i.activedays||i.active||i.activeDays||"0")||0;if(!l)throw Error(`Row ${t+2}: Email is required`);if(!l.includes("@"))throw Error(`Row ${t+2}: Invalid email format`);return{email:l,totalVideos:o,walletBalance:n,activeDays:d}});f(a.slice(0,10)),v(!0)}catch(e){console.error("Error previewing file:",e),d.A.fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{u(!1)}},y=async()=>{if(h&&(await d.A.fire({icon:"question",title:"Confirm Data Upload",html:`
        <div class="text-left">
          <p><strong>Are you sure you want to update user data from this file?</strong></p>
          <br>
          <p>This will:</p>
          <ul>
            <li>Find users by email address</li>
            <li>Add to their existing total videos count</li>
            <li>Add to their existing wallet balance</li>
            <li>SET their active days to the specified value</li>
            <li>Skip users not found in the system</li>
          </ul>
          <br>
          <p class="text-yellow-600"><strong>Note:</strong> Videos and wallet will be ADDED, but active days will be SET (replaced)!</p>
        </div>
      `,showCancelButton:!0,confirmButtonText:"Yes, Update Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{u(!0),m(null),d.A.fire({title:"Updating Users",html:`
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p>Processing user updates...</p>
            <p class="text-sm text-gray-600 mt-2">Please wait...</p>
          </div>
        `,allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});let e=(await h.text()).split("\n").filter(e=>e.trim()),t=e[0],r=t.includes("	")?"	":",",a=t.split(r).map(e=>e.trim().replace(/"/g,"").toLowerCase()),i=e.slice(1).map(e=>{let t=e.split(r).map(e=>e.trim().replace(/"/g,"")),s={};return a.forEach((e,r)=>{s[e]=t[r]||""}),{email:s.email||"",totalVideos:parseInt(s.totalvideos||s.videos||s.totalVideos||"0")||0,walletBalance:parseFloat(s.walletbalance||s.wallet||s.walletBalance||"0")||0,activeDays:parseInt(s.activedays||s.active||s.activeDays||"0")||0}}).filter(e=>e.email),l=0,o=0,c=0,p=[];for(let e of i)try{let t=(await (0,n.x5)(e.email)).find(t=>t.email?.toLowerCase()===e.email.toLowerCase());if(!t){c++,p.push(`User not found: ${e.email}`);continue}let{getWalletData:r,getVideoCountData:a}=await Promise.resolve().then(s.bind(s,3582)),[i,o]=await Promise.all([r(t.id),a(t.id)]),d={totalVideos:(o.totalVideos||0)+e.totalVideos,wallet:(i.wallet||0)+e.walletBalance,activeDays:e.activeDays};await (0,n.TK)(t.id,d),l++}catch(t){o++,p.push(`Failed to update ${e.email}: ${t.message}`)}d.A.close();let x={success:l,failed:o,errors:p,notFound:c};m(x),d.A.fire({icon:l>0?o>0||c>0?"warning":"success":"error",title:"Update Complete",html:`
          <div class="text-left">
            <p><strong>Update Summary:</strong></p>
            <ul>
              <li class="text-green-600">✓ Successfully updated: ${l} users</li>
              <li class="text-yellow-600">⚠ Not found: ${c} users</li>
              <li class="text-red-600">✗ Failed: ${o} users</li>
            </ul>
            ${p.length>0?`<br><p><strong>First 5 errors:</strong></p><ul>${p.slice(0,5).map(e=>`<li class="text-red-600 text-sm">${e}</li>`).join("")}</ul>`:""}
          </div>
        `,timer:o>0?void 0:5e3,showConfirmButton:o>0})}catch(e){console.error("Error updating users:",e),d.A.fire({icon:"error",title:"Update Failed",text:e.message||"Failed to update users. Please try again."})}finally{u(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Simple User Update"}),(0,r.jsx)("p",{className:"text-white/80",children:"Update user videos, wallet balance, and active days via CSV"})]}),(0,r.jsxs)(l(),{href:"/admin/users",className:"btn-secondary",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload CSV File"]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample File"}),(0,r.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalVideos,walletBalance,activeDays\<EMAIL>,100,500,15\<EMAIL>,250,1200,25\<EMAIL>,75,300,5"],{type:"text/csv"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="simple-upload-sample.csv",s.click(),URL.revokeObjectURL(t)},className:"btn-secondary text-sm",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select CSV File"}),(0,r.jsx)("input",{type:"file",accept:".csv,.txt",onChange:e=>{let t=e.target.files?.[0];t&&(x(t),f([]),v(!1),m(null))},className:"form-input"})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:b,disabled:!h||c,className:"btn-secondary",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,r.jsx)("button",{onClick:y,disabled:!h||c||!g,className:"btn-primary",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Updating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Update Users"]})})]})})]}),g&&w.length>0&&(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 10 Records)"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full text-white",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-white/20",children:[(0,r.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,r.jsx)("th",{className:"text-left p-2",children:"Add Videos"}),(0,r.jsx)("th",{className:"text-left p-2",children:"Add Wallet (₹)"}),(0,r.jsx)("th",{className:"text-left p-2",children:"Set Active Days"})]})}),(0,r.jsx)("tbody",{children:w.map((e,t)=>(0,r.jsxs)("tr",{className:"border-b border-white/10",children:[(0,r.jsx)("td",{className:"p-2",children:e.email}),(0,r.jsxs)("td",{className:"p-2",children:["+",e.totalVideos]}),(0,r.jsxs)("td",{className:"p-2",children:["+₹",e.walletBalance]}),(0,r.jsx)("td",{className:"p-2",children:e.activeDays})]},t))})]})})]}),p&&(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Update Results"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:p.success}),(0,r.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Updated"})]}),(0,r.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:p.notFound}),(0,r.jsx)("div",{className:"text-yellow-300 text-sm",children:"Users Not Found"})]}),(0,r.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:p.failed}),(0,r.jsx)("div",{className:"text-red-300 text-sm",children:"Failed Updates"})]})]}),p.errors.length>0&&(0,r.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,r.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[p.errors.slice(0,10).map((e,t)=>(0,r.jsxs)("li",{children:["• ",e]},t)),p.errors.length>10&&(0,r.jsxs)("li",{className:"text-red-400",children:["... and ",p.errors.length-10," more errors"]})]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Instructions"]}),(0,r.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-white mb-2",children:"CSV Format:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"email:"})," User's email address (must exist in system)"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"totalVideos:"})," Number of videos to ADD to current count"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"walletBalance:"})," Amount to ADD to current wallet balance"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"activeDays:"})," Active days to SET (replace current value)"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,r.jsx)("li",{children:"Videos and wallet values are ADDED to existing data"}),(0,r.jsx)("li",{children:"Active days value REPLACES the current active days"}),(0,r.jsx)("li",{children:"Users must already exist in the system"}),(0,r.jsx)("li",{children:"Email addresses are case-insensitive"}),(0,r.jsx)("li",{children:"Use comma or tab as delimiter"}),(0,r.jsx)("li",{children:"First row must be headers: email,totalVideos,walletBalance,activeDays"})]})]})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,2756,7567,5901,3582],()=>s(74452));module.exports=r})();