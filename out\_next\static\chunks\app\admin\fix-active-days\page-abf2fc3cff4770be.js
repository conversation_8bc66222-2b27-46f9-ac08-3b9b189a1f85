(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1561],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>c,_f:()=>o});var r=s(6104),i=s(4752),a=s.n(i);function n(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await a().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await r.j2.signOut(),a().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&n(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},2817:(e,t,s)=>{Promise.resolve().then(s.bind(s,6311))},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j2:()=>c});var r=s(3915),i=s(3004),a=s(5317),n=s(858);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,i.xI)(o),l=(0,a.aU)(o);(0,n.c7)(o)},6311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(5155),i=s(2115),a=s(6681),n=s(3592),o=s(4752),c=s.n(o);function l(){let{user:e,loading:t}=(0,a.Nu)(),[o,l]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1),[h,m]=(0,i.useState)(null),f=(null==e?void 0:e.email)==="<EMAIL>",x=async()=>{if(!f)return void c().fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await c().fire({icon:"warning",title:"Fix All Users Active Days",text:"This will recalculate and update active days for all users. This may take a while. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Fix All",cancelButtonText:"Cancel"})).isConfirmed)try{l(!0);let e=await (0,n.gj)();m(e),c().fire({icon:"success",title:"Active Days Fixed!",html:'\n          <div class="text-left">\n            <p><strong>Fixed:</strong> '.concat(e.fixedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount," users</p>\n          </div>\n        "),timer:5e3})}catch(e){console.error("Error fixing active days:",e),c().fire({icon:"error",title:"Error",text:"Failed to fix active days. Check console for details."})}finally{l(!1)}},g=async()=>{if(!f)return void c().fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await c().fire({icon:"warning",title:"Reset All Daily Video Counts",text:"This will reset today's video count to 0 for all users. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Reset All",cancelButtonText:"Cancel"})).isConfirmed)try{u(!0);let{getDocs:e,collection:t}=await Promise.resolve().then(s.bind(s,5317)),{db:r}=await Promise.resolve().then(s.bind(s,6104)),{COLLECTIONS:i}=await Promise.resolve().then(s.bind(s,3592)),a=await e(t(r,i.users)),o=0,l=0;for(let e of a.docs)try{await (0,n.HY)(e.id),o++}catch(t){console.error("Error resetting daily count for user ".concat(e.id,":"),t),l++}c().fire({icon:"success",title:"Daily Counts Reset!",html:'\n          <div class="text-left">\n            <p><strong>Reset:</strong> '.concat(o," users</p>\n            <p><strong>Errors:</strong> ").concat(l," users</p>\n          </div>\n        "),timer:5e3})}catch(e){console.error("Error resetting daily counts:",e),c().fire({icon:"error",title:"Error",text:"Failed to reset daily counts. Check console for details."})}finally{u(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):f?(0,r.jsx)("div",{className:"min-h-screen p-4",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-white mb-6",children:[(0,r.jsx)("i",{className:"fas fa-tools mr-2"}),"Fix Active Days & Daily Counts"]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,r.jsx)("i",{className:"fas fa-calendar-check mr-2"}),"Fix Active Days"]}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"Recalculates and updates active days for all users based on their plan activation date and leave history."}),(0,r.jsx)("button",{onClick:x,disabled:o,className:"btn-primary ".concat(o?"opacity-50 cursor-not-allowed":""),children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Fixing Active Days..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-wrench mr-2"}),"Fix All Users Active Days"]})})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,r.jsx)("i",{className:"fas fa-redo mr-2"}),"Reset Daily Video Counts"]}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"Resets today's video count to 0 for all users. Use this if daily counts are showing incorrect values."}),(0,r.jsx)("button",{onClick:g,disabled:d,className:"btn-secondary ".concat(d?"opacity-50 cursor-not-allowed":""),children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Resetting Daily Counts..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Reset All Daily Counts"]})})]}),h&&(0,r.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-green-300 mb-2",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Last Operation Results"]}),(0,r.jsxs)("div",{className:"text-white",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Fixed:"})," ",h.fixedCount," users"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Errors:"})," ",h.errorCount," users"]})]})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("a",{href:"/admin",className:"btn-secondary",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin Dashboard"]})})]})]})})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-white mb-4",children:"Only admin can access this page."}),(0,r.jsx)("a",{href:"/admin",className:"btn-primary",children:"Back to Admin"})]})})}},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>o,wC:()=>l});var r=s(2115),i=s(3004),a=s(6104),n=s(12);function o(){let[e,t]=(0,r.useState)(null),[s,o]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,i.hg)(a.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let c=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=o();return(0,r.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=o(),[s,i]=(0,r.useState)(!1),[a,n]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");i(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||a,isAdmin:s}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,3592,8441,1684,7358],()=>t(2817)),_N_E=e.O()}]);