'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { signInWithEmailAndPassword } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { useAuthState } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

export default function LoginPage() {
  const { user, loading } = useAuthState()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  useEffect(() => {
    if (user && !loading) {
      window.location.href = '/dashboard'
    }
  }, [user, loading])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please fill in all fields',
        background: 'rgba(255, 255, 255, 0.95)',
        backdrop: 'rgba(0, 0, 0, 0.8)',
      })
      return
    }

    setIsLoading(true)

    try {
      await signInWithEmailAndPassword(auth, email, password)
      // Redirect will be handled by useEffect
    } catch (error: any) {
      console.error('Login error:', error)
      
      let message = 'An error occurred during login'
      
      switch (error.code) {
        case 'auth/user-not-found':
          message = 'No account found with this email address'
          break
        case 'auth/wrong-password':
          message = 'Incorrect password'
          break
        case 'auth/invalid-email':
          message = 'Invalid email address'
          break
        case 'auth/user-disabled':
          message = 'This account has been disabled'
          break
        case 'auth/too-many-requests':
          message = 'Too many failed attempts. Please try again later'
          break
        default:
          message = error.message || 'Login failed'
      }

      Swal.fire({
        icon: 'error',
        title: 'Login Failed',
        text: message,
        background: 'rgba(255, 255, 255, 0.95)',
        backdrop: 'rgba(0, 0, 0, 0.8)',
      })
      
      setPassword('')
    } finally {
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="glass-card w-full max-w-md p-8">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/img/instra-logo.svg"
              alt="Instra Global Logo"
              width={50}
              height={50}
              className="mr-3"
            />
            <span className="text-2xl font-bold text-white">Instra Global</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Welcome Back</h1>
          <p className="text-white/80">Sign in to continue earning</p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-white font-medium mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="form-input"
              placeholder="Enter your email"
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-white font-medium mb-2">
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="form-input pr-12"
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="password-toggle-btn"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full btn-primary flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Logging in...
              </>
            ) : (
              <>
                <i className="fas fa-sign-in-alt mr-2"></i>
                Login
              </>
            )}
          </button>
        </form>

        {/* Links */}
        <div className="mt-6 text-center space-y-3">
          <Link
            href="/forgot-password"
            className="text-white/80 hover:text-white transition-colors"
          >
            Forgot your password?
          </Link>
          
          <div className="text-white/60">
            Don't have an account?{' '}
            <Link
              href="/register"
              className="text-white font-semibold hover:underline"
            >
              Sign up here
            </Link>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="text-white/80 hover:text-white transition-colors inline-flex items-center"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
