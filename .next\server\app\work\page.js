(()=>{var e={};e.id=4246,e.ids=[4246,7087],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19645:(e,t,r)=>{Promise.resolve().then(r.bind(r,30766))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40268:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),a=r(43210),o=r(85814),n=r.n(o),i=r(87979),l=r(744),c=r(55986),d=r(3582);r(87087);let u={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_"};var p=r(98873),h=r(77567);function m(){let{user:e,loading:t}=(0,i.Nu)(),{hasBlockingNotifications:r,isChecking:o,markAllAsRead:u}=(0,l.J)(e?.uid||null),{isBlocked:m,leaveStatus:x}=(0,c.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e}),[g,f]=(0,a.useState)(null),[v,b]=(0,a.useState)(0),[w,y]=(0,a.useState)(0),[D,k]=(0,a.useState)(50),[N,j]=(0,a.useState)(""),[S,_]=(0,a.useState)("hindi"),[C,q]=(0,a.useState)(0),[A,P]=(0,a.useState)(!1),[M,E]=(0,a.useState)(!1),[T,L]=(0,a.useState)([]),[I,U]=(0,a.useState)(!0),[O]=(0,a.useState)([{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}]),[B,R]=(0,a.useState)({earningPerBatch:10,plan:"Trial"}),[Y,$]=(0,a.useState)(null),[J,H]=(0,a.useState)(0),[z,G]=(0,a.useState)(0),F=async()=>{if(A&&!M&&!(C<50)){if(m)return void h.A.fire({icon:"warning",title:"Submission Not Available",text:x.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{E(!0);let t=B.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"translation_earning",amount:t,description:"Batch completion reward - 50 translations completed"});let r=Math.min(v+50,50);b(r),y(w+50),k(0);let s=new Date().toDateString(),a=`translation_session_${e.uid}_${s}`;localStorage.removeItem(a),q(0),P(!1),j(""),h.A.fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:`
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${t} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),h.A.fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{E(!1)}}};return t||I||o?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":o?"Checking notifications...":"Loading translations..."})]})}):r&&e?(0,s.jsx)(p.A,{userId:e.uid,onAllRead:u}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,s.jsxs)("div",{className:"text-white text-right",children:[(0,s.jsxs)("p",{className:"text-sm",children:["Plan: ",B.plan]}),(0,s.jsxs)("p",{className:"text-sm",children:["₹",B.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:J}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-blue-400",children:v}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Translations"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-green-400",children:w}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Total Translations"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-purple-400",children:D}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Translations Left"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[z,"/","Trial"===B.plan?"2":"30"]}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to get new translation",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"New Translation"]})]}),g&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-flag mr-2"}),"English Text:"]}),(0,s.jsx)("p",{className:"text-white text-lg bg-white/5 p-3 rounded border-l-4 border-blue-400",children:g.english})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-globe mr-2"}),"Translate to:"]}),(0,s.jsx)("select",{value:S,onChange:e=>_(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:O.map(e=>(0,s.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-edit mr-2"}),"Your Translation:"]}),(0,s.jsx)("textarea",{value:N,onChange:e=>j(e.target.value),placeholder:`Enter your ${O.find(e=>e.code===S)?.name} translation here...`,className:"w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none"})]}),(0,s.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,s.jsxs)("button",{onClick:()=>{if(!N.trim()||C>=50)return;if(m)return void h.A.fire({icon:"warning",title:"Work Suspended",text:x.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});let t=C+1;q(t),k(Math.max(0,50-t));let r=new Date().toDateString(),s=`translation_session_${e.uid}_${r}`;if(localStorage.setItem(s,t.toString()),T.length>0){let e=Math.floor(Math.random()*T.length);f(T[e]);let t=Math.floor(Math.random()*O.length);_(O[t].code)}j(""),t<50?h.A.fire({icon:"success",title:"Translation Completed!",text:`Progress: ${t}/50 translations completed. ${50-t} more to go!`,timer:2e3,showConfirmButton:!1}):h.A.fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:!N.trim()||C>=50,className:`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${!N.trim()||C>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"}`,children:[(0,s.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Translation"]}),A&&(0,s.jsxs)("button",{onClick:F,disabled:M,className:"btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit & Earn ₹",B.earningPerBatch]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-white/80",children:["Progress: ",C,"/50 translations completed"]}),(0,s.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:`${C/50*100}%`}})})]})]})]})]})}},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},48060:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54557:(e,t,r)=>{Promise.resolve().then(r.bind(r,40268))},55511:e=>{"use strict";e.exports=require("crypto")},55986:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var s=r(43210),a=r(87087);function o({userId:e,checkInterval:t=3e4,enabled:r=!0}){let[o,n]=(0,s.useState)({blocked:!1,lastChecked:new Date}),[i,l]=(0,s.useState)(!1);return{leaveStatus:o,isChecking:i,checkLeaveStatus:(0,s.useCallback)(async()=>{if(e&&r)try{l(!0);let t=await (0,a.q8)(e);return n({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),n(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{l(!1)}},[e,r]),isBlocked:o.blocked}}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87087:(e,t,r)=>{"use strict";r.d(t,{applyUserLeave:()=>d,calculateActiveDays:()=>x,cancelUserLeave:()=>p,createAdminLeave:()=>n,deleteAdminLeave:()=>l,getAdminLeaves:()=>i,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>h,isAdminLeaveDay:()=>c,isUserOnLeave:()=>m,q8:()=>g});var s=r(33784),a=r(75535);let o={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function n(e){try{return(await (0,a.gS)((0,a.collection)(s.db,o.adminLeaves),{...e,date:a.Dc.fromDate(e.date),createdAt:a.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function i(){try{let e=(0,a.P)((0,a.collection)(s.db,o.adminLeaves),(0,a.My)("date","asc")),t=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function l(e){try{await (0,a.kd)((0,a.H9)(s.db,o.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function c(e){try{let t=new Date(e);t.setHours(0,0,0,0);let r=new Date(e);r.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",r.toISOString());let n=(0,a.P)((0,a.collection)(s.db,o.adminLeaves),(0,a._M)("date",">=",a.Dc.fromDate(t)),(0,a._M)("date","<=",a.Dc.fromDate(r))),i=await (0,a.getDocs)(n),l=!i.empty;return l?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),l}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,r,n,i=new Date,l=i.getFullYear(),c=i.getMonth()+1,d=await h(e.userId,l,c),u="pending";return d<4&&(u="approved",t="system",n=a.Dc.now(),r=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,a.gS)((0,a.collection)(s.db,o.userLeaves),{...e,date:a.Dc.fromDate(e.date),status:u,appliedAt:a.Dc.now(),...t&&{reviewedBy:t},...n&&{reviewedAt:n},...r&&{reviewNotes:r}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,a.P)((0,a.collection)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a.My)("date","desc"));return(await (0,a.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function p(e){try{await (0,a.kd)((0,a.H9)(s.db,o.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function h(e,t,r){try{let n=new Date(t,r-1,1),i=new Date(t,r,0,23,59,59,999),l=(0,a.P)((0,a.collection)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a._M)("status","==","approved"),(0,a._M)("date",">=",a.Dc.fromDate(n)),(0,a._M)("date","<=",a.Dc.fromDate(i)));return(await (0,a.getDocs)(l)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function m(e,t){try{let r=new Date(t);r.setHours(0,0,0,0);let n=new Date(t);n.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",r.toISOString(),"to",n.toISOString());let i=(0,a.P)((0,a.collection)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a._M)("status","==","approved"),(0,a._M)("date",">=",a.Dc.fromDate(r)),(0,a._M)("date","<=",a.Dc.fromDate(n))),l=await (0,a.getDocs)(i),c=!l.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function x(e,t){let r=new Date,n=Math.floor((r.getTime()-t.getTime())/864e5);try{let i=(0,a.P)((0,a.collection)(s.db,o.adminLeaves),(0,a._M)("date",">=",a.Dc.fromDate(t)),(0,a._M)("date","<=",a.Dc.fromDate(r))),l=(await (0,a.getDocs)(i)).size,c=(0,a.P)((0,a.collection)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a._M)("status","==","approved"),(0,a._M)("date",">=",a.Dc.fromDate(t)),(0,a._M)("date","<=",a.Dc.fromDate(r))),d=(await (0,a.getDocs)(c)).size;return Math.max(0,n-l-d)}catch(e){return console.error("Error calculating active days:",e),Math.max(0,n)}}async function g(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await c(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let r=await m(e,t);if(console.log("\uD83D\uDC64 User leave check result:",r),r)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,5901,3582,8126],()=>r(48060));module.exports=s})();