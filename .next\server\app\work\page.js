(()=>{var e={};e.id=4246,e.ids=[4246,7087],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19645:(e,t,a)=>{Promise.resolve().then(a.bind(a,30766))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28879:(e,t,a)=>{"use strict";a.d(t,{Dt:()=>l,PO:()=>d,Qy:()=>i,bQ:()=>c,cb:()=>s,jQ:()=>u});let r={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},s=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}];function n(e){let t=function(e){try{let t=localStorage.getItem(`${r.BATCH_PREFIX}${e}`);if(!t)return null;let a=JSON.parse(t);if(Date.now()-a.lastUpdated>864e5)return localStorage.removeItem(`${r.BATCH_PREFIX}${e}`),null;return a}catch(t){return console.error(`Error loading translation batch ${e}:`,t),null}}(e);return t?t.translations:[]}function o(){return n(parseInt(localStorage.getItem(r.CURRENT_BATCH)||"0"))}function l(){let e=parseInt(localStorage.getItem(r.TOTAL_TRANSLATIONS)||"0"),t=parseInt(localStorage.getItem(r.CURRENT_BATCH)||"0"),a=Math.ceil(e/100),s=n(t);return{totalTranslations:e,currentBatch:t,totalBatches:a,translationsInCurrentBatch:s.length}}function i(){Object.keys(localStorage).forEach(e=>{(e.startsWith(r.BATCH_PREFIX)||Object.values(r).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function c(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error(`Failed to load translations: ${e.statusText}`);let t=await e.json();console.log("Raw translation data loaded:",t.length,"entries");let a=[];return Array.isArray(t)&&t.forEach((e,t)=>{e.english&&a.push({id:`translation_${t}_${Date.now()}`,english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian,category:"General",batchIndex:Math.floor(a.length/100)})}),a}catch(e){throw console.error("Error loading translations from file:",e),e}}async function d(){try{if(!function(){let e=localStorage.getItem(r.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached translation data..."),o();{console.log("Loading fresh translation data...");let e=await c();return!function(e){let t=Math.ceil(e.length/100);for(let s=0;s<t;s++){let t=100*s,n=Math.min(t+100,e.length),o=e.slice(t,n);var a=s;try{let e={batchNumber:a,translations:o,totalTranslations:o.length,lastUpdated:Date.now()};localStorage.setItem(`${r.BATCH_PREFIX}${a}`,JSON.stringify(e))}catch(e){console.error(`Error saving translation batch ${a}:`,e)}}localStorage.setItem(r.TOTAL_TRANSLATIONS,e.length.toString()),localStorage.setItem(r.CURRENT_BATCH,"0"),localStorage.setItem(r.LAST_PROCESSED,Date.now().toString()),console.log(`Saved ${e.length} translations in ${t} batches`)}(e),o()}}catch(t){console.error("Error initializing translation system:",t);let e=o();if(e.length>0)return console.log("Using cached translations as fallback"),e;throw t}}function u(){let e=Math.floor(Math.random()*s.length);return s[e].code}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},48060:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=a(65239),s=a(48088),n=a(88170),o=a.n(n),l=a(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54557:(e,t,a)=>{Promise.resolve().then(a.bind(a,92920))},55511:e=>{"use strict";e.exports=require("crypto")},55986:(e,t,a)=>{"use strict";a.d(t,{l:()=>n});var r=a(43210),s=a(87087);function n({userId:e,checkInterval:t=3e4,enabled:a=!0}){let[n,o]=(0,r.useState)({blocked:!1,lastChecked:new Date}),[l,i]=(0,r.useState)(!1);return{leaveStatus:n,isChecking:l,checkLeaveStatus:(0,r.useCallback)(async()=>{if(e&&a)try{i(!0);let t=await (0,s.q8)(e);return o({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),o(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{i(!1)}},[e,a]),isBlocked:n.blocked}}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87087:(e,t,a)=>{"use strict";a.d(t,{applyUserLeave:()=>d,calculateActiveDays:()=>p,cancelUserLeave:()=>h,createAdminLeave:()=>o,deleteAdminLeave:()=>i,getAdminLeaves:()=>l,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>g,isAdminLeaveDay:()=>c,isUserOnLeave:()=>m,q8:()=>x});var r=a(33784),s=a(75535);let n={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function o(e){try{return(await (0,s.gS)((0,s.collection)(r.db,n.adminLeaves),{...e,date:s.Dc.fromDate(e.date),createdAt:s.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function l(){try{let e=(0,s.P)((0,s.collection)(r.db,n.adminLeaves),(0,s.My)("date","asc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function i(e){try{await (0,s.kd)((0,s.H9)(r.db,n.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function c(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let o=(0,s.P)((0,s.collection)(r.db,n.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),l=await (0,s.getDocs)(o),i=!l.empty;return i?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),i}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,a,o,l=new Date,i=l.getFullYear(),c=l.getMonth()+1,d=await g(e.userId,i,c),u="pending";return d<4&&(u="approved",t="system",o=s.Dc.now(),a=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,s.gS)((0,s.collection)(r.db,n.userLeaves),{...e,date:s.Dc.fromDate(e.date),status:u,appliedAt:s.Dc.now(),...t&&{reviewedBy:t},...o&&{reviewedAt:o},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,s.P)((0,s.collection)(r.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s.My)("date","desc"));return(await (0,s.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function h(e){try{await (0,s.kd)((0,s.H9)(r.db,n.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function g(e,t,a){try{let o=new Date(t,a-1,1),l=new Date(t,a,0,23,59,59,999),i=(0,s.P)((0,s.collection)(r.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(o)),(0,s._M)("date","<=",s.Dc.fromDate(l)));return(await (0,s.getDocs)(i)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function m(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let o=new Date(t);o.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",o.toISOString());let l=(0,s.P)((0,s.collection)(r.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(a)),(0,s._M)("date","<=",s.Dc.fromDate(o))),i=await (0,s.getDocs)(l),c=!i.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function p(e,t){let a=new Date,o=Math.floor((a.getTime()-t.getTime())/864e5);try{let l=(0,s.P)((0,s.collection)(r.db,n.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),i=(await (0,s.getDocs)(l)).size,c=(0,s.P)((0,s.collection)(r.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),d=(await (0,s.getDocs)(c)).size;return Math.max(0,o-i-d)}catch(e){return console.error("Error calculating active days:",e),Math.max(0,o)}}async function x(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await c(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await m(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},91645:e=>{"use strict";e.exports=require("net")},92920:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(60687),s=a(43210),n=a(85814),o=a.n(n),l=a(87979),i=a(744),c=a(55986),d=a(3582);a(87087);var u=a(28879),h=a(98873),g=a(77567);function m(){let{user:e,loading:t}=(0,l.Nu)(),{hasBlockingNotifications:a,isChecking:n,markAllAsRead:m}=(0,i.J)(e?.uid||null),{isBlocked:p,leaveStatus:x}=(0,c.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e}),[f,b]=(0,s.useState)(null),[v,w]=(0,s.useState)(0),[y,N]=(0,s.useState)(0),[S,D]=(0,s.useState)(0),[k,j]=(0,s.useState)(!1),[C,T]=(0,s.useState)(!1),[A,_]=(0,s.useState)([]),[P,E]=(0,s.useState)(!0),[L,I]=(0,s.useState)(""),[M,q]=(0,s.useState)([]),[O,R]=(0,s.useState)(!1),[U,B]=(0,s.useState)(!1),[$,H]=(0,s.useState)(""),[Y,J]=(0,s.useState)(!1),[F,z]=(0,s.useState)(!1),[G,W]=(0,s.useState)(0),[X,K]=(0,s.useState)(!1),[Q,V]=(0,s.useState)({earningPerBatch:10,plan:"Trial"}),[Z,ee]=(0,s.useState)(null),[et,ea]=(0,s.useState)(0),[er,es]=(0,s.useState)(0),en=(e=A)=>{if(0===e.length)return;let t=Math.floor(Math.random()*e.length),a=e[t],r=(0,u.jQ)(),s=u.cb.find(e=>e.code===r);b({id:`step_${Date.now()}_${Math.random()}`,englishText:a.english,targetLanguage:r,targetLanguageName:s?.name||"Unknown",targetTranslation:a[r]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),I(""),q([]),R(!1),H(""),J(!1),z(!1),K(!1)},eo=(0,s.useCallback)(e=>{if(!f||O||X)return;let t=e.target.value,a=Date.now();if(el(t,a)&&(K(!0),g.A.fire({icon:"error",title:"Paste Detected!",text:U?"Copy-paste is allowed for your account.":"Copy-paste is not allowed. Please type the text manually.",timer:3e3}),!U)){e.target.value=L;return}let r=eu(t,f.englishText);q(r),I(t),t===f.englishText&&0===r.length&&(R(!0),g.A.fire({icon:"success",title:"Perfect!",text:"Text typed correctly. Now select the target language.",timer:2e3,showConfirmButton:!1})),W(a)},[f,O,X,U,L]),el=(e,t)=>{let a=t-G,r=e.length-L.length;return r>3&&a<100?(console.log("\uD83D\uDEAB Paste detected: Multiple characters at once"),!0):r>1&&a<50?(console.log("\uD83D\uDEAB Paste detected: Unrealistic typing speed"),!0):r>10?(console.log("\uD83D\uDEAB Paste detected: Large text block"),!0):!!(e.length>20)&&e===f?.englishText.substring(0,e.length)&&!!(e.length/(t-(G||t))>.1)&&(console.log("\uD83D\uDEAB Paste detected: Perfect match with high speed"),!0)},ei=(0,s.useCallback)(e=>{!U&&((e.ctrlKey||e.metaKey)&&"v"===e.key&&(e.preventDefault(),K(!0),g.A.fire({icon:"error",title:"Paste Not Allowed!",text:"Keyboard paste shortcuts are disabled. Please type manually.",timer:2e3})),e.repeat&&(console.log("\uD83D\uDEAB Long press detected"),"Backspace"!==e.key&&"Delete"!==e.key&&e.preventDefault()))},[U]),ec=(0,s.useCallback)(e=>{U||(e.preventDefault(),K(!0),g.A.fire({icon:"error",title:"Drag & Drop Not Allowed!",text:"Please type the text manually.",timer:2e3}))},[U]),ed=(0,s.useCallback)(e=>{U||(e.preventDefault(),g.A.fire({icon:"warning",title:"Context Menu Disabled",text:"Right-click menu is disabled to prevent paste operations.",timer:1500}))},[U]),eu=(e,t)=>{let a=[];for(let r=0;r<e.length;r++)(r>=t.length||e[r]!==t[r])&&a.push(r);return a},eh=e=>{f&&O&&(H(e),e===f.targetLanguage?(J(!0),g.A.fire({icon:"success",title:"Correct Language!",text:"You selected the correct language. Click Convert to see the translation.",timer:2e3,showConfirmButton:!1})):(J(!1),g.A.fire({icon:"error",title:"Wrong Language!",text:`Please select ${f.targetLanguageName} language.`,timer:2e3,showConfirmButton:!1})))},eg=async()=>{if(k&&!C&&!(S<50)){if(p)return void g.A.fire({icon:"warning",title:"Submission Not Available",text:x.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{T(!0);let t=Q.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"translation_earning",amount:t,description:"Batch completion reward - 50 translations completed"});let a=Math.min(v+50,50);w(a),N(y+50);let r=new Date().toDateString(),s=`translation_session_${e.uid}_${r}`;localStorage.removeItem(s),D(0),j(!1),g.A.fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:`
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${t} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),g.A.fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{T(!1)}}};return t||P||n?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:t?"Loading...":n?"Checking notifications...":"Loading translations..."})]})}):a&&e?(0,r.jsx)(h.A,{userId:e.uid,onAllRead:m}):(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)(o(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,r.jsxs)("div",{className:"text-white text-right",children:[(0,r.jsxs)("p",{className:"text-sm",children:["Plan: ",Q.plan]}),(0,r.jsxs)("p",{className:"text-sm",children:["₹",Q.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:et}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-blue-400",children:v}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Translations"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-green-400",children:y}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Total Translations"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-S)}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Translations Left"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[er,"/","Trial"===Q.plan?"2":"30"]}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,r.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,r.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to get new translation",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"New Translation"]})]}),f&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-keyboard mr-2"}),"Step 1: Type the English text below"]}),(0,r.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3",children:(0,r.jsx)("p",{className:"text-white text-lg font-mono",children:f.englishText})}),(0,r.jsx)("textarea",{value:L,onChange:eo,onKeyDown:ei,onDrop:ec,onContextMenu:ed,disabled:O||X,placeholder:U?"Type or paste the English text here...":"Type the English text here (copy-paste not allowed)...",className:`w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono ${M.length>0?"border-red-500":""} ${O?"border-green-500 bg-green-500/10":""}`,onPaste:e=>{U||(e.preventDefault(),K(!0),g.A.fire({icon:"error",title:"Paste Not Allowed!",text:"Please type the text manually.",timer:2e3}))},onDragOver:e=>{U||e.preventDefault()},spellCheck:!1,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off"}),M.length>0&&(0,r.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),"Typing errors detected. Please correct them to continue."]}),O&&(0,r.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Perfect! Text typed correctly."]}),X&&!U&&(0,r.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-ban mr-1"}),"Paste detected! Please type manually.",(0,r.jsx)("button",{onClick:()=>{K(!1),I("")},className:"ml-2 text-blue-400 underline",children:"Reset"})]})]}),O&&(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-globe mr-2"}),"Step 2: Select the target language - ",f.targetLanguageName]}),(0,r.jsxs)("select",{value:$,onChange:e=>eh(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:[(0,r.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select target language..."}),u.cb.map(e=>(0,r.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))]}),$&&!Y&&(0,r.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-times-circle mr-1"}),"Wrong language! Please select ",f.targetLanguageName,"."]}),Y&&(0,r.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Correct language selected!"]})]}),Y&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("button",{onClick:()=>{f&&Y&&(z(!0),b(e=>e?{...e,isConverted:!0}:null))},disabled:F,className:`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${F?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"}`,children:[(0,r.jsx)("i",{className:"fas fa-exchange-alt mr-2"}),"Convert to ",f.targetLanguageName]})}),F&&(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-language mr-2"}),f.targetLanguageName," Translation:"]}),(0,r.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-green-400",children:(0,r.jsx)("p",{className:"text-white text-lg",children:f.targetTranslation})}),(0,r.jsx)("div",{className:"text-center mt-4",children:(0,r.jsxs)("button",{onClick:()=>{if(!f||!F)return;b(e=>e?{...e,isSubmitted:!0}:null);let t=S+1;D(t);let a=new Date().toDateString(),r=`translation_session_${e.uid}_${a}`;localStorage.setItem(r,t.toString()),t<50?g.A.fire({icon:"success",title:"Translation Submitted!",text:`Progress: ${t}/50 translations completed.`,timer:2e3,showConfirmButton:!1}).then(()=>{en()}):g.A.fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},className:"btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105",children:[(0,r.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Translation"]})})]}),k&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("button",{onClick:eg,disabled:C,className:"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105",children:[(0,r.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit All 50 Translations & Earn ₹",Q.earningPerBatch]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:"text-white/80",children:["Progress: ",S,"/50 translations completed"]}),(0,r.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:`${S/50*100}%`}})})]})]})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[6204,2756,7567,5901,3582,8126],()=>a(48060));module.exports=r})();