(()=>{var e={};e.id=4246,e.ids=[4246,7087],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19645:(e,t,r)=>{Promise.resolve().then(r.bind(r,30766))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28879:(e,t,r)=>{"use strict";r.d(t,{Dt:()=>i,PO:()=>d,Qy:()=>l,bQ:()=>c,jQ:()=>u});let a={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},s=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}];function n(e){let t=function(e){try{let t=localStorage.getItem(`${a.BATCH_PREFIX}${e}`);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.lastUpdated>864e5)return localStorage.removeItem(`${a.BATCH_PREFIX}${e}`),null;return r}catch(t){return console.error(`Error loading translation batch ${e}:`,t),null}}(e);return t?t.translations:[]}function o(){return n(parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0"))}function i(){let e=parseInt(localStorage.getItem(a.TOTAL_TRANSLATIONS)||"0"),t=parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0"),r=Math.ceil(e/100),s=n(t);return{totalTranslations:e,currentBatch:t,totalBatches:r,translationsInCurrentBatch:s.length}}function l(){Object.keys(localStorage).forEach(e=>{(e.startsWith(a.BATCH_PREFIX)||Object.values(a).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function c(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error(`Failed to load translations: ${e.statusText}`);let t=await e.json();console.log("Raw translation data loaded:",t.length,"entries");let r=[];return Array.isArray(t)&&t.forEach((e,t)=>{e.english&&r.push({id:`translation_${t}_${Date.now()}`,english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian,category:"General",batchIndex:Math.floor(r.length/100)})}),r}catch(e){throw console.error("Error loading translations from file:",e),e}}async function d(){try{if(!function(){let e=localStorage.getItem(a.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached translation data..."),o();{console.log("Loading fresh translation data...");let e=await c();return!function(e){let t=Math.ceil(e.length/100);for(let s=0;s<t;s++){let t=100*s,n=Math.min(t+100,e.length),o=e.slice(t,n);var r=s;try{let e={batchNumber:r,translations:o,totalTranslations:o.length,lastUpdated:Date.now()};localStorage.setItem(`${a.BATCH_PREFIX}${r}`,JSON.stringify(e))}catch(e){console.error(`Error saving translation batch ${r}:`,e)}}localStorage.setItem(a.TOTAL_TRANSLATIONS,e.length.toString()),localStorage.setItem(a.CURRENT_BATCH,"0"),localStorage.setItem(a.LAST_PROCESSED,Date.now().toString()),console.log(`Saved ${e.length} translations in ${t} batches`)}(e),o()}}catch(t){console.error("Error initializing translation system:",t);let e=o();if(e.length>0)return console.log("Using cached translations as fallback"),e;throw t}}function u(){let e=Math.floor(Math.random()*s.length);return s[e].code}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},48060:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54557:(e,t,r)=>{Promise.resolve().then(r.bind(r,92920))},55511:e=>{"use strict";e.exports=require("crypto")},55986:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});var a=r(43210),s=r(87087);function n({userId:e,checkInterval:t=3e4,enabled:r=!0}){let[n,o]=(0,a.useState)({blocked:!1,lastChecked:new Date}),[i,l]=(0,a.useState)(!1);return{leaveStatus:n,isChecking:i,checkLeaveStatus:(0,a.useCallback)(async()=>{if(e&&r)try{l(!0);let t=await (0,s.q8)(e);return o({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),o(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{l(!1)}},[e,r]),isBlocked:n.blocked}}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87087:(e,t,r)=>{"use strict";r.d(t,{applyUserLeave:()=>d,calculateActiveDays:()=>g,cancelUserLeave:()=>h,createAdminLeave:()=>o,deleteAdminLeave:()=>l,getAdminLeaves:()=>i,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>m,isAdminLeaveDay:()=>c,isUserOnLeave:()=>p,q8:()=>x});var a=r(33784),s=r(75535);let n={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function o(e){try{return(await (0,s.gS)((0,s.collection)(a.db,n.adminLeaves),{...e,date:s.Dc.fromDate(e.date),createdAt:s.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function i(){try{let e=(0,s.P)((0,s.collection)(a.db,n.adminLeaves),(0,s.My)("date","asc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function l(e){try{await (0,s.kd)((0,s.H9)(a.db,n.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function c(e){try{let t=new Date(e);t.setHours(0,0,0,0);let r=new Date(e);r.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",r.toISOString());let o=(0,s.P)((0,s.collection)(a.db,n.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(r))),i=await (0,s.getDocs)(o),l=!i.empty;return l?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),l}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,r,o,i=new Date,l=i.getFullYear(),c=i.getMonth()+1,d=await m(e.userId,l,c),u="pending";return d<4&&(u="approved",t="system",o=s.Dc.now(),r=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,s.gS)((0,s.collection)(a.db,n.userLeaves),{...e,date:s.Dc.fromDate(e.date),status:u,appliedAt:s.Dc.now(),...t&&{reviewedBy:t},...o&&{reviewedAt:o},...r&&{reviewNotes:r}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,s.P)((0,s.collection)(a.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s.My)("date","desc"));return(await (0,s.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function h(e){try{await (0,s.kd)((0,s.H9)(a.db,n.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function m(e,t,r){try{let o=new Date(t,r-1,1),i=new Date(t,r,0,23,59,59,999),l=(0,s.P)((0,s.collection)(a.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(o)),(0,s._M)("date","<=",s.Dc.fromDate(i)));return(await (0,s.getDocs)(l)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function p(e,t){try{let r=new Date(t);r.setHours(0,0,0,0);let o=new Date(t);o.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",r.toISOString(),"to",o.toISOString());let i=(0,s.P)((0,s.collection)(a.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(r)),(0,s._M)("date","<=",s.Dc.fromDate(o))),l=await (0,s.getDocs)(i),c=!l.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function g(e,t){let r=new Date,o=Math.floor((r.getTime()-t.getTime())/864e5);try{let i=(0,s.P)((0,s.collection)(a.db,n.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(r))),l=(await (0,s.getDocs)(i)).size,c=(0,s.P)((0,s.collection)(a.db,n.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(r))),d=(await (0,s.getDocs)(c)).size;return Math.max(0,o-l-d)}catch(e){return console.error("Error calculating active days:",e),Math.max(0,o)}}async function x(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await c(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let r=await p(e,t);if(console.log("\uD83D\uDC64 User leave check result:",r),r)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},91645:e=>{"use strict";e.exports=require("net")},92920:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(60687),s=r(43210),n=r(85814),o=r.n(n),i=r(87979),l=r(744),c=r(55986),d=r(3582);r(87087),r(28879);var u=r(98873),h=r(77567);function m(){let{user:e,loading:t}=(0,i.Nu)(),{hasBlockingNotifications:r,isChecking:n,markAllAsRead:m}=(0,l.J)(e?.uid||null),{isBlocked:p,leaveStatus:g}=(0,c.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e}),[x,f]=(0,s.useState)(null),[b,v]=(0,s.useState)(0),[w,y]=(0,s.useState)(0),[S,D]=(0,s.useState)(50),[N,k]=(0,s.useState)(""),[j,_]=(0,s.useState)("hindi"),[A,T]=(0,s.useState)(0),[C,E]=(0,s.useState)(!1),[I,P]=(0,s.useState)(!1),[M,q]=(0,s.useState)([]),[L,O]=(0,s.useState)(!0),[R]=(0,s.useState)([{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}]),[U,B]=(0,s.useState)({earningPerBatch:10,plan:"Trial"}),[$,H]=(0,s.useState)(null),[J,Y]=(0,s.useState)(0),[F,G]=(0,s.useState)(0),z=async()=>{if(C&&!I&&!(A<50)){if(p)return void h.A.fire({icon:"warning",title:"Submission Not Available",text:g.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{P(!0);let t=U.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"translation_earning",amount:t,description:"Batch completion reward - 50 translations completed"});let r=Math.min(b+50,50);v(r),y(w+50),D(0);let a=new Date().toDateString(),s=`translation_session_${e.uid}_${a}`;localStorage.removeItem(s),T(0),E(!1),k(""),h.A.fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:`
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${t} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),h.A.fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{P(!1)}}};return t||L||n?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":n?"Checking notifications...":"Loading translations..."})]})}):r&&e?(0,a.jsx)(u.A,{userId:e.uid,onAllRead:m}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)(o(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,a.jsxs)("div",{className:"text-white text-right",children:[(0,a.jsxs)("p",{className:"text-sm",children:["Plan: ",U.plan]}),(0,a.jsxs)("p",{className:"text-sm",children:["₹",U.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:J}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-blue-400",children:b}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Translations"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-green-400",children:w}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Total Translations"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-purple-400",children:S}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Translations Left"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[F,"/","Trial"===U.plan?"2":"30"]}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to get new translation",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"New Translation"]})]}),x&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-flag mr-2"}),"English Text:"]}),(0,a.jsx)("p",{className:"text-white text-lg bg-white/5 p-3 rounded border-l-4 border-blue-400",children:x.english})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-globe mr-2"}),"Translate to:"]}),(0,a.jsx)("select",{value:j,onChange:e=>_(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:R.map(e=>(0,a.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-edit mr-2"}),"Your Translation:"]}),(0,a.jsx)("textarea",{value:N,onChange:e=>k(e.target.value),placeholder:`Enter your ${R.find(e=>e.code===j)?.name} translation here...`,className:"w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none"})]}),(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsxs)("button",{onClick:()=>{if(!N.trim()||A>=50)return;if(p)return void h.A.fire({icon:"warning",title:"Work Suspended",text:g.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});let t=A+1;T(t),D(Math.max(0,50-t));let r=new Date().toDateString(),a=`translation_session_${e.uid}_${r}`;if(localStorage.setItem(a,t.toString()),M.length>0){let e=Math.floor(Math.random()*M.length);f(M[e]);let t=Math.floor(Math.random()*R.length);_(R[t].code)}k(""),t<50?h.A.fire({icon:"success",title:"Translation Completed!",text:`Progress: ${t}/50 translations completed. ${50-t} more to go!`,timer:2e3,showConfirmButton:!1}):h.A.fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:!N.trim()||A>=50,className:`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${!N.trim()||A>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"}`,children:[(0,a.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Translation"]}),C&&(0,a.jsxs)("button",{onClick:z,disabled:I,className:"btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit & Earn ₹",U.earningPerBatch]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-white/80",children:["Progress: ",A,"/50 translations completed"]}),(0,a.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:`${A/50*100}%`}})})]})]})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[6204,2756,7567,5901,3582,8126],()=>r(48060));module.exports=a})();