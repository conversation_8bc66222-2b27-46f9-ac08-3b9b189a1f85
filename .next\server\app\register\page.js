(()=>{var e={};e.id=2454,e.ids=[2454],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8548:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var t=s(60687),o=s(43210),a=s(85814),i=s.n(a),n=s(30474),l=s(63385),c=s(75535),d=s(33784),u=s(87979),m=s(3582),p=s(77567);function h(){let{user:e,loading:r}=(0,u.hD)(),[s,a]=(0,o.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[h,f]=(0,o.useState)(!1),[g,x]=(0,o.useState)(!1),[b,w]=(0,o.useState)(!1),v=e=>{let{name:r,value:s}=e.target;a(e=>({...e,[r]:s}))},y=()=>{let{name:e,email:r,mobile:t,password:o,confirmPassword:a}=s;if(!e||!r||!t||!o||!a)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(t))throw Error("Please enter a valid 10-digit mobile number");if(o.length<6)throw Error("Password must be at least 6 characters long");if(o!==a)throw Error("Passwords do not match")},N=async e=>{e.preventDefault();try{y(),f(!0),console.log("Creating user with email and password...");let e=(await (0,l.eJ)(d.j2,s.email,s.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let r=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase(),o=`TN${r}${t}`;console.log("Generated referral code:",o);let a={[m.FIELD_NAMES.name]:s.name.trim(),[m.FIELD_NAMES.email]:s.email.toLowerCase(),[m.FIELD_NAMES.mobile]:s.mobile,[m.FIELD_NAMES.referralCode]:o,[m.FIELD_NAMES.referredBy]:s.referralCode||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:1,[m.FIELD_NAMES.joinedDate]:c.Dc.now(),[m.FIELD_NAMES.wallet]:0,[m.FIELD_NAMES.totalTranslations]:0,[m.FIELD_NAMES.todayTranslations]:0,[m.FIELD_NAMES.lastTranslationDate]:null,status:"active"};console.log("Creating user document with data:",a),console.log("User UID:",e.uid),console.log("Collection:",m.COLLECTIONS.users),console.log("Document path:",`${m.COLLECTIONS.users}/${e.uid}`),console.log("Creating user document in Firestore...");let i=(0,c.H9)(d.db,m.COLLECTIONS.users,e.uid);console.log("Document reference created:",i.path),console.log("About to create document with data:",JSON.stringify(a,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",i.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,c.BN)(i,a),console.log("✅ User document created successfully");let r=await (0,c.x7)(i);if(r.exists())console.log("✅ Document verification successful:",r.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error(`Failed to create user profile: ${e.message}. Your account was created but profile setup failed. Please contact support.`)}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),p.A.fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to MyTube!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(r){console.error("Registration error:",r),console.error("Error code:",r.code),console.error("Error message:",r.message),console.error("Full error object:",JSON.stringify(r,null,2));let e="An error occurred during registration";if(r.message.includes("fill in all"))e=r.message;else if(r.message.includes("Name must be"))e=r.message;else if(r.message.includes("valid email"))e=r.message;else if(r.message.includes("valid 10-digit"))e=r.message;else if(r.message.includes("Password must be"))e=r.message;else if(r.message.includes("Passwords do not match"))e=r.message;else if(r.message.includes("email address is already registered"))e=r.message;else if(r.message.includes("mobile number is already registered"))e=r.message;else switch(r.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=r.message||"Registration failed"}p.A.fire({icon:"error",title:"Registration Failed",text:e})}finally{f(!1)}};return r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8",children:(0,t.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,t.jsx)("p",{className:"text-white/80",children:"Join Instra Global and start earning today"})]}),(0,t.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:s.name,onChange:v,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:s.email,onChange:v,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,t.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:s.mobile,onChange:v,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:g?"text":"password",id:"password",name:"password",value:s.password,onChange:v,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>x(!g),className:"password-toggle-btn","aria-label":g?"Hide password":"Show password",children:(0,t.jsx)("i",{className:`fas ${g?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:b?"text":"password",id:"confirmPassword",name:"confirmPassword",value:s.confirmPassword,onChange:v,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>w(!b),className:"password-toggle-btn","aria-label":b?"Hide confirm password":"Show confirm password",children:(0,t.jsx)("i",{className:`fas ${b?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,t.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:s.referralCode,onChange:v,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,t.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center mt-6",children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,t.jsx)(i(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)(i(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14008:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=s(65239),o=s(48088),a=s(88170),i=s.n(a),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(r,l);let c={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94530)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\register\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,s)=>{"use strict";s.d(r,{db:()=>c,j2:()=>l});var t=s(67989),o=s(63385),a=s(75535),i=s(70146);let n=(0,t.Dk)().length?(0,t.Sx)():(0,t.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,o.xI)(n),c=(0,a.aU)(n);(0,i.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35397:(e,r,s)=>{Promise.resolve().then(s.bind(s,8548))},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41477:(e,r,s)=>{Promise.resolve().then(s.bind(s,94530))},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,r,s)=>{"use strict";s.d(r,{M4:()=>n,_f:()=>i});var t=s(33784),o=s(77567);function a(e){try{Object.keys(localStorage).forEach(r=>{(r.includes(e)||r.startsWith("video_session_")||r.startsWith("watch_times_")||r.startsWith("video_refresh_")||r.startsWith("video_change_notification_")||r.startsWith("leave_")||r.includes("mytube_")||r.includes("user_"))&&localStorage.removeItem(r)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function i(e,r="/login"){try{if((await o.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&a(e),await t.j2.signOut(),o.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=r}),!0;return!1}catch(e){return console.error("Logout error:",e),o.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e,r="/login"){try{e&&a(e),await t.j2.signOut(),window.location.href=r}catch(e){console.error("Quick logout error:",e),window.location.href=r}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,r,s)=>{"use strict";s.d(r,{Nu:()=>i,hD:()=>a,wC:()=>n});var t=s(43210);s(63385),s(33784);var o=s(51278);function a(){let[e,r]=(0,t.useState)(null),[s,a]=(0,t.useState)(!0),i=async()=>{try{await (0,o.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:i}}function i(){let{user:e,loading:r}=a();return{user:e,loading:r}}function n(){let{user:e,loading:r}=a(),[s,o]=(0,t.useState)(!1),[i,n]=(0,t.useState)(!0);return{user:e,loading:r||i,isAdmin:s}}},91645:e=>{"use strict";e.exports=require("net")},94530:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\register\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6204,2756,7567,5901,3582],()=>s(14008));module.exports=t})();