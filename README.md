# Instra Global Next.js - Translation & Earning Platform

This is the Next.js version of the Instra Global platform, converted from the original MyTube implementation. Instra Global is a comprehensive translation and earning platform where users can translate text and earn money.

## Features

### User Features
- **Landing Page**: Modern, responsive homepage with PWA support
- **Authentication**: Secure login/register system with Firebase Auth
- **Dashboard**: Comprehensive user dashboard with earnings overview
- **Video Watching**: Interactive video watching interface with timer and progress tracking
- **Wallet System**: Multiple wallet types (Earning, Bonus, Main) with transaction history
- **Referral System**: Refer friends and earn bonuses
- **Profile Management**: User profile and settings management
- **Progressive Web App**: Installable PWA with offline support

### Admin Features
- **Admin Dashboard**: Comprehensive admin panel with statistics
- **User Management**: View, edit, and manage user accounts
- **Transaction Management**: Monitor all platform transactions
- **Withdrawal Management**: Process and approve user withdrawals
- **Settings**: Platform configuration and settings management
- **Analytics**: Real-time platform analytics and reporting

## Technology Stack

- **Frontend**: Next.js 15.3.3 with TypeScript
- **Styling**: Tailwind CSS with custom components
- **Authentication**: Firebase Auth
- **Database**: Firebase Firestore
- **Hosting**: Vercel (recommended) or Firebase Hosting
- **PWA**: Service Worker with Workbox
- **UI Components**: Custom React components
- **Icons**: Font Awesome 6
- **Notifications**: SweetAlert2

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Firebase project with Firestore and Authentication enabled

### Installation

1. **Clone the repository**
   ```bash
   cd "js/Node Mytube"
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Firebase**
   - Update `.env.local` with your Firebase configuration
   - Ensure Firestore security rules are properly configured
   - Enable Email/Password authentication in Firebase Console

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Environment Variables

Create a `.env.local` file with the following variables:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin panel pages
│   ├── dashboard/         # User dashboard
│   ├── login/             # Authentication pages
│   ├── work/              # Video watching interface
│   └── layout.tsx         # Root layout
├── components/            # Reusable React components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries
│   ├── firebase.ts        # Firebase configuration
│   ├── dataService.ts     # User data service
│   └── adminDataService.ts # Admin data service
└── styles/               # Global styles and Tailwind config

public/
├── img/                  # Images and logos
├── manifest.json         # PWA manifest
└── mytube.json          # Video data
```

## Key Features Implementation

### Authentication System
- Firebase Auth integration with custom hooks
- Role-based access control (User/Admin)
- Protected routes with automatic redirects

### Video Watching System
- Timer-based video watching (5 minutes per video)
- Progress tracking and submission
- Earnings calculation based on user plan
- Video rotation system

### Wallet System
- Multiple wallet types (Earning, Bonus, Main)
- Real-time balance updates
- Transaction history tracking
- Withdrawal processing

### Admin Panel
- Comprehensive dashboard with statistics
- User management with search and filters
- Transaction monitoring
- Withdrawal approval system
- Real-time data with caching

### Progressive Web App
- Service Worker for offline support
- App installation prompt
- Push notifications support
- Responsive design for all devices

## Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Firebase Hosting
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Build the project: `npm run build`
3. Deploy: `firebase deploy --only hosting`

## Configuration

### Firebase Security Rules
Ensure your Firestore security rules are properly configured for the collections used by the platform.

### Video Configuration
Update `public/mytube.json` with your video URLs and metadata.

### Plan Configuration
Modify the plan structure in the data service to match your pricing model.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For support and questions, please contact the development team or create an issue in the repository.

## License

This project is proprietary software. All rights reserved.
