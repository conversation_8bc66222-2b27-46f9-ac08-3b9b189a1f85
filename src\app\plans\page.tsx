'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuthState } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

interface Plan {
  id: string
  name: string
  price: number
  duration: number
  earningPerTranslation: number
  languages: number
  features: string[]
  popular?: boolean
  description: string
}

const plans: Plan[] = [
  {
    id: 'trial',
    name: 'Trial',
    price: 0,
    duration: 2,
    earningPerTranslation: 25,
    languages: 1,
    description: 'Explore and test your translation potential before handling real jobs',
    features: [
      '2 days access',
      '₹25 per 50 translations',
      'Basic support',
      'Learn the platform'
    ]
  },
  {
    id: 'junior',
    name: 'Junior',
    price: 499,
    duration: 30,
    earningPerTranslation: 150,
    languages: 1,
    description: 'Entry-level role for new freelancers starting their translation journey',
    features: [
      '30 days access',
      'Certified for 1 Language',
      '₹150 per 50 translations',
      'Basic support',
      'Translation training materials'
    ]
  },
  {
    id: 'senior',
    name: 'Senior',
    price: 1499,
    duration: 30,
    earningPerTranslation: 250,
    languages: 3,
    description: 'Senior role for experienced translators handling multiple Languages',
    features: [
      '30 days access',
      'Certified for 3 Languages',
      '₹250 per 50 translations',
      'Priority support',
      'Advanced translation tools',
      'Quality assurance training'
    ],
    popular: true
  },
  {
    id: 'expert',
    name: 'Expert',
    price: 2999,
    duration: 30,
    earningPerTranslation: 400,
    languages: 5,
    description: 'Top-tier role for expert translators with broad multi-language proficiency',
    features: [
      '30 days access',
      'Certified for 5 Languages',
      '₹400 per 50 translations',
      'VIP support',
      'Premium translation tools',
      'Dedicated account manager',
      'Exclusive high-value projects'
    ]
  }
]

export default function PlansPage() {
  const { user, loading } = useAuthState()
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handlePlanSelect = async (plan: Plan) => {
    if (!user) {
      Swal.fire({
        icon: 'info',
        title: 'Login Required',
        text: 'Please login to purchase a plan',
        showCancelButton: true,
        confirmButtonText: 'Login',
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/login'
        }
      })
      return
    }

    if (plan.id === 'trial') {
      Swal.fire({
        icon: 'info',
        title: 'Trial Plan',
        text: 'You are already on the trial plan. Upgrade to a paid plan for better earnings!',
      })
      return
    }

    setSelectedPlan(plan.id)
    setIsProcessing(true)

    try {
      // In a real implementation, this would integrate with a payment gateway
      // For now, we'll just show a message
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate processing

      Swal.fire({
        icon: 'info',
        title: 'Payment Integration Required',
        html: `
          <p>To complete your purchase of the <strong>${plan.name}</strong> plan (₹${plan.price}), please contact our support team.</p>
          <br>
          <p><strong>Plan Details:</strong></p>
          <ul style="text-align: left; margin: 10px 0;">
            <li>Duration: ${plan.duration} days</li>
            <li>Earning: ₹${plan.earningPerTranslation} per 50 translations</li>
            <li>Languages: Certified for ${plan.languages} language${plan.languages > 1 ? 's' : ''}</li>
          </ul>
          <br>
          <p><strong>Contact Options:</strong></p>
          <p>📧 Email: <strong><EMAIL></strong></p>
        `,
        confirmButtonText: 'Contact Support',
        showCancelButton: true,
        cancelButtonText: 'Cancel'
      })
    } catch (error) {
      console.error('Error processing plan selection:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to process plan selection. Please try again.',
      })
    } finally {
      setIsProcessing(false)
      setSelectedPlan(null)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <Link href={user ? "/dashboard" : "/"} className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back
          </Link>
          <h1 className="text-xl font-bold text-white">Choose Your Plan</h1>
          <div className="w-20"></div> {/* Spacer for centering */}
        </div>
      </header>

      {/* Plans Grid */}
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Start Earning with Instra Global
          </h2>
          <p className="text-white/80 text-lg max-w-2xl mx-auto">
            Choose the perfect plan for your translation career. Translate text and earn money with our professional certification levels.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`glass-card p-6 relative ${
                plan.popular ? 'ring-2 ring-yellow-400' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-white">₹{plan.price}</span>
                  {plan.price > 0 && (
                    <span className="text-white/60 ml-2">/ {plan.duration} days</span>
                  )}
                </div>
                <p className="text-green-400 font-semibold text-sm">
                  Earn ₹{plan.earningPerTranslation} per 50 translations
                </p>
                <p className="text-white/60 text-xs mt-2">
                  {plan.description}
                </p>
              </div>

              <ul className="space-y-2 mb-6 text-sm">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-white/80">
                    <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                    {feature}
                  </li>
                ))}
              </ul>

              <button
                onClick={() => handlePlanSelect(plan)}
                disabled={isProcessing && selectedPlan === plan.id}
                className={`w-full py-2 rounded-lg font-semibold transition-all duration-300 text-sm ${
                  plan.popular
                    ? 'bg-yellow-400 text-black hover:bg-yellow-500'
                    : plan.price === 0
                    ? 'bg-gray-600 text-white hover:bg-gray-700'
                    : 'bg-purple-600 text-white hover:bg-purple-700'
                } disabled:opacity-50`}
              >
                {isProcessing && selectedPlan === plan.id ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4 inline-block"></div>
                    Processing...
                  </>
                ) : plan.price === 0 ? (
                  'Start Free Trial'
                ) : (
                  `Choose ${plan.name}`
                )}
              </button>
            </div>
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-12 glass-card p-8">
          <h3 className="text-xl font-bold text-white mb-4 text-center">
            <i className="fas fa-info-circle mr-2"></i>
            Translation Plan Benefits
          </h3>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-white mb-3">Earning Structure</h4>
              <ul className="space-y-2 text-white/80">
                <li>• Complete 50 translations daily to earn the full amount</li>
                <li>• Each translation must be accurate and complete</li>
                <li>• Earnings are credited to your wallet</li>
                <li>• Higher plans offer better earning rates</li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-3">Language Certification</h4>
              <ul className="space-y-2 text-white/80">
                <li>• Higher plans certify you for more languages</li>
                <li>• Access to specialized translation projects</li>
                <li>• Professional translator recognition</li>
                <li>• Quality assurance and training included</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="mt-8 text-center">
          <p className="text-white/60 mb-4">
            Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):
          </p>
          <div className="flex justify-center">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-white hover:text-blue-400 transition-colors"
            >
              <i className="fas fa-envelope mr-2"></i>
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
