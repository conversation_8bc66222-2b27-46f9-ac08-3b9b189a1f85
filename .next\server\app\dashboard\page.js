(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21164:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},21820:e=>{"use strict";e.exports=require("os")},24871:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(60687),r=t(43210),l=t(77567);function i({variant:e="homepage",className:s=""}){let{isInstallable:t,isInstalled:i,installApp:n,getInstallInstructions:o}=function(){let[e,s]=(0,r.useState)(null),[t,a]=(0,r.useState)(!1),[l,i]=(0,r.useState)(!1);return{isInstallable:t,isInstalled:l,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:t}=await e.userChoice;if("accepted"===t)return i(!0),a(!1),s(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[c,d]=(0,r.useState)(!1),x=async()=>{await n()?l.A.fire({icon:"success",title:"App Installed!",text:"Instra Global has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):t||d(!0)},m=()=>{let e=o();l.A.fire({title:`Install Instra Global on ${e.browser}`,html:`
        <div class="text-left">
          <p class="mb-4 text-gray-600">Follow these steps to install Instra Global as an app:</p>
          <ol class="list-decimal list-inside space-y-2">
            ${e.steps.map(e=>`<li class="text-gray-700">${e}</li>`).join("")}
          </ol>
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-800">
              <i class="fas fa-info-circle mr-2"></i>
              Installing the app gives you faster access, offline capabilities, and a native app experience!
            </p>
          </div>
        </div>
      `,confirmButtonText:"Got it!",confirmButtonColor:"#6A11CB"})};return i?(0,a.jsx)("div",{className:`${s}`,children:"homepage"===e?(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,a.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,a.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===e?(0,a.jsxs)("div",{className:`glass-card p-8 hover:scale-105 transition-transform ${s}`,children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,a.jsx)("div",{className:"space-y-3",children:t?(0,a.jsxs)("button",{onClick:x,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,a.jsxs)("button",{onClick:m,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,a.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,a.jsx)("div",{className:`glass-card p-4 ${s}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),t?(0,a.jsxs)("button",{onClick:x,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,a.jsxs)("button",{onClick:m,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},50208:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58876:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(60687),r=t(43210),l=t(85814),i=t.n(l),n=t(30474),o=t(87979),c=t(744),d=t(3582),x=t(51278);function m({userId:e,isOpen:s,onClose:t}){let[l,i]=(0,r.useState)([]),[n,o]=(0,r.useState)(!0),c=async()=>{try{o(!0);let s=await (0,d.Ss)(e,20);i(s)}catch(e){console.error("Error loading notifications:",e)}finally{o(!1)}},x=s=>{s.id&&!(0,d.mv)(s.id,e)&&((0,d.bA)(s.id,e),i([...l]))},m=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},u=e=>{let s=Math.floor((new Date().getTime()-e.getTime())/1e3);if(s<60)return"Just now";if(s<3600){let e=Math.floor(s/60);return`${e} minute${e>1?"s":""} ago`}if(s<86400){let e=Math.floor(s/3600);return`${e} hour${e>1?"s":""} ago`}{let e=Math.floor(s/86400);return`${e} day${e>1?"s":""} ago`}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-2"}),"Notifications"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:c,disabled:n,className:"text-gray-500 hover:text-gray-700 transition-colors p-1",title:"Refresh notifications",children:(0,a.jsx)("i",{className:`fas fa-sync-alt ${n?"animate-spin":""}`})}),(0,a.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[60vh]",children:n?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"spinner w-8 h-8"})}):0===l.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No notifications yet"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"You'll see important updates here"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:l.map(s=>{let t=!!s.id&&(0,d.mv)(s.id,e);return(0,a.jsx)("div",{onClick:()=>x(s),className:`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${!t?"bg-blue-50 border-l-4 border-l-blue-500":""}`,children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,a.jsx)("i",{className:m(s.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:`text-sm font-medium ${!t?"text-gray-900":"text-gray-700"}`,children:s.title}),!t&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,a.jsx)("p",{className:`text-sm mt-1 ${!t?"text-gray-800":"text-gray-600"}`,children:s.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:u(s.createdAt)})]})]})},s.id)})})}),l.length>0&&(0,a.jsx)("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:(0,a.jsx)("button",{onClick:()=>{l.forEach(s=>{s.id&&!(0,d.mv)(s.id,e)&&(0,d.bA)(s.id,e)}),i([...l])},className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Mark all as read"})})]})}):null}function u({userId:e,onClick:s}){let[t,l]=(0,r.useState)([]),[i,n]=(0,r.useState)(0),[o,c]=(0,r.useState)(!1);return(0,a.jsxs)("button",{onClick:s,className:"relative p-2 text-white hover:text-yellow-300 transition-colors",title:`${i} unread notifications`,children:[(0,a.jsx)("i",{className:`fas fa-bell text-xl notification-bell ${o?"animate-pulse":""}`}),i>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse",children:i>9?"9+":i}),o&&(0,a.jsx)("span",{className:"absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-sync-alt text-xs animate-spin"})})]})}var p=t(98873),h=t(77567);function f({userId:e,currentMonth:s,usedLeaves:l,maxLeaves:i,onLeaveCountChange:n}){let[o,c]=(0,r.useState)([]),[d,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),[p,f]=(0,r.useState)(""),[b,g]=(0,r.useState)({date:"",reason:""}),v=async()=>{try{let{getUserLeaves:s}=await t.e(7087).then(t.bind(t,87087)),a=await s(e);c(a)}catch(e){console.error("Error loading user leaves:",e),c([])}},j=async()=>{try{if(!b.date||!b.reason.trim())return void h.A.fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let s=new Date(b.date),a=new Date;a.setHours(0,0,0,0);let r=new Date(a);if(r.setDate(r.getDate()+1),s<=a)return void h.A.fire({icon:"error",title:"Invalid Date",text:"Cannot apply leave for today or past dates. Please select a future date."});try{let{isUserPlanExpired:r}=await Promise.resolve().then(t.bind(t,3582));if((await r(e)).expired)return void h.A.fire({icon:"error",title:"Plan Expired",text:"Your plan has expired. Cannot apply for leave."});let{getUserData:l,getPlanValidityDays:i}=await Promise.resolve().then(t.bind(t,3582)),n=await l(e);if(n){let e;if("Trial"===n.plan){let s=n.joinedDate||new Date;e=new Date(s.getTime()+1728e5)}else if(n.planExpiry)e=n.planExpiry;else{let s=i(n.plan),t=n.activeDays||1,r=Math.max(0,s-t);e=new Date(a.getTime()+24*r*36e5)}if(s>e)return void h.A.fire({icon:"error",title:"Date Outside Plan Period",text:`Cannot apply leave beyond your plan expiry date (${e.toLocaleDateString()}).`})}}catch(e){console.error("Error checking plan expiry:",e)}if(l>=i)return void h.A.fire({icon:"error",title:"Leave Limit Exceeded",text:`You have already used all ${i} leaves for this month.`});if(o.find(e=>e.date.toDateString()===s.toDateString()))return void h.A.fire({icon:"error",title:"Duplicate Application",text:"You have already applied for leave on this date."});u(!0);let{applyUserLeave:c}=await t.e(7087).then(t.bind(t,87087)),d=await c({userId:e,date:s,reason:b.reason.trim()});await v(),n&&n(),d.autoApproved?h.A.fire({icon:"success",title:"✅ Leave Auto-Approved!",html:`
            <div class="text-left">
              <p><strong>Your leave has been automatically approved!</strong></p>
              <br>
              <p><strong>Date:</strong> ${s.toLocaleDateString()}</p>
              <p><strong>Reason:</strong> ${b.reason.trim()}</p>
              <br>
              <p class="text-green-600"><strong>Leave Quota:</strong> ${d.usedLeaves}/${d.maxLeaves} used this month</p>
              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>
            </div>
          `,timer:6e3,showConfirmButton:!0,confirmButtonText:"Great!"}):h.A.fire({icon:"warning",title:"⏳ Leave Pending Approval",html:`
            <div class="text-left">
              <p><strong>Your leave application has been submitted.</strong></p>
              <br>
              <p><strong>Date:</strong> ${s.toLocaleDateString()}</p>
              <p><strong>Reason:</strong> ${b.reason.trim()}</p>
              <br>
              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>
              <p class="text-gray-600"><strong>Leave Quota:</strong> ${d.maxLeaves}/${d.maxLeaves} used this month</p>
            </div>
          `,timer:6e3,showConfirmButton:!0,confirmButtonText:"Understood"}),g({date:"",reason:""}),x(!1)}catch(e){console.error("Error applying leave:",e),h.A.fire({icon:"error",title:"Application Failed",text:"Failed to apply for leave. Please try again."})}finally{u(!1)}},N=async e=>{try{let s=o.find(s=>s.id===e);if(!s||"pending"!==s.status)return void h.A.fire({icon:"error",title:"Cannot Cancel",text:"Only pending leave applications can be cancelled."});if((await h.A.fire({title:"Cancel Leave Application",text:"Are you sure you want to cancel this leave application?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Cancel",cancelButtonText:"Keep Application"})).isConfirmed){let{cancelUserLeave:s}=await t.e(7087).then(t.bind(t,87087));await s(e),await v(),n&&n(),h.A.fire({icon:"success",title:"Application Cancelled",text:"Your leave application has been cancelled.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error cancelling leave:",e),h.A.fire({icon:"error",title:"Cancellation Failed",text:"Failed to cancel leave application. Please try again."})}},w=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=(e,s)=>{switch(e){case"approved":return"system"===s?"fas fa-magic text-green-500":"fas fa-check-circle text-green-500";case"rejected":return"fas fa-times-circle text-red-500";case"pending":return"fas fa-clock text-yellow-500";default:return"fas fa-question-circle text-gray-500"}},C=i-l;return(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-2"}),"Leave Management"]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm text-white/80",children:[s," Leaves"]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:[C,"/",i," Available"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-400",children:i}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Monthly Quota"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-yellow-400",children:l}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Used"})]}),(0,a.jsxs)("div",{className:"bg-blue-500/20 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-blue-400",children:C}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Remaining"})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>x(!0),disabled:C<=0,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),C>0?"Apply for Leave":"No Leaves Available"]})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white/80",children:"Recent Applications"}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-4 text-white/60",children:[(0,a.jsx)("i",{className:"fas fa-calendar-check text-2xl mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No leave applications yet"})]}):(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:o.slice(-5).reverse().map(e=>(0,a.jsx)("div",{className:"bg-white/10 p-3 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:e.date.toLocaleDateString()}),(0,a.jsxs)("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${w(e.status)}`,children:[(0,a.jsx)("i",{className:`${y(e.status,e.reviewedBy)} mr-1`}),e.status.charAt(0).toUpperCase()+e.status.slice(1),"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsx)("span",{className:"ml-1",title:"Auto-approved",children:"⚡"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-white/70 mt-1",children:[e.reason,"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-magic mr-1"}),"Auto-approved (within quota)"]}),e.reviewNotes&&"system"!==e.reviewedBy&&(0,a.jsxs)("div",{className:"text-xs text-blue-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-comment mr-1"}),e.reviewNotes]})]})]}),"pending"===e.status&&(0,a.jsx)("button",{onClick:()=>N(e.id),className:"text-red-400 hover:text-red-300 text-sm",children:(0,a.jsx)("i",{className:"fas fa-times"})})]})},e.id))})]}),d&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Apply for Leave"}),(0,a.jsx)("button",{onClick:()=>x(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),p&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["Leave applications are allowed until ",new Date(p).toLocaleDateString()]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,a.jsx)("input",{type:"date",value:b.date,onChange:e=>g(s=>({...s,date:e.target.value})),min:(()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})(),max:p,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave can only be applied for future dates within your plan period"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,a.jsx)("textarea",{value:b.reason,onChange:e=>g(s=>({...s,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"You have ",C," leave(s) remaining for ",s,"."]}),C>0&&(0,a.jsxs)("div",{className:"text-sm text-green-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("strong",{children:"Auto-Approval:"})," Your leave will be automatically approved since you have available quota."]}),C<=0&&(0,a.jsxs)("div",{className:"text-sm text-orange-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),(0,a.jsx)("strong",{children:"Manual Review:"})," Leave will require admin approval as quota is exceeded."]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,a.jsx)("button",{onClick:()=>x(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,a.jsx)("button",{onClick:j,disabled:m,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Applying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Apply Leave"]})})]})]})})]})}var b=t(24871);function g(){let{user:e,loading:s}=(0,o.Nu)(),{hasBlockingNotifications:l,isChecking:d,markAllAsRead:h}=(0,c.J)(e?.uid||null),[g,v]=(0,r.useState)(null),[j,N]=(0,r.useState)(null),[w,y]=(0,r.useState)(null),[C,A]=(0,r.useState)(!0),[k,S]=(0,r.useState)(!1),[q,D]=(0,r.useState)(0),P=async()=>{try{let{getUserMonthlyLeaveCount:s}=await t.e(7087).then(t.bind(t,87087)),a=new Date,r=a.getFullYear(),l=a.getMonth()+1,i=await s(e.uid,r,l);return D(i),console.log(`User ${e.uid} has used ${i} leaves this month`),i}catch(e){return console.error("Error loading user leave count:",e),D(0),0}};return s||C||d?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:s?"Loading...":d?"Checking notifications...":"Loading dashboard..."})]})}):l&&e?(0,a.jsx)(p.A,{userId:e.uid,onAllRead:h}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:40,height:40,className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Instra Global Dashboard"}),(0,a.jsxs)("p",{className:"text-white/80",children:["Welcome back, ",g?.name||"User"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsx)(u,{userId:e.uid,onClick:()=>S(!0)}),(0,a.jsxs)("button",{onClick:()=>{(0,x._f)(e?.uid,"/login")},className:"glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6",children:[(0,a.jsxs)(i(),{href:"/work",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-play-circle text-3xl text-youtube-red mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Watch Videos"})]}),(0,a.jsxs)(i(),{href:"/wallet",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-wallet text-3xl text-green-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Wallet"})]}),(0,a.jsxs)(i(),{href:"/transactions",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-history text-3xl text-orange-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Transactions"})]}),(0,a.jsxs)(i(),{href:"/refer",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-users text-3xl text-blue-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Refer & Earn"})]}),(0,a.jsxs)(i(),{href:"/profile",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-user text-3xl text-purple-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Profile"})]}),(0,a.jsxs)(i(),{href:"/plans",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-crown text-3xl text-yellow-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Plans"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-wallet mr-2"}),"Wallet Overview"]}),(0,a.jsxs)("div",{className:"bg-green-500/20 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"My Wallet"}),(0,a.jsxs)("p",{className:"text-4xl font-bold text-white mb-2",children:["₹",(j?.wallet||0).toFixed(2)]}),(0,a.jsx)("p",{className:"text-white/60",children:"Total available balance"}),g?.plan==="Trial"&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,a.jsx)("span",{className:"text-red-400 font-medium text-sm",children:"Withdrawal Restricted"})]}),(0,a.jsx)("p",{className:"text-white/80 text-xs mb-3",children:"Trial users cannot withdraw funds. Upgrade to enable withdrawals."}),(0,a.jsxs)(i(),{href:"/plans",className:"btn-secondary text-xs px-3 py-1",children:[(0,a.jsx)("i",{className:"fas fa-arrow-up mr-1"}),"Upgrade Plan"]})]}),(0,a.jsxs)(i(),{href:"/wallet",className:"btn-primary mt-4 inline-block",children:[(0,a.jsx)("i",{className:"fas fa-eye mr-2"}),"View Details"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-video mr-2"}),"Today's Progress"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-youtube-red",children:w?.todayVideos||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Videos Watched"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-400",children:w?.remainingVideos||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Remaining"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-400",children:w?.totalVideos||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Videos"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"bg-white/20 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-youtube-red h-3 rounded-full transition-all duration-300",style:{width:`${w?w.todayVideos/50*100:0}%`}})}),(0,a.jsxs)("p",{className:"text-white/80 text-sm mt-2 text-center",children:[w?.todayVideos||0," / 50 videos completed today"]})]})]}),e&&(0,a.jsx)(f,{userId:e.uid,currentMonth:new Date().toLocaleDateString("en-US",{month:"long",year:"numeric"}),usedLeaves:q,maxLeaves:4,onLeaveCountChange:P}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-headset mr-2"}),"Need Help?"]}),(0,a.jsx)("p",{className:"text-white/60 mb-6",children:"Our support team is here to help you with any questions about earning, withdrawals, or your account."}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("a",{href:"https://wa.me/************",target:"_blank",rel:"noopener noreferrer",className:"flex items-center bg-green-500/20 border border-green-500/30 rounded-lg p-4 hover:bg-green-500/30 transition-colors",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-green-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:"WhatsApp Support"}),(0,a.jsx)("div",{className:"text-green-400 text-sm",children:"+91 **********"}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:"9 AM - 6 PM (Working days)"})]})]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-500/30 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-blue-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:"Email Support"}),(0,a.jsx)("div",{className:"text-blue-400 text-sm",children:"<EMAIL>"}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:"9 AM - 6 PM (Working days)"})]})]})]})]}),(0,a.jsx)(b.A,{variant:"dashboard",className:"mb-6"}),e&&(0,a.jsx)(m,{userId:e.uid,isOpen:k,onClose:()=>S(!1)})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},91660:(e,s,t)=>{Promise.resolve().then(t.bind(t,58876))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[6204,2756,7567,5901,3582,8126],()=>t(50208));module.exports=a})();