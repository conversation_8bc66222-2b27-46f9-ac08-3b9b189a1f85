(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4363],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>c,_f:()=>n});var a=s(6104),r=s(4752),i=s.n(r);function l(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e),await a.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&l(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},2813:(e,t,s)=>{Promise.resolve().then(s.bind(s,4665))},4665:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(5155),r=s(2115),i=s(6874),l=s.n(i),n=s(6681),c=s(3592),o=s(4752),d=s.n(o);function h(){let{user:e,loading:t}=(0,n.Nu)(),[s,i]=(0,r.useState)(null),[o,h]=(0,r.useState)([]),[m,x]=(0,r.useState)(!0),[u,f]=(0,r.useState)(0);(0,r.useEffect)(()=>{e&&g()},[e]);let g=async()=>{try{x(!0);let t=await (0,c.getUserData)(e.uid);if(i(t),null==t?void 0:t.referralCode){let e=await (0,c.pl)(t.referralCode);h(e);let s=e.reduce((e,t)=>e+p(t.plan),0);f(s)}}catch(e){console.error("Error loading referral data:",e),d().fire({icon:"error",title:"Error",text:"Failed to load referral data. Please try again."})}finally{x(!1)}},j=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},p=e=>({Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200})[e]||0;return t||m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Refer & Earn"}),(0,a.jsxs)("button",{onClick:g,className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-code mr-2"}),"Your Referral Code"]}),(0,a.jsx)("div",{className:"bg-white/10 p-4 rounded-lg mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-2xl font-mono font-bold text-white",children:(null==s?void 0:s.referralCode)||"Loading..."}),(0,a.jsxs)("button",{onClick:()=>{(null==s?void 0:s.referralCode)&&(navigator.clipboard.writeText(s.referralCode),d().fire({icon:"success",title:"Copied!",text:"Referral code copied to clipboard",timer:1500,showConfirmButton:!1}))},className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-copy mr-2"}),"Copy"]})]})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>{if(null==s?void 0:s.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(s.referralCode);navigator.clipboard.writeText(e),d().fire({icon:"success",title:"Link Copied!",text:"Referral link copied to clipboard",timer:2e3,showConfirmButton:!1})}},className:"btn-primary",children:[(0,a.jsx)("i",{className:"fas fa-link mr-2"}),"Copy Referral Link"]}),(0,a.jsxs)("button",{onClick:()=>{if(null==s?void 0:s.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(s.referralCode),t="\uD83C\uDF89 Join MyTube and start earning money by watching videos!\n\n\uD83D\uDCB0 Earn up to ₹30,000 per month\n\uD83C\uDFAC Watch videos and get paid\n⚡ Quick and easy registration\n\nUse my referral code: ".concat(s.referralCode,"\n\nJoin now: ").concat(e);navigator.share?navigator.share({title:"Join MyTube and Start Earning",text:t,url:e}):(navigator.clipboard.writeText(t),d().fire({icon:"success",title:"Message Copied!",text:"Referral message copied to clipboard. Share it with your friends!",timer:2e3,showConfirmButton:!1}))}},className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-share mr-2"}),"Share with Friends"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-list mr-2"}),"Your Referrals (",o.length,")"]}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-users text-white/30 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 mb-4",children:"No referrals yet"}),(0,a.jsx)("p",{className:"text-white/40 text-sm",children:"Start sharing your referral code to earn bonuses!"})]}):(0,a.jsx)("div",{className:"space-y-3",children:o.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("span",{className:"text-white font-bold",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:e.email}),(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:["Joined: ",e.joinedDate.toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-white text-sm ".concat(j(e.plan)),children:e.plan}),(0,a.jsxs)("p",{className:"text-green-400 font-bold mt-1",children:["+₹",p(e.plan)]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-users text-4xl text-blue-400 mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:o.length}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Referrals"})]}),(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-rupee-sign text-4xl text-green-400 mb-4"}),(0,a.jsxs)("h3",{className:"text-2xl font-bold text-white",children:["₹",u]}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Earnings"})]}),(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-gift text-4xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:o.filter(e=>"Trial"!==e.plan).length}),(0,a.jsx)("p",{className:"text-white/80",children:"Paid Referrals"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How Referral Works"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Share Your Code"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Share your referral code or link with friends"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Friend Joins"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Your friend registers using your referral code"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Friend Upgrades"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"When they purchase a plan, you earn bonus"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"You Earn + 50 Videos"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"₹50-₹1200 bonus + 50 lifetime videos based on plan"}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-white/60",children:[(0,a.jsx)("div",{children:"₹499 Plan: ₹50 • ₹2999 Plan: ₹300"}),(0,a.jsx)("div",{children:"₹3999 Plan: ₹400 • ₹5999 Plan: ₹700 • ₹9999 Plan: ₹1200"})]})]})]})]})]})]})]})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>o,j2:()=>c});var a=s(3915),r=s(3004),i=s(5317),l=s(858);let n=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,r.xI)(n),o=(0,i.aU)(n);(0,l.c7)(n)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>n,wC:()=>o});var a=s(2115),r=s(3004),i=s(6104),l=s(12);function n(){let[e,t]=(0,a.useState)(null),[s,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,r.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let c=async()=>{try{await (0,l.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=n();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function o(){let{user:e,loading:t}=n(),[s,r]=(0,a.useState)(!1),[i,l]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),l(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:s}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(2813)),_N_E=e.O()}]);