'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { getUsers, searchUsers, getAllUsers, getTotalUserCount, updateUser, deleteUser } from '@/lib/adminDataService'
import { processReferralBonus, grantQuickVideoAdvantage as grantQuickTranslationAdvantage, removeQuickVideoAdvantage as removeQuickTranslationAdvantage, updateUserPlanExpiry } from '@/lib/dataService'
import { downloadCSV, formatUsersForExport } from '@/lib/csvExport'
import Swal from 'sweetalert2'

interface User {
  id: string
  name: string
  email: string
  mobile: string
  referralCode: string
  referredBy: string
  plan: string
  planExpiry: Date | null
  activeDays: number
  totalTranslations: number
  todayTranslations: number
  wallet: number // Single wallet system
  joinedDate: Date
  status: string
  quickTranslationAdvantage?: boolean
  quickTranslationAdvantageExpiry?: Date | null
  quickTranslationAdvantageDays?: number
  quickTranslationAdvantageSeconds?: number
  quickTranslationAdvantageGrantedBy?: string
}

export default function AdminUsersPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [users, setUsers] = useState<User[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [totalUsers, setTotalUsers] = useState(0)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editData, setEditData] = useState({
    name: '',
    email: '',
    mobile: '',
    referralCode: '',
    referredBy: '',
    plan: '',
    activeDays: 0,
    totalTranslations: 0,
    todayTranslations: 0,
    wallet: 0,
    status: 'active',
    quickTranslationAdvantage: false,
    quickTranslationAdvantageDays: 7,
    quickTranslationAdvantageSeconds: 30
  })
  const [isSaving, setIsSaving] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [lastDoc, setLastDoc] = useState<any>(null)

  useEffect(() => {
    if (isAdmin) {
      loadUsers()
    }
  }, [isAdmin])

  const loadUsers = async (reset = true) => {
    try {
      setDataLoading(true)

      const result = await getUsers(50, reset ? null : lastDoc)

      if (reset) {
        setUsers(result.users as User[])
        setCurrentPage(1)
        // Get total user count when loading fresh data
        try {
          const count = await getTotalUserCount()
          setTotalUsers(count)
        } catch (countError) {
          console.error('Error getting total user count:', countError)
        }
      } else {
        setUsers(prev => [...prev, ...(result.users as User[])])
      }

      setLastDoc(result.lastDoc)
      setHasMore(result.hasMore)
    } catch (error) {
      console.error('Error loading users:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load users. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      loadUsers()
      return
    }

    try {
      setIsSearching(true)
      const searchResults = await searchUsers(searchTerm.trim())
      setUsers(searchResults as User[])
      setHasMore(false)
    } catch (error) {
      console.error('Error searching users:', error)
      Swal.fire({
        icon: 'error',
        title: 'Search Failed',
        text: 'Failed to search users. Please try again.',
      })
    } finally {
      setIsSearching(false)
    }
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setEditData({
      name: user.name,
      email: user.email,
      mobile: user.mobile,
      referralCode: user.referralCode,
      referredBy: user.referredBy,
      plan: user.plan,
      activeDays: user.activeDays,
      totalTranslations: user.totalTranslations,
      todayTranslations: user.todayTranslations,
      wallet: user.wallet || 0,
      status: user.status,
      quickTranslationAdvantage: user.quickTranslationAdvantage || false,
      quickTranslationAdvantageDays: user.quickTranslationAdvantageDays || 7,
      quickTranslationAdvantageSeconds: user.quickTranslationAdvantageSeconds || 30
    })
    setShowEditModal(true)
  }

  const handleSaveUser = async () => {
    if (!selectedUser) return

    try {
      setIsSaving(true)

      // Check if plan is being changed from Trial to paid plan
      const oldPlan = selectedUser.plan
      const newPlan = editData.plan
      const planChanged = oldPlan !== newPlan

      const updateData: any = {
        name: editData.name,
        email: editData.email,
        mobile: editData.mobile,
        referralCode: editData.referralCode,
        referredBy: editData.referredBy,
        plan: editData.plan,
        activeDays: editData.activeDays,
        totalTranslations: editData.totalTranslations,
        todayTranslations: editData.todayTranslations,
        wallet: editData.wallet,
        status: editData.status
      }

      await updateUser(selectedUser.id, updateData)

      // Handle quick translation advantage changes
      const currentHasAdvantage = selectedUser.quickTranslationAdvantage &&
        selectedUser.quickTranslationAdvantageExpiry &&
        new Date() < selectedUser.quickTranslationAdvantageExpiry

      if (editData.quickTranslationAdvantage && !currentHasAdvantage) {
        // Grant quick translation advantage
        await grantQuickTranslationAdvantage(
          selectedUser.id,
          editData.quickTranslationAdvantageDays,
          user?.email || 'admin',
          editData.quickTranslationAdvantageSeconds
        )
      } else if (!editData.quickTranslationAdvantage && currentHasAdvantage) {
        // Remove quick translation advantage
        await removeQuickTranslationAdvantage(selectedUser.id, user?.email || 'admin')
      } else if (editData.quickTranslationAdvantage && currentHasAdvantage) {
        // Update existing advantage (remove and re-grant with new settings)
        await removeQuickTranslationAdvantage(selectedUser.id, user?.email || 'admin')
        await grantQuickTranslationAdvantage(
          selectedUser.id,
          editData.quickTranslationAdvantageDays,
          user?.email || 'admin',
          editData.quickTranslationAdvantageSeconds
        )
      }

      // Update plan expiry if plan changed
      if (planChanged) {
        try {
          await updateUserPlanExpiry(selectedUser.id, newPlan)
          console.log(`Updated plan expiry for user ${selectedUser.id}: ${oldPlan} -> ${newPlan}`)
        } catch (expiryError) {
          console.error('Error updating plan expiry:', expiryError)
          // Continue with other operations even if expiry update fails
        }
      }

      // Process referral bonus if plan changed from Trial to paid plan
      if (planChanged && oldPlan === 'Trial' && newPlan !== 'Trial') {
        try {
          console.log(`Processing referral bonus for user ${selectedUser.id}: ${oldPlan} -> ${newPlan}`)
          await processReferralBonus(selectedUser.id, oldPlan, newPlan)

          // Show additional success message about referral bonus
          Swal.fire({
            icon: 'success',
            title: 'User Updated & Referral Bonus Processed',
            html: `
              <div class="text-left">
                <p><strong>User plan updated:</strong> ${oldPlan} → ${newPlan}</p>
                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>
              </div>
            `,
            timer: 4000,
            showConfirmButton: false
          })
        } catch (referralError) {
          console.error('Error processing referral bonus:', referralError)
          // Still show success for user update, but mention referral bonus issue
          Swal.fire({
            icon: 'warning',
            title: 'User Updated (Referral Bonus Issue)',
            html: `
              <div class="text-left">
                <p><strong>User plan updated successfully:</strong> ${oldPlan} → ${newPlan}</p>
                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>
                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>
              </div>
            `,
            timer: 5000,
            showConfirmButton: false
          })
        }
      } else {
        // Regular update success message
        let successMessage = 'User information has been updated successfully'

        // Add quick advantage info if changed
        if (editData.quickTranslationAdvantage && !currentHasAdvantage) {
          successMessage += `. Quick translation advantage granted for ${editData.quickTranslationAdvantageDays} days.`
        } else if (!editData.quickTranslationAdvantage && currentHasAdvantage) {
          successMessage += '. Quick translation advantage removed.'
        } else if (editData.quickTranslationAdvantage && currentHasAdvantage) {
          successMessage += `. Quick translation advantage updated for ${editData.quickTranslationAdvantageDays} days.`
        }

        Swal.fire({
          icon: 'success',
          title: 'User Updated',
          text: successMessage,
          timer: 3000,
          showConfirmButton: false
        })
      }

      // Update local state
      setUsers(prev => prev.map(user =>
        user.id === selectedUser.id
          ? {
              ...user,
              ...updateData,
              quickTranslationAdvantage: editData.quickTranslationAdvantage,
              quickTranslationAdvantageDays: editData.quickTranslationAdvantageDays,
              quickTranslationAdvantageSeconds: editData.quickTranslationAdvantageSeconds
            }
          : user
      ))

      setShowEditModal(false)
      setSelectedUser(null)

      // Reload users to get updated data
      await loadUsers()

    } catch (error) {
      console.error('Error updating user:', error)
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: 'Failed to update user. Please try again.',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDeleteUser = async (user: User) => {
    const result = await Swal.fire({
      icon: 'warning',
      title: 'Delete User',
      text: `Are you sure you want to delete ${user.name}? This action cannot be undone.`,
      showCancelButton: true,
      confirmButtonText: 'Yes, Delete',
      confirmButtonColor: '#dc2626',
      cancelButtonText: 'Cancel'
    })

    if (result.isConfirmed) {
      try {
        await deleteUser(user.id)
        setUsers(prev => prev.filter(u => u.id !== user.id))

        Swal.fire({
          icon: 'success',
          title: 'User Deleted',
          text: 'User has been deleted successfully',
          timer: 2000,
          showConfirmButton: false
        })
      } catch (error) {
        console.error('Error deleting user:', error)
        Swal.fire({
          icon: 'error',
          title: 'Delete Failed',
          text: 'Failed to delete user. Please try again.',
        })
      }
    }
  }



  const loadMoreUsers = () => {
    if (hasMore && !dataLoading) {
      loadUsers(false)
    }
  }

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0.00'
    }
    return `₹${amount.toFixed(2)}`
  }

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'Trial':
        return 'bg-gray-500'
      case 'Starter':
        return 'bg-blue-500'
      case 'Basic':
        return 'bg-green-500'
      case 'Premium':
        return 'bg-purple-500'
      case 'Gold':
        return 'bg-yellow-500'
      case 'Platinum':
        return 'bg-indigo-500'
      case 'Diamond':
        return 'bg-pink-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500'
      case 'inactive':
        return 'bg-red-500'
      case 'suspended':
        return 'bg-yellow-500'
      default:
        return 'bg-gray-500'
    }
  }

  const handleExportUsers = async () => {
    try {
      // Show loading state
      Swal.fire({
        title: 'Exporting Users...',
        text: 'Please wait while we prepare your export file.',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading()
        }
      })

      // Get all users for export (not just the currently displayed ones)
      const allUsers = await getAllUsers()

      if (allUsers.length === 0) {
        Swal.fire({
          icon: 'warning',
          title: 'No Data',
          text: 'No users to export.',
        })
        return
      }

      const exportData = formatUsersForExport(allUsers)
      downloadCSV(exportData, 'users')

      Swal.fire({
        icon: 'success',
        title: 'Export Complete',
        text: `Exported ${allUsers.length} users to CSV file.`,
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error exporting users:', error)
      Swal.fire({
        icon: 'error',
        title: 'Export Failed',
        text: 'Failed to export users. Please try again.',
      })
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <Link href="/admin" className="text-gray-600 hover:text-gray-800">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            {totalUsers > 0 && (
              <p className="text-sm text-gray-600">
                {searchTerm ? `Showing ${users.length} of ${totalUsers} users` : `Total: ${totalUsers} users`}
              </p>
            )}
          </div>

          <div className="flex gap-2">
            <Link
              href="/admin/upload-users"
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center"
            >
              <i className="fas fa-upload mr-2"></i>
              Upload Users
            </Link>
            <button
              onClick={handleExportUsers}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              <i className="fas fa-download mr-2"></i>
              Export CSV
            </button>
            <button
              onClick={() => loadUsers()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Search Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex gap-4">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search by name, email, mobile, or referral code..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            onClick={handleSearch}
            disabled={isSearching}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isSearching ? (
              <>
                <div className="spinner mr-2 w-4 h-4"></div>
                Searching...
              </>
            ) : (
              <>
                <i className="fas fa-search mr-2"></i>
                Search
              </>
            )}
          </button>
          {searchTerm && (
            <button
              onClick={() => {
                setSearchTerm('')
                loadUsers()
              }}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>
      </div>

      {/* Users Table */}
      <div className="p-6">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Translations
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quick Copy-Paste
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wallet
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dataLoading && users.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center">
                      <div className="spinner mx-auto"></div>
                      <p className="mt-2 text-gray-500">Loading users...</p>
                    </td>
                  </tr>
                ) : users.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No users found
                    </td>
                  </tr>
                ) : (
                  users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="text-sm text-gray-500">
                            Joined: {user.joinedDate instanceof Date ? user.joinedDate.toLocaleDateString() : new Date(user.joinedDate).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{user.mobile}</div>
                        <div className="text-sm text-gray-500">Code: {user.referralCode}</div>
                        {user.referredBy && (
                          <div className="text-sm text-gray-500">Ref: {user.referredBy}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full text-white ${getPlanBadgeColor(user.plan)}`}>
                          {user.plan}
                        </span>
                        <div className="text-sm text-gray-500 mt-1">
                          Days: {user.activeDays}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">Total: {user.totalTranslations}</div>
                        <div className="text-sm text-gray-500">Today: {user.todayTranslations}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {user.quickTranslationAdvantage && user.quickTranslationAdvantageExpiry && new Date() < user.quickTranslationAdvantageExpiry ? (
                          <div>
                            <span className="px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500">
                              Enabled
                            </span>
                            <div className="text-xs text-gray-500 mt-1">
                              Until: {user.quickTranslationAdvantageExpiry instanceof Date ? user.quickTranslationAdvantageExpiry.toLocaleDateString() : new Date(user.quickTranslationAdvantageExpiry).toLocaleDateString()}
                            </div>
                          </div>
                        ) : (
                          <span className="px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500">
                            Disabled
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <i className="fas fa-wallet mr-1 text-green-500"></i>
                          {formatCurrency(user.wallet || 0)}
                        </div>
                        <div className="text-xs text-gray-500">
                          Total Balance
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full text-white ${getStatusBadgeColor(user.status)}`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditUser(user)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Edit User"
                          >
                            <i className="fas fa-edit"></i>
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete User"
                          >
                            <i className="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Load More Button */}
          {hasMore && !dataLoading && users.length > 0 && (
            <div className="px-6 py-4 border-t border-gray-200 text-center">
              <button
                onClick={loadMoreUsers}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
              >
                <i className="fas fa-chevron-down mr-2"></i>
                Load More Users
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Edit User Modal */}
      {showEditModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable">
          <div className="bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl">
            <div className="p-6 border-b border-gray-200 flex-shrink-0">
              <h3 className="text-lg font-bold text-gray-900">Edit User</h3>
            </div>

            <div className="p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  value={editData.name}
                  onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={editData.email}
                  onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mobile</label>
                <input
                  type="text"
                  value={editData.mobile}
                  onChange={(e) => setEditData(prev => ({ ...prev, mobile: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Referral Code</label>
                  <input
                    type="text"
                    value={editData.referralCode}
                    onChange={(e) => setEditData(prev => ({ ...prev, referralCode: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Referred By</label>
                  <input
                    type="text"
                    value={editData.referredBy}
                    onChange={(e) => setEditData(prev => ({ ...prev, referredBy: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                <select
                  value={editData.plan}
                  onChange={(e) => setEditData(prev => ({ ...prev, plan: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Trial">Trial</option>
                  <option value="Junior">Junior</option>
                  <option value="Senior">Senior</option>
                  <option value="Expert">Expert</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Active Days</label>
                <input
                  type="number"
                  value={editData.activeDays}
                  onChange={(e) => setEditData(prev => ({ ...prev, activeDays: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Translations</label>
                  <input
                    type="number"
                    value={editData.totalTranslations}
                    onChange={(e) => setEditData(prev => ({ ...prev, totalTranslations: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Today Translations</label>
                  <input
                    type="number"
                    value={editData.todayTranslations}
                    onChange={(e) => setEditData(prev => ({ ...prev, todayTranslations: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Wallet Balance (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editData.wallet}
                    onChange={(e) => setEditData(prev => ({ ...prev, wallet: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={editData.status}
                  onChange={(e) => setEditData(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                </select>
              </div>

              {/* Quick Translation Copy-Paste Advantage Section */}
              <div className="border-t border-gray-200 pt-4">
                <h4 className="text-md font-semibold text-gray-900 mb-3">
                  <i className="fas fa-copy mr-2 text-yellow-500"></i>
                  Quick Translation Copy-Paste Advantage
                </h4>
                <p className="text-xs text-gray-600 mb-3">
                  When enabled, user can copy-paste English text instead of typing manually
                </p>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="quickTranslationAdvantage"
                      checked={editData.quickTranslationAdvantage}
                      onChange={(e) => setEditData(prev => ({ ...prev, quickTranslationAdvantage: e.target.checked }))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="quickTranslationAdvantage" className="ml-2 block text-sm text-gray-700">
                      Enable Copy-Paste for English Text
                    </label>
                  </div>

                  {editData.quickTranslationAdvantage && (
                    <div className="grid grid-cols-2 gap-4 ml-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Days</label>
                        <input
                          type="number"
                          min="1"
                          max="365"
                          value={editData.quickTranslationAdvantageDays}
                          onChange={(e) => setEditData(prev => ({ ...prev, quickTranslationAdvantageDays: parseInt(e.target.value) || 7 }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Translation Duration</label>
                        <select
                          value={editData.quickTranslationAdvantageSeconds}
                          onChange={(e) => setEditData(prev => ({ ...prev, quickTranslationAdvantageSeconds: parseInt(e.target.value) }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={1}>1 second</option>
                          <option value={10}>10 seconds</option>
                          <option value={30}>30 seconds</option>
                        </select>
                      </div>
                    </div>
                  )}

                  {/* Current Status Display */}
                  {selectedUser && (
                    <div className="ml-6 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">
                        <strong>Current Status:</strong>{' '}
                        {selectedUser.quickTranslationAdvantage &&
                         selectedUser.quickTranslationAdvantageExpiry &&
                         new Date() < selectedUser.quickTranslationAdvantageExpiry ? (
                          <span className="text-green-600">
                            Copy-paste enabled until {selectedUser.quickTranslationAdvantageExpiry instanceof Date ? selectedUser.quickTranslationAdvantageExpiry.toLocaleDateString() : new Date(selectedUser.quickTranslationAdvantageExpiry).toLocaleDateString()}
                          </span>
                        ) : (
                          <span className="text-gray-500">Copy-paste disabled - manual typing required</span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex-shrink-0">
              <div className="flex gap-4">
              <button
                onClick={handleSaveUser}
                disabled={isSaving}
                className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
              <button
                onClick={() => setShowEditModal(false)}
                className="flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700"
              >
                Cancel
              </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
