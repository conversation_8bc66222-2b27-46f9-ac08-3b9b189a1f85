(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7122],{12:(e,a,t)=>{"use strict";t.d(a,{M4:()=>o,_f:()=>l});var r=t(6104),s=t(4752),n=t.n(s);function i(e){try{Object.keys(localStorage).forEach(a=>{(a.includes(e)||a.startsWith("video_session_")||a.startsWith("watch_times_")||a.startsWith("video_refresh_")||a.startsWith("video_change_notification_")||a.startsWith("leave_")||a.includes("mytube_")||a.includes("user_"))&&localStorage.removeItem(a)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await n().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),n().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=a}),!0;return!1}catch(e){return console.error("Logout error:",e),n().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await r.j2.signOut(),window.location.href=a}catch(e){console.error("Quick logout error:",e),window.location.href=a}}},2740:(e,a,t)=>{Promise.resolve().then(t.bind(t,3356))},3356:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var r=t(5155),s=t(2115),n=t(6874),i=t.n(n),l=t(6681),o=t(4752),d=t.n(o);function c(){let{user:e,loading:a,isAdmin:t}=(0,l.wC)(),[n,o]=(0,s.useState)({appName:"MyTube",appVersion:"1.0.0",maintenanceMode:!1,registrationEnabled:!0,minWithdrawalAmount:50,maxWithdrawalAmount:5e4,withdrawalProcessingTime:"24-48 hours",supportEmail:"<EMAIL>",supportPhone:"+917676636990",defaultVideoDuration:300,maxDailyVideos:50,referralBonus:50,videoEarningRates:{trial:10,starter:25,basic:75,premium:150,gold:200,platinum:250,diamond:400}}),[c,u]=(0,s.useState)(!0),[m,g]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t&&x()},[t]);let x=async()=>{try{u(!0),u(!1)}catch(e){console.error("Error loading settings:",e),u(!1)}},h=async()=>{try{if(g(!0),n.minWithdrawalAmount>=n.maxWithdrawalAmount)throw Error("Minimum withdrawal amount must be less than maximum withdrawal amount");let e=[1,10,30].includes(n.defaultVideoDuration),a=n.defaultVideoDuration>=60&&n.defaultVideoDuration<=420;if(!e&&!a)throw Error("Default video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");d().fire({icon:"success",title:"Settings Saved",text:"System settings have been updated successfully.",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error saving settings:",e),d().fire({icon:"error",title:"Save Failed",text:e.message||"Failed to save settings. Please try again."})}finally{g(!1)}},p=(e,a)=>{o(t=>({...t,[e]:a}))},b=(e,a)=>{o(t=>({...t,videoEarningRates:{...t.videoEarningRates,[e]:a}}))};return a||c?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading settings..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"System Settings"})]}),(0,r.jsx)("button",{onClick:h,disabled:m,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-save mr-2"}),"Save Settings"]})})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"General Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"App Name"}),(0,r.jsx)("input",{type:"text",value:n.appName,onChange:e=>p("appName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"App Version"}),(0,r.jsx)("input",{type:"text",value:n.appVersion,onChange:e=>p("appVersion",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Support Email"}),(0,r.jsx)("input",{type:"email",value:n.supportEmail,onChange:e=>p("supportEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Support Phone (WhatsApp)",(0,r.jsx)("i",{className:"fab fa-whatsapp text-green-500 ml-1"})]}),(0,r.jsx)("input",{type:"text",value:n.supportPhone,onChange:e=>p("supportPhone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"+917676636990"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"WhatsApp number for customer support"})]})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"maintenanceMode",checked:n.maintenanceMode,onChange:e=>p("maintenanceMode",e.target.checked),className:"mr-2"}),(0,r.jsx)("label",{htmlFor:"maintenanceMode",className:"text-sm font-medium text-gray-700",children:"Maintenance Mode (Disable app access for users)"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"registrationEnabled",checked:n.registrationEnabled,onChange:e=>p("registrationEnabled",e.target.checked),className:"mr-2"}),(0,r.jsx)("label",{htmlFor:"registrationEnabled",className:"text-sm font-medium text-gray-700",children:"Enable New User Registration"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Video Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Default Video Duration"}),(0,r.jsxs)("select",{value:n.defaultVideoDuration,onChange:e=>p("defaultVideoDuration",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]}),(0,r.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,r.jsx)("option",{value:60,children:"1 minute"}),(0,r.jsx)("option",{value:120,children:"2 minutes"}),(0,r.jsx)("option",{value:180,children:"3 minutes"}),(0,r.jsx)("option",{value:240,children:"4 minutes"}),(0,r.jsx)("option",{value:300,children:"5 minutes"}),(0,r.jsx)("option",{value:360,children:"6 minutes"}),(0,r.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Default duration for new users: ",n.defaultVideoDuration<60?"".concat(n.defaultVideoDuration," second").concat(n.defaultVideoDuration>1?"s":""):"".concat(Math.round(n.defaultVideoDuration/60)," minute").concat(Math.round(n.defaultVideoDuration/60)>1?"s":"")]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Daily Videos"}),(0,r.jsx)("input",{type:"number",min:"1",max:"100",value:n.maxDailyVideos,onChange:e=>p("maxDailyVideos",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Video Earning Rates (₹ per video)"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Trial Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.trial,onChange:e=>b("trial",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Starter Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.starter,onChange:e=>b("starter",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Basic Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.basic,onChange:e=>b("basic",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Premium Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.premium,onChange:e=>b("premium",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Gold Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.gold,onChange:e=>b("gold",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Platinum Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.platinum,onChange:e=>b("platinum",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Diamond Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.diamond,onChange:e=>b("diamond",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Withdrawal Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Minimum Withdrawal Amount (₹)"}),(0,r.jsx)("input",{type:"number",min:"1",value:n.minWithdrawalAmount,onChange:e=>p("minWithdrawalAmount",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Maximum Withdrawal Amount (₹)"}),(0,r.jsx)("input",{type:"number",min:"1",value:n.maxWithdrawalAmount,onChange:e=>p("maxWithdrawalAmount",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Processing Time"}),(0,r.jsx)("input",{type:"text",value:n.withdrawalProcessingTime,onChange:e=>p("withdrawalProcessingTime",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Referral Settings"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Bonus (₹)"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.referralBonus,onChange:e=>p("referralBonus",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Amount given to referrer when someone joins using their code"})]})})]})]})]})}},6104:(e,a,t)=>{"use strict";t.d(a,{db:()=>d,j2:()=>o});var r=t(3915),s=t(3004),n=t(5317),i=t(858);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),o=(0,s.xI)(l),d=(0,n.aU)(l);(0,i.c7)(l)},6681:(e,a,t)=>{"use strict";t.d(a,{Nu:()=>o,hD:()=>l,wC:()=>d});var r=t(2115),s=t(3004),n=t(6104),i=t(12);function l(){let[e,a]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,s.hg)(n.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),a(e),l(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),l(!1)}},[]);let o=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:o}}function o(){let{user:e,loading:a}=l();return(0,r.useEffect)(()=>{a||e||(window.location.href="/login")},[e,a]),{user:e,loading:a}}function d(){let{user:e,loading:a}=l(),[t,s]=(0,r.useState)(!1),[n,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!a&&!e){window.location.href="/admin/login";return}if(e){let a=["<EMAIL>","<EMAIL>"].includes(e.email||"");s(a),i(!1),a||(window.location.href="/login")}},[e,a]),{user:e,loading:a||n,isAdmin:t}}}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,8441,1684,7358],()=>a(2740)),_N_E=e.O()}]);