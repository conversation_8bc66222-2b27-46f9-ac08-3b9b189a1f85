(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4246,9567],{1927:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var n=a(5155),o=a(2115),s=a(6874),r=a.n(s),l=a(6681),i=a(7460),c=a(6572),d=a(3592),u=a(9567);let h={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},g=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}];function m(){var e=parseInt(localStorage.getItem(h.CURRENT_BATCH)||"0");let t=function(e){try{let t=localStorage.getItem("".concat(h.BATCH_PREFIX).concat(e));if(!t)return null;let a=JSON.parse(t);if(Date.now()-a.lastUpdated>864e5)return localStorage.removeItem("".concat(h.BATCH_PREFIX).concat(e)),null;return a}catch(t){return console.error("Error loading translation batch ".concat(e,":"),t),null}}(e);return t?t.translations:[]}async function f(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error("Failed to load translations: ".concat(e.statusText));let t=await e.json();console.log("Raw translation data loaded:",t.length,"entries");let a=[];return Array.isArray(t)&&t.forEach((e,t)=>{e.english&&a.push({id:"translation_".concat(t,"_").concat(Date.now()),english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian,category:"General",batchIndex:Math.floor(a.length/100)})}),a}catch(e){throw console.error("Error loading translations from file:",e),e}}async function p(){try{if(!function(){let e=localStorage.getItem(h.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached translation data..."),m();{console.log("Loading fresh translation data...");let e=await f();return!function(e){let t=Math.ceil(e.length/100);for(let n=0;n<t;n++){let t=100*n,o=Math.min(t+100,e.length),s=e.slice(t,o);var a=n;try{let e={batchNumber:a,translations:s,totalTranslations:s.length,lastUpdated:Date.now()};localStorage.setItem("".concat(h.BATCH_PREFIX).concat(a),JSON.stringify(e))}catch(e){console.error("Error saving translation batch ".concat(a,":"),e)}}localStorage.setItem(h.TOTAL_TRANSLATIONS,e.length.toString()),localStorage.setItem(h.CURRENT_BATCH,"0"),localStorage.setItem(h.LAST_PROCESSED,Date.now().toString()),console.log("Saved ".concat(e.length," translations in ").concat(t," batches"))}(e),m()}}catch(t){console.error("Error initializing translation system:",t);let e=m();if(e.length>0)return console.log("Using cached translations as fallback"),e;throw t}}var x=a(8647),w=a(4752),b=a.n(w);function v(){var e;let{user:t,loading:a}=(0,l.Nu)(),{hasBlockingNotifications:s,isChecking:h,markAllAsRead:m}=(0,i.J)((null==t?void 0:t.uid)||null),{isBlocked:f,leaveStatus:w}=(0,c.l)({userId:(null==t?void 0:t.uid)||null,checkInterval:3e4,enabled:!!t}),[v,y]=(0,o.useState)(null),[D,k]=(0,o.useState)(0),[N,S]=(0,o.useState)(0),[j,E]=(0,o.useState)(50),[_,T]=(0,o.useState)(""),[C,A]=(0,o.useState)("hindi"),[L,M]=(0,o.useState)(0),[I,P]=(0,o.useState)(!1),[B,R]=(0,o.useState)(!1),[O,U]=(0,o.useState)([]),[H,F]=(0,o.useState)(!0),[W]=(0,o.useState)([{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"chinese",name:"Chinese",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"}]),[G,V]=(0,o.useState)({earningPerBatch:10,plan:"Trial"}),[Y,z]=(0,o.useState)(null),[J,X]=(0,o.useState)(0),[q,K]=(0,o.useState)(0);(0,o.useEffect)(()=>{t&&Q()},[t]),(0,o.useEffect)(()=>{P(L>=50)},[L]);let Q=async()=>{try{console.log("\uD83D\uDD0D Checking work access for user:",t.uid);let e=await (0,d.isUserPlanExpired)(t.uid);if(console.log("\uD83D\uDCC5 Plan status result:",e),e.expired){console.log("\uD83D\uDEAB Work access blocked - Plan expired:",e.reason),b().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">'.concat(e.reason,'</p>\n              <p class="text-sm text-gray-600">\n                Active Days: ').concat(e.activeDays||0," | Days Left: ").concat(e.daysLeft||0,"\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let a=await (0,d.getVideoCountData)(t.uid);if(console.log("\uD83D\uDCCA Translation data check:",a),a.todayVideos>=50){console.log("\uD83D\uDEAB Work access blocked - Daily session completed"),b().fire({icon:"info",title:"Daily Session Completed",html:'\n            <div class="text-center">\n              <p class="mb-3">You have already completed your daily session of 50 translations!</p>\n              <p class="text-sm text-gray-600">\n                Translations completed today: '.concat(a.todayVideos,'/50\n              </p>\n              <p class="text-sm text-green-600 mt-2">\n                Come back tomorrow for your next session.\n              </p>\n            </div>\n          '),confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"});return}let n=await (0,u.q8)(t.uid);if(console.log("\uD83D\uDCCA Work status result:",n),n.blocked){console.log("\uD83D\uDEAB Work access blocked:",n.reason),b().fire({icon:"warning",title:"Work Not Available",text:n.reason||"Work is currently blocked.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}console.log("✅ Work access allowed, proceeding with normal loading"),Z(),$(),ee(),ea(),et()}catch(e){console.error("❌ Error checking work access (allowing work to proceed):",e),Z(),$(),ee(),ea(),et()}},Z=async()=>{try{console.log("\uD83D\uDCCA Loading translation data for user:",t.uid);let e=await (0,d.getVideoCountData)(t.uid);console.log("\uD83D\uDCCA Translation data loaded:",e),k(e.todayVideos),S(e.totalVideos)}catch(e){console.error("Error loading translation data:",e)}},$=async()=>{try{let e=await (0,d.Q6)(t.uid);V({earningPerBatch:e.earningPerBatch,plan:e.plan})}catch(e){console.error("Error loading translation settings:",e)}},ee=async()=>{try{let e=await (0,d.getUserData)(t.uid);if(z(e),e){try{await (0,d.iF)(t.uid)}catch(e){console.error("Error updating active days:",e)}let a=await (0,d.isUserPlanExpired)(t.uid);X(a.daysLeft||0),K(a.activeDays||0),console.log("\uD83D\uDCCA Plan status loaded:",{plan:e.plan,expired:a.expired,daysLeft:a.daysLeft,activeDays:a.activeDays,reason:a.reason})}}catch(e){console.error("Error loading user data:",e)}},et=()=>{let e=new Date().toDateString(),a="translation_session_".concat(t.uid,"_").concat(e),n=localStorage.getItem(a);if(n){let e=parseInt(n);M(e),E(Math.max(0,50-e))}else E(50)},ea=async()=>{try{F(!0);let e=(await p()).map(e=>({english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,japanese:e.japanese,korean:e.korean,chinese:e.chinese,arabic:e.arabic,dutch:e.dutch,swedish:e.swedish,norwegian:e.norwegian}));if(U(e),e.length>0){let t=Math.floor(Math.random()*e.length);y(e[t]);let a=function(){let e=Math.floor(Math.random()*g.length);return g[e].code}();A(a)}}catch(e){console.error("Error loading translations:",e),b().fire({icon:"error",title:"Loading Error",text:"Failed to load translation data. Please refresh the page."})}finally{F(!1)}},en=async()=>{if(I&&!B&&!(L<50)){if(f)return void b().fire({icon:"warning",title:"Submission Not Available",text:w.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{R(!0);let e=G.earningPerBatch;for(let e=0;e<50;e++)await (0,d.yx)(t.uid);await (0,d.updateWalletBalance)(t.uid,e),await (0,d.addTransaction)(t.uid,{type:"translation_earning",amount:e,description:"Batch completion reward - 50 translations completed"});let a=Math.min(D+50,50);k(a),S(N+50),E(0);let n=new Date().toDateString(),o="translation_session_".concat(t.uid,"_").concat(n);localStorage.removeItem(o),M(0),P(!1),T(""),b().fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:'\n          <div class="text-center">\n            <p class="text-lg font-bold text-green-600 mb-2">₹'.concat(e,' Earned!</p>\n            <p class="mb-2">50 translations completed and submitted</p>\n            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>\n            <p class="text-sm text-blue-600 font-semibold">\n              \uD83C\uDF89 Your daily session is complete! Come back tomorrow for your next session.\n            </p>\n          </div>\n        '),confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),b().fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{R(!1)}}};return a||H||h?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"spinner mb-4"}),(0,n.jsx)("p",{className:"text-white",children:a?"Loading...":h?"Checking notifications...":"Loading translations..."})]})}):s&&t?(0,n.jsx)(x.A,{userId:t.uid,onAllRead:m}):(0,n.jsxs)("div",{className:"min-h-screen p-4",children:[(0,n.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)(r(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,n.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,n.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,n.jsxs)("div",{className:"text-white text-right",children:[(0,n.jsxs)("p",{className:"text-sm",children:["Plan: ",G.plan]}),(0,n.jsxs)("p",{className:"text-sm",children:["₹",G.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:J}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-blue-400",children:D}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Translations"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-green-400",children:N}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Total Translations"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-lg font-bold text-purple-400",children:j}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Translations Left"})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,n.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[q,"/","Trial"===G.plan?"2":"30"]}),(0,n.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,n.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,n.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,n.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to get new translation",children:[(0,n.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"New Translation"]})]}),v&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,n.jsx)("i",{className:"fas fa-flag mr-2"}),"English Text:"]}),(0,n.jsx)("p",{className:"text-white text-lg bg-white/5 p-3 rounded border-l-4 border-blue-400",children:v.english})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,n.jsx)("i",{className:"fas fa-globe mr-2"}),"Translate to:"]}),(0,n.jsx)("select",{value:C,onChange:e=>A(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:W.map(e=>(0,n.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))})]}),(0,n.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,n.jsx)("i",{className:"fas fa-edit mr-2"}),"Your Translation:"]}),(0,n.jsx)("textarea",{value:_,onChange:e=>T(e.target.value),placeholder:"Enter your ".concat(null==(e=W.find(e=>e.code===C))?void 0:e.name," translation here..."),className:"w-full h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none"})]}),(0,n.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,n.jsxs)("button",{onClick:()=>{if(!_.trim()||L>=50)return;if(f)return void b().fire({icon:"warning",title:"Work Suspended",text:w.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});let e=L+1;M(e),E(Math.max(0,50-e));let a=new Date().toDateString(),n="translation_session_".concat(t.uid,"_").concat(a);if(localStorage.setItem(n,e.toString()),O.length>0){let e=Math.floor(Math.random()*O.length);y(O[e]);let t=Math.floor(Math.random()*W.length);A(W[t].code)}T(""),e<50?b().fire({icon:"success",title:"Translation Completed!",text:"Progress: ".concat(e,"/50 translations completed. ").concat(50-e," more to go!"),timer:2e3,showConfirmButton:!1}):b().fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:!_.trim()||L>=50,className:"px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat(!_.trim()||L>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"),children:[(0,n.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Translation"]}),I&&(0,n.jsxs)("button",{onClick:en,disabled:B,className:"btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105",children:[(0,n.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit & Earn ₹",G.earningPerBatch]})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsxs)("p",{className:"text-white/80",children:["Progress: ",L,"/50 translations completed"]}),(0,n.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,n.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:"".concat(L/50*100,"%")}})})]})]})]})]})}},6572:(e,t,a)=>{"use strict";a.d(t,{l:()=>s});var n=a(2115),o=a(9567);function s(e){let{userId:t,checkInterval:a=3e4,enabled:s=!0}=e,[r,l]=(0,n.useState)({blocked:!1,lastChecked:new Date}),[i,c]=(0,n.useState)(!1),d=(0,n.useCallback)(async()=>{if(t&&s)try{c(!0);let e=await (0,o.q8)(t);return l({blocked:e.blocked,reason:e.reason,lastChecked:new Date}),e}catch(e){return console.error("Error checking leave status:",e),l(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{c(!1)}},[t,s]);return(0,n.useEffect)(()=>{t&&s&&d()},[t,s,d]),(0,n.useEffect)(()=>{if(!t||!s||a<=0)return;let e=setInterval(()=>{d()},a);return()=>clearInterval(e)},[t,s,a,d]),{leaveStatus:r,isChecking:i,checkLeaveStatus:d,isBlocked:r.blocked}}},8763:(e,t,a)=>{Promise.resolve().then(a.bind(a,1927))},9567:(e,t,a)=>{"use strict";a.d(t,{applyUserLeave:()=>d,calculateActiveDays:()=>f,cancelUserLeave:()=>h,createAdminLeave:()=>r,deleteAdminLeave:()=>i,getAdminLeaves:()=>l,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>g,isAdminLeaveDay:()=>c,isUserOnLeave:()=>m,q8:()=>p});var n=a(6104),o=a(5317);let s={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function r(e){try{return(await (0,o.gS)((0,o.collection)(n.db,s.adminLeaves),{...e,date:o.Dc.fromDate(e.date),createdAt:o.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function l(){try{let e=(0,o.P)((0,o.collection)(n.db,s.adminLeaves),(0,o.My)("date","asc")),t=(await (0,o.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function i(e){try{await (0,o.kd)((0,o.H9)(n.db,s.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function c(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let r=(0,o.P)((0,o.collection)(n.db,s.adminLeaves),(0,o._M)("date",">=",o.Dc.fromDate(t)),(0,o._M)("date","<=",o.Dc.fromDate(a))),l=await (0,o.getDocs)(r),i=!l.empty;return i?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),i}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,a,r,l=new Date,i=l.getFullYear(),c=l.getMonth()+1,d=await g(e.userId,i,c),u="pending";return d<4&&(u="approved",t="system",r=o.Dc.now(),a="Auto-approved: ".concat(d+1,"/").concat(4," monthly leaves used")),{id:(await (0,o.gS)((0,o.collection)(n.db,s.userLeaves),{...e,date:o.Dc.fromDate(e.date),status:u,appliedAt:o.Dc.now(),...t&&{reviewedBy:t},...r&&{reviewedAt:r},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,o.P)((0,o.collection)(n.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o.My)("date","desc"));return(await (0,o.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(t=e.data().reviewedAt)?void 0:t.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function h(e){try{await (0,o.kd)((0,o.H9)(n.db,s.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function g(e,t,a){try{let r=new Date(t,a-1,1),l=new Date(t,a,0,23,59,59,999),i=(0,o.P)((0,o.collection)(n.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(r)),(0,o._M)("date","<=",o.Dc.fromDate(l)));return(await (0,o.getDocs)(i)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function m(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let r=new Date(t);r.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",r.toISOString());let l=(0,o.P)((0,o.collection)(n.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(a)),(0,o._M)("date","<=",o.Dc.fromDate(r))),i=await (0,o.getDocs)(l),c=!i.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function f(e,t){let a=new Date,r=Math.floor((a.getTime()-t.getTime())/864e5);try{let l=(0,o.P)((0,o.collection)(n.db,s.adminLeaves),(0,o._M)("date",">=",o.Dc.fromDate(t)),(0,o._M)("date","<=",o.Dc.fromDate(a))),i=(await (0,o.getDocs)(l)).size,c=(0,o.P)((0,o.collection)(n.db,s.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(t)),(0,o._M)("date","<=",o.Dc.fromDate(a))),d=(await (0,o.getDocs)(c)).size;return Math.max(0,r-i-d)}catch(e){return console.error("Error calculating active days:",e),Math.max(0,r)}}async function p(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await c(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await m(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,1018,8441,1684,7358],()=>t(8763)),_N_E=e.O()}]);