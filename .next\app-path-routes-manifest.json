{"/_not-found/page": "/_not-found", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/setup/page": "/admin/setup", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/transactions/page": "/admin/transactions", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/page": "/", "/plans/page": "/plans", "/profile/page": "/profile", "/refer/page": "/refer", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/wallet/page": "/wallet", "/transactions/page": "/transactions", "/work/page": "/work"}