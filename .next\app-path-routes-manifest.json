{"/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/transactions/page": "/admin/transactions", "/admin/upload-users/page": "/admin/upload-users", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-registration/page": "/debug-registration", "/forgot-password/page": "/forgot-password", "/login/page": "/login", "/page": "/", "/plans/page": "/plans", "/profile/page": "/profile", "/refer/page": "/refer", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/admin/users/page": "/admin/users", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-simple-registration/page": "/test-simple-registration", "/test-translations/page": "/test-translations", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work", "/reset-password/page": "/reset-password", "/admin/fix-active-days/page": "/admin/fix-active-days"}