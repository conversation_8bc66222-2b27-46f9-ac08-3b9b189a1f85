{"/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/upload-users/page": "/admin/upload-users", "/admin/transactions/page": "/admin/transactions", "/dashboard/page": "/dashboard", "/admin/users/page": "/admin/users", "/admin/setup/page": "/admin/setup", "/clear-cache/page": "/clear-cache", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration/page": "/debug-registration", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/page": "/", "/login/page": "/login", "/plans/page": "/plans", "/profile/page": "/profile", "/register/page": "/register", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firestore/page": "/test-firestore", "/test-firebase/page": "/test-firebase", "/admin/login/page": "/admin/login", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/test-reg-simple/page": "/test-reg-simple", "/admin/withdrawals/page": "/admin/withdrawals", "/test-videos/page": "/test-videos", "/work/page": "/work"}