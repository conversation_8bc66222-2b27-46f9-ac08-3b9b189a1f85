'use client'

import React from 'react'
import Link from 'next/link'
import InstallApp from '@/components/InstallApp'
import Image from 'next/image'

export default function HomePage() {
  return (
    <main className="min-h-screen">
      {/* Minimal Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Image
              src="/img/instra-logo.svg"
              alt="Instra Global Logo"
              width={32}
              height={32}
              className="mr-2"
            />
            <span className="text-white text-xl font-bold">Instra Global</span>
          </div>
          <div className="flex space-x-4">
            <Link href="#pricing" className="nav-link">
              <i className="fas fa-crown mr-2"></i>
              Plans
            </Link>
            <Link href="/login" className="nav-link">
              <i className="fas fa-sign-in-alt mr-2"></i>
              Login
            </Link>
          </div>
        </div>
      </nav>



      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center px-4">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-8">
            <div className="flex items-center justify-center mb-6">
              <Image
                src="/img/instra-logo.svg"
                alt="Instra Global Logo"
                width={60}
                height={60}
                className="mr-4"
              />
              <span className="text-4xl font-bold text-white">Instra Global</span>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold mb-6 gradient-text">
              Translate Text & Earn Money
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Translate text and earn up to ₹30,000 per month. Start your journey to financial freedom today by completing simple translation tasks!
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="feature-card">
              <i className="fas fa-language text-4xl text-instra-purple mb-4"></i>
              <h3 className="text-xl font-semibold text-white mb-2">Multiple Languages</h3>
              <p className="text-white/80">Translate to various languages daily</p>
            </div>
            <div className="feature-card">
              <i className="fas fa-money-bill-wave text-4xl text-green-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-white mb-2">Instant Earnings</h3>
              <p className="text-white/80">Get paid for every translation completed</p>
            </div>
            <div className="feature-card">
              <i className="fas fa-bolt text-4xl text-yellow-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-white mb-2">Fast & Simple</h3>
              <p className="text-white/80">Easy translation process</p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="space-y-4">
            <Link href="/login" className="btn-primary inline-flex items-center text-lg px-8 py-4">
              <i className="fas fa-rocket mr-3"></i>
              Start Earning Now
            </Link>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
              <Link href="/how-it-works" className="btn-secondary inline-flex items-center">
                <i className="fas fa-info-circle mr-2"></i>
                How It Works
              </Link>
              <Link href="/faq" className="btn-secondary inline-flex items-center">
                <i className="fas fa-question-circle mr-2"></i>
                FAQ
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Choose Your Earning Plan
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Start with our free trial or upgrade to premium plans for higher earnings. Translate text and earn money with flexible pricing options.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Trial Plan */}
            <div className="glass-card p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-2">Trial</h3>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-white">Free</span>
                  <span className="text-white/60 ml-2">/ 2 days</span>
                </div>
                <p className="text-green-400 font-semibold text-sm">
                  Earn ₹25 per 50 translations
                </p>
              </div>
              <div className="text-xs text-white/60 mb-4 text-center">
                Explore and test your translation potential before handling real jobs
              </div>
              <ul className="space-y-2 mb-6 text-sm">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  2 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  ₹25 per 50 translations
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  Basic support
                </li>
              </ul>
              <Link href="/register" className="w-full btn-secondary block text-center text-sm py-2">
                Start Free Trial
              </Link>
            </div>

            {/* Junior Plan */}
            <div className="glass-card p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-2">Junior</h3>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-white">₹499</span>
                  <span className="text-white/60 ml-2">/ 30 days</span>
                </div>
                <p className="text-green-400 font-semibold text-sm">
                  Earn ₹150 per 50 translations
                </p>
              </div>
              <div className="text-xs text-white/60 mb-4 text-center">
                Entry-level role for new freelancers starting their translation journey
              </div>
              <ul className="space-y-2 mb-6 text-sm">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  30 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  Certified for 1 Language
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  ₹150 per 50 translations
                </li>
              </ul>
              <Link href="/plans" className="w-full btn-primary block text-center text-sm py-2">
                Choose Junior
              </Link>
            </div>

            {/* Senior Plan - Most Popular */}
            <div className="glass-card p-6 relative ring-2 ring-yellow-400">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold">
                  Most Popular
                </span>
              </div>
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-2">Senior</h3>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-white">₹1,499</span>
                  <span className="text-white/60 ml-2">/ 30 days</span>
                </div>
                <p className="text-green-400 font-semibold text-sm">
                  Earn ₹250 per 50 translations
                </p>
              </div>
              <div className="text-xs text-white/60 mb-4 text-center">
                Senior role for experienced translators handling multiple Languages
              </div>
              <ul className="space-y-2 mb-6 text-sm">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  30 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  Certified for 3 Languages
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  ₹250 per 50 translations
                </li>
              </ul>
              <Link href="/plans" className="w-full bg-yellow-400 text-black py-2 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-300 block text-center text-sm">
                Choose Senior
              </Link>
            </div>

            {/* Expert Plan */}
            <div className="glass-card p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-2">Expert</h3>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-white">₹2,999</span>
                  <span className="text-white/60 ml-2">/ 30 days</span>
                </div>
                <p className="text-green-400 font-semibold text-sm">
                  Earn ₹400 per 50 translations
                </p>
              </div>
              <div className="text-xs text-white/60 mb-4 text-center">
                Top-tier role for expert translators with broad multi-language proficiency
              </div>
              <ul className="space-y-2 mb-6 text-sm">
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  30 days access
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  Certified for 5 Languages
                </li>
                <li className="flex items-center text-white/80">
                  <i className="fas fa-check text-green-400 mr-2 text-xs"></i>
                  ₹400 per 50 translations
                </li>
              </ul>
              <Link href="/plans" className="w-full btn-primary block text-center text-sm py-2">
                Choose Expert
              </Link>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/plans" className="btn-secondary inline-flex items-center">
              <i className="fas fa-crown mr-2"></i>
              View All Plans
            </Link>
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Need Help?
          </h2>
          <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto">
            Our support team is here to help you get started and answer any questions about earning with Instra Global.
          </p>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <a
              href="https://wa.me/917676636990"
              target="_blank"
              rel="noopener noreferrer"
              className="glass-card p-8 hover:scale-105 transition-transform"
            >
              <i className="fab fa-whatsapp text-5xl text-green-400 mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">WhatsApp Support</h3>
              <p className="text-white/80 mb-4">Get instant help via WhatsApp</p>
              <p className="text-green-400 font-semibold">+91 7676636990</p>
              <p className="text-white/60 text-sm mt-2">9 AM - 6 PM (Working days)</p>
            </a>

            <a
              href="mailto:<EMAIL>"
              className="glass-card p-8 hover:scale-105 transition-transform"
            >
              <i className="fas fa-envelope text-5xl text-blue-400 mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-2">Email Support</h3>
              <p className="text-white/80 mb-4">Send us detailed queries</p>
              <p className="text-blue-400 font-semibold"><EMAIL></p>
              <p className="text-white/60 text-sm mt-2">9 AM - 6 PM (Working days)</p>
            </a>

            <InstallApp variant="homepage" />
          </div>
        </div>
      </section>
    </main>
  )
}
