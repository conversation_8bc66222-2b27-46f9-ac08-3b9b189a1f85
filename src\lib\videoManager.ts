// Video Manager for handling large video datasets with local storage batching

export interface VideoData {
  id: string
  title: string
  url: string
  embedUrl: string
  duration: number
  category?: string
  batchIndex?: number
}

export interface VideoBatch {
  batchNumber: number
  videos: VideoData[]
  totalVideos: number
  lastUpdated: number
}

const STORAGE_KEYS = {
  CURRENT_BATCH: 'instra_current_batch',
  BATCH_PREFIX: 'instra_batch_',
  VIDEO_INDEX: 'instra_video_index',
  TOTAL_VIDEOS: 'instra_total_videos',
  LAST_PROCESSED: 'instra_last_processed'
}

const BATCH_SIZE = 100
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

// Extract video ID from YouTube URL
function extractVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/
  ]
  
  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match) return match[1]
  }
  return null
}

// Convert YouTube watch URL to embed URL
function getEmbedUrl(watchUrl: string): string {
  const videoId = extractVideoId(watchUrl)
  return videoId ? `https://www.youtube.com/embed/${videoId}` : watchUrl
}

// Generate video title from URL (fallback)
function generateTitle(url: string, index: number): string {
  return `Video ${index + 1}`
}

// Process raw video data from Mytube.json
export function processVideoData(rawData: Record<string, string> | Array<Record<string, string>>): VideoData[] {
  const processedVideos: VideoData[] = []

  // Handle both object and array formats
  if (Array.isArray(rawData)) {
    // Array format: [{"url1": "url2"}, {"url3": "url4"}, ...]
    rawData.forEach((item, index) => {
      Object.entries(item).forEach(([key, value]) => {
        // Use the value (second URL) as the main video URL
        const videoId = extractVideoId(value)
        if (videoId) {
          processedVideos.push({
            id: `video_${processedVideos.length}_${videoId}`,
            title: generateTitle(value, processedVideos.length),
            url: value,
            embedUrl: getEmbedUrl(value),
            duration: 300, // Default 5 minutes
            category: 'General',
            batchIndex: Math.floor(processedVideos.length / BATCH_SIZE)
          })
        }
      })
    })
  } else {
    // Object format: {"url1": "url2", "url3": "url4", ...}
    Object.entries(rawData).forEach(([key, value], index) => {
      // Use the value (second URL) as the main video URL
      const videoId = extractVideoId(value)
      if (videoId) {
        processedVideos.push({
          id: `video_${processedVideos.length}_${videoId}`,
          title: generateTitle(value, processedVideos.length),
          url: value,
          embedUrl: getEmbedUrl(value),
          duration: 300, // Default 5 minutes
          category: 'General',
          batchIndex: Math.floor(processedVideos.length / BATCH_SIZE)
        })
      }
    })
  }

  return processedVideos
}

// Save video batch to localStorage
function saveBatchToStorage(batchNumber: number, videos: VideoData[]): void {
  try {
    const batch: VideoBatch = {
      batchNumber,
      videos,
      totalVideos: videos.length,
      lastUpdated: Date.now()
    }
    
    localStorage.setItem(
      `${STORAGE_KEYS.BATCH_PREFIX}${batchNumber}`,
      JSON.stringify(batch)
    )
  } catch (error) {
    console.error(`Error saving batch ${batchNumber}:`, error)
  }
}

// Load video batch from localStorage
function loadBatchFromStorage(batchNumber: number): VideoBatch | null {
  try {
    const stored = localStorage.getItem(`${STORAGE_KEYS.BATCH_PREFIX}${batchNumber}`)
    if (!stored) return null
    
    const batch: VideoBatch = JSON.parse(stored)
    
    // Check if cache is still valid
    if (Date.now() - batch.lastUpdated > CACHE_DURATION) {
      localStorage.removeItem(`${STORAGE_KEYS.BATCH_PREFIX}${batchNumber}`)
      return null
    }
    
    return batch
  } catch (error) {
    console.error(`Error loading batch ${batchNumber}:`, error)
    return null
  }
}

// Split videos into batches and save to localStorage
export function saveVideosToBatches(videos: VideoData[]): void {
  const totalBatches = Math.ceil(videos.length / BATCH_SIZE)
  
  for (let i = 0; i < totalBatches; i++) {
    const startIndex = i * BATCH_SIZE
    const endIndex = Math.min(startIndex + BATCH_SIZE, videos.length)
    const batchVideos = videos.slice(startIndex, endIndex)
    
    saveBatchToStorage(i, batchVideos)
  }
  
  // Save metadata
  localStorage.setItem(STORAGE_KEYS.TOTAL_VIDEOS, videos.length.toString())
  localStorage.setItem(STORAGE_KEYS.CURRENT_BATCH, '0')
  localStorage.setItem(STORAGE_KEYS.LAST_PROCESSED, Date.now().toString())
  
  console.log(`Saved ${videos.length} videos in ${totalBatches} batches`)
}

// Load videos from a specific batch
export function loadVideoBatch(batchNumber: number): VideoData[] {
  const batch = loadBatchFromStorage(batchNumber)
  return batch ? batch.videos : []
}

// Get current batch videos
export function getCurrentBatchVideos(): VideoData[] {
  const currentBatch = parseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_BATCH) || '0')
  return loadVideoBatch(currentBatch)
}

// Move to next batch
export function moveToNextBatch(): VideoData[] {
  const currentBatch = parseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_BATCH) || '0')
  const totalVideos = parseInt(localStorage.getItem(STORAGE_KEYS.TOTAL_VIDEOS) || '0')
  const totalBatches = Math.ceil(totalVideos / BATCH_SIZE)
  
  const nextBatch = (currentBatch + 1) % totalBatches
  localStorage.setItem(STORAGE_KEYS.CURRENT_BATCH, nextBatch.toString())
  
  return loadVideoBatch(nextBatch)
}

// Get video statistics
export function getVideoStats(): {
  totalVideos: number
  currentBatch: number
  totalBatches: number
  videosInCurrentBatch: number
} {
  const totalVideos = parseInt(localStorage.getItem(STORAGE_KEYS.TOTAL_VIDEOS) || '0')
  const currentBatch = parseInt(localStorage.getItem(STORAGE_KEYS.CURRENT_BATCH) || '0')
  const totalBatches = Math.ceil(totalVideos / BATCH_SIZE)
  const currentBatchVideos = loadVideoBatch(currentBatch)
  
  return {
    totalVideos,
    currentBatch,
    totalBatches,
    videosInCurrentBatch: currentBatchVideos.length
  }
}

// Clear all video data from localStorage
export function clearVideoStorage(): void {
  const keys = Object.keys(localStorage)
  keys.forEach(key => {
    if (key.startsWith(STORAGE_KEYS.BATCH_PREFIX) || 
        Object.values(STORAGE_KEYS).includes(key)) {
      localStorage.removeItem(key)
    }
  })
  console.log('Cleared all video storage')
}

// Check if videos need to be reloaded
export function shouldReloadVideos(): boolean {
  const lastProcessed = localStorage.getItem(STORAGE_KEYS.LAST_PROCESSED)
  if (!lastProcessed) return true
  
  const timeSinceLastUpdate = Date.now() - parseInt(lastProcessed)
  return timeSinceLastUpdate > CACHE_DURATION
}

// Load videos from the Instradata.json file
export async function loadVideosFromFile(): Promise<VideoData[]> {
  try {
    const response = await fetch('/Instradata.json')
    if (!response.ok) {
      throw new Error(`Failed to load videos: ${response.statusText}`)
    }

    const rawData = await response.json()
    console.log('Raw video data loaded:', Object.keys(rawData).length, 'entries')

    // Process the data directly (it's already in the correct format)
    return processVideoData(rawData)
  } catch (error) {
    console.error('Error loading videos from file:', error)
    throw error
  }
}

// Initialize video system
export async function initializeVideoSystem(): Promise<VideoData[]> {
  try {
    // Check if we need to reload videos
    if (shouldReloadVideos()) {
      console.log('Loading fresh video data...')
      const videos = await loadVideosFromFile()
      saveVideosToBatches(videos)
      return getCurrentBatchVideos()
    } else {
      console.log('Using cached video data...')
      return getCurrentBatchVideos()
    }
  } catch (error) {
    console.error('Error initializing video system:', error)
    // Fallback to cached data if available
    const cachedVideos = getCurrentBatchVideos()
    if (cachedVideos.length > 0) {
      console.log('Using cached videos as fallback')
      return cachedVideos
    }
    throw error
  }
}
