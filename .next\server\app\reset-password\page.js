(()=>{var e={};e.id=4700,e.ids=[4700],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11451:(e,t,r)=>{Promise.resolve().then(r.bind(r,90322))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),o=r(63385),i=r(75535),a=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,o.xI)(n),c=(0,i.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{M4:()=>n,_f:()=>a});var s=r(33784),o=r(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function a(e,t="/login"){try{if((await o.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await s.j2.signOut(),o.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),o.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e,t="/login"){try{e&&i(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85316:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\reset-password\\page.tsx","default")},85348:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),o=r(48088),i=r(88170),a=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85316)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\reset-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>a,hD:()=>i,wC:()=>n});var s=r(43210);r(63385),r(33784);var o=r(51278);function i(){let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0),a=async()=>{try{await (0,o.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:a}}function a(){let{user:e,loading:t}=i();return{user:e,loading:t}}function n(){let{user:e,loading:t}=i(),[r,o]=(0,s.useState)(!1),[a,n]=(0,s.useState)(!0);return{user:e,loading:t||a,isAdmin:r}}},90322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),o=r(43210),i=r(85814),a=r.n(i),n=r(30474),l=r(63385),c=r(33784),d=r(87979),u=r(77567);function p(){let{user:e,loading:t}=(0,d.hD)(),[r,i]=(0,o.useState)(""),[p,h]=(0,o.useState)(""),[x,m]=(0,o.useState)(""),[f,w]=(0,o.useState)(""),[g,b]=(0,o.useState)(!1),[v,j]=(0,o.useState)(!0),[y,S]=(0,o.useState)(!1),[N,q]=(0,o.useState)(!1),[P,k]=(0,o.useState)(!1),[C,_]=(0,o.useState)(!1),D=async e=>{if(e.preventDefault(),!x.trim())return void u.A.fire({icon:"error",title:"Password Required",text:"Please enter a new password"});if(x.length<6)return void u.A.fire({icon:"error",title:"Password Too Short",text:"Password must be at least 6 characters long"});if(x!==f)return void u.A.fire({icon:"error",title:"Passwords Don't Match",text:"Please make sure both passwords match"});b(!0);try{await (0,l.R4)(c.j2,r,x),_(!0),u.A.fire({icon:"success",title:"Password Reset Successful!",text:"Your password has been updated successfully. You can now login with your new password.",confirmButtonText:"Go to Login",confirmButtonColor:"#3b82f6"}).then(()=>{window.location.href="/login"})}catch(t){console.error("Password reset error:",t);let e="An error occurred while resetting your password";switch(t.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/weak-password":e="Password is too weak. Please choose a stronger password.";break;default:e=t.message||"Failed to reset password"}u.A.fire({icon:"error",title:"Reset Failed",text:e})}finally{b(!1)}};return t||v?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:v?"Verifying reset link...":"Loading..."})]})}):y?(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(a(),{href:"/",className:"inline-block",children:(0,s.jsx)(n.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Set New Password"}),(0,s.jsxs)("p",{className:"text-white/80",children:["Enter your new password for ",(0,s.jsx)("span",{className:"font-semibold text-blue-400",children:p})]})]}),(0,s.jsxs)("form",{onSubmit:D,className:"glass-card p-8 space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newPassword",className:"block text-white font-medium mb-2",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,s.jsx)("input",{type:N?"text":"password",id:"newPassword",value:x,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter new password",disabled:g}),(0,s.jsx)("button",{type:"button",onClick:()=>q(!N),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,s.jsx)("i",{className:`fas ${N?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,s.jsx)("input",{type:P?"text":"password",id:"confirmPassword",value:f,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Confirm new password",disabled:g}),(0,s.jsx)("button",{type:"button",onClick:()=>k(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,s.jsx)("i",{className:`fas ${P?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:g,className:"w-full btn-primary flex items-center justify-center",children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Updating Password..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-check mr-2"}),"Update Password"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)(a(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]})})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"fas fa-times text-red-400 text-2xl"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Invalid Reset Link"}),(0,s.jsx)("p",{className:"text-white/80 mb-6",children:"This password reset link is invalid or has expired."}),(0,s.jsx)(a(),{href:"/forgot-password",className:"btn-primary",children:"Request New Reset Link"})]})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97379:(e,t,r)=>{Promise.resolve().then(r.bind(r,85316))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,5901],()=>r(85348));module.exports=s})();