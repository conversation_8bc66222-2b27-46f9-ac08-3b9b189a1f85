(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{12:(e,t,r)=>{"use strict";r.d(t,{M4:()=>n,_f:()=>i});var s=r(6104),o=r(4752),a=r.n(o);function l(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await a().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e),await s.j2.signOut(),a().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&l(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},getImageProps:function(){return i}});let s=r(8229),o=r(8883),a=r(3063),l=s._(r(1193));function i(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let n=a.Image},3587:(e,t,r)=>{Promise.resolve().then(r.bind(r,6616))},6104:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>n});var s=r(3915),o=r(3004),a=r(5317),l=r(858);let i=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),n=(0,o.xI)(i),c=(0,a.aU)(i);(0,l.c7)(i)},6616:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(5155),o=r(2115),a=r(6874),l=r.n(a),i=r(6766),n=r(3004),c=r(5317),d=r(6104),u=r(6681),m=r(3592),f=r(4752),h=r.n(f);function g(){let{user:e,loading:t}=(0,u.hD)(),[r,a]=(0,o.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[f,g]=(0,o.useState)(!1),[w,p]=(0,o.useState)(!1),[b,x]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("ref");e&&a(t=>({...t,referralCode:e}))},[]);let y=e=>{let{name:t,value:r}=e.target;a(e=>({...e,[t]:r}))},N=()=>{let{name:e,email:t,mobile:s,password:o,confirmPassword:a}=r;if(!e||!t||!s||!o||!a)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(s))throw Error("Please enter a valid 10-digit mobile number");if(o.length<6)throw Error("Password must be at least 6 characters long");if(o!==a)throw Error("Passwords do not match")},v=async e=>{e.preventDefault();try{N(),g(!0),console.log("Creating user with email and password...");let e=(await (0,n.eJ)(d.j2,r.email,r.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let t=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),o="MY".concat(t).concat(s);console.log("Generated referral code:",o);let a={[m.FIELD_NAMES.name]:r.name.trim(),[m.FIELD_NAMES.email]:r.email.toLowerCase(),[m.FIELD_NAMES.mobile]:r.mobile,[m.FIELD_NAMES.referralCode]:o,[m.FIELD_NAMES.referredBy]:r.referralCode||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:1,[m.FIELD_NAMES.joinedDate]:c.Dc.now(),[m.FIELD_NAMES.wallet]:0,[m.FIELD_NAMES.totalVideos]:0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.videoDuration]:30,status:"active"};console.log("Creating user document with data:",a),console.log("User UID:",e.uid),console.log("Collection:",m.COLLECTIONS.users),console.log("Document path:","".concat(m.COLLECTIONS.users,"/").concat(e.uid)),console.log("Creating user document in Firestore...");let l=(0,c.H9)(d.db,m.COLLECTIONS.users,e.uid);console.log("Document reference created:",l.path),console.log("About to create document with data:",JSON.stringify(a,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",l.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,c.BN)(l,a),console.log("✅ User document created successfully");let t=await (0,c.x7)(l);if(t.exists())console.log("✅ Document verification successful:",t.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error("Failed to create user profile: ".concat(e.message,". Your account was created but profile setup failed. Please contact support."))}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),h().fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to MyTube!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(t){console.error("Registration error:",t),console.error("Error code:",t.code),console.error("Error message:",t.message),console.error("Full error object:",JSON.stringify(t,null,2));let e="An error occurred during registration";if(t.message.includes("fill in all"))e=t.message;else if(t.message.includes("Name must be"))e=t.message;else if(t.message.includes("valid email"))e=t.message;else if(t.message.includes("valid 10-digit"))e=t.message;else if(t.message.includes("Password must be"))e=t.message;else if(t.message.includes("Passwords do not match"))e=t.message;else if(t.message.includes("email address is already registered"))e=t.message;else if(t.message.includes("mobile number is already registered"))e=t.message;else switch(t.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=t.message||"Registration failed"}h().fire({icon:"error",title:"Registration Failed",text:e})}finally{g(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8",children:(0,s.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(i.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,s.jsx)("p",{className:"text-white/80",children:"Join Instra Global and start earning today"})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:r.name,onChange:y,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:r.email,onChange:y,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,s.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:r.mobile,onChange:y,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:w?"text":"password",id:"password",name:"password",value:r.password,onChange:y,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>p(!w),className:"password-toggle-btn","aria-label":w?"Hide password":"Show password",children:(0,s.jsx)("i",{className:"fas ".concat(w?"fa-eye-slash":"fa-eye")})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:b?"text":"password",id:"confirmPassword",name:"confirmPassword",value:r.confirmPassword,onChange:y,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!b),className:"password-toggle-btn","aria-label":b?"Hide confirm password":"Show confirm password",children:(0,s.jsx)("i",{className:"fas ".concat(b?"fa-eye-slash":"fa-eye")})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,s.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:r.referralCode,onChange:y,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,s.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center mt-6",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(l(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6681:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>n,hD:()=>i,wC:()=>c});var s=r(2115),o=r(3004),a=r(6104),l=r(12);function i(){let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0);(0,s.useEffect)(()=>{try{let e=(0,o.hg)(a.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let n=async()=>{try{await (0,l.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:n}}function n(){let{user:e,loading:t}=i();return(0,s.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=i(),[r,o]=(0,s.useState)(!1),[a,l]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");o(t),l(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||a,isAdmin:r}}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var s=r(1469),o=r.n(s)}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,3592,8441,1684,7358],()=>t(3587)),_N_E=e.O()}]);