(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9479],{454:(e,t,a)=>{Promise.resolve().then(a.bind(a,8133))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var n=a(3915),s=a(3004),c=a(5317),r=a(858);let i=(0,n.Dk)().length?(0,n.Sx)():(0,n.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),o=(0,s.xI)(i),l=(0,c.aU)(i);(0,r.c7)(i)},8133:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var n=a(5155),s=a(2115),c=a(6104),r=a(3004),i=a(5317);function o(){let[e,t]=(0,s.useState)(""),[a,o]=(0,s.useState)(!1),[l,d]=(0,s.useState)(""),[u,m]=(0,s.useState)(!1),b=async()=>{o(!0),t("Testing Firebase connection...\n");try{t(e=>e+"Firebase config loaded ✓\n"),t(e=>e+"Project ID: ".concat("instra-global","\n"));let e="test".concat(Date.now(),"@example.com");t(e=>e+"Creating test user...\n");let a=(await (0,r.eJ)(c.j2,e,"test123456")).user;t(e=>e+"User created with UID: ".concat(a.uid," ✓\n")),t(e=>e+"User email: ".concat(a.email,"\n")),t(e=>e+"Testing Firestore write...\n");let n={name:"Test User",email:e,mobile:"1234567890",referralCode:"TEST123",referredBy:null,plan:"trial",planExpiry:null,activeDays:2,joinedDate:new Date,wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,createdAt:new Date,test:!0};t(e=>e+"Writing to collection: users, document: ".concat(a.uid,"\n")),await (0,i.BN)((0,i.H9)(c.db,"users",a.uid),n),t(e=>e+"Firestore write successful ✓\n"),t(e=>e+"Testing Firestore read...\n");let s=await (0,i.x7)((0,i.H9)(c.db,"users",a.uid));if(s.exists()){t(e=>e+"Firestore read successful ✓\n");let e=s.data();t(e=>e+"Document exists: ".concat(s.exists(),"\n")),t(t=>t+"Data keys: ".concat(Object.keys(e).join(", "),"\n")),t(t=>t+"Name: ".concat(e.name,"\n")),t(t=>t+"Email: ".concat(e.email,"\n")),t(t=>t+"Wallet: ".concat(e.wallet,"\n"))}else t(e=>e+"Document not found ✗\n");t(e=>e+"Testing transaction creation...\n");let o={userId:a.uid,type:"test",amount:10,description:"Test transaction",date:new Date,status:"completed"},l=(0,i.H9)(c.db,"transactions","test_".concat(a.uid,"_").concat(Date.now()));await (0,i.BN)(l,o),t(e=>e+"Transaction creation successful ✓\n"),t(e=>e+"Cleaning up test data...\n"),await a.delete(),t(e=>e+"Test user deleted ✓\n"),t(e=>e+"\n\uD83C\uDF89 All tests passed! Firebase is working correctly.")}catch(e){console.error("Firebase test error:",e),t(t=>t+"\n❌ Error: ".concat(e.message,"\n")),t(t=>t+"Error code: ".concat(e.code,"\n")),e.code&&t(t=>t+"Error details: ".concat(e.code,"\n")),t(t=>t+"Stack trace: ".concat(e.stack,"\n"))}finally{o(!1)}},g=async()=>{m(!0),d("Testing registration flow...\n");try{let e="regtest".concat(Date.now(),"@example.com");d(t=>t+"Creating user with email: ".concat(e,"\n"));let t=(await (0,r.eJ)(c.j2,e,"test123456")).user;d(e=>e+"✓ User account created: ".concat(t.uid,"\n"));let a={name:"Test Registration User",email:e,mobile:"**********",referralCode:"TEST".concat(Math.random().toString(36).substr(2,4).toUpperCase()),referredBy:null,plan:"trial",planExpiry:null,activeDays:2,joinedDate:new Date,wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null};d(e=>e+"Creating user document in Firestore...\n"),await (0,i.BN)((0,i.H9)(c.db,"users",t.uid),a),d(e=>e+"✓ User document created successfully\n"),d(e=>e+"Verifying document creation...\n");let n=await (0,i.x7)((0,i.H9)(c.db,"users",t.uid));if(n.exists()){let e=n.data();d(e=>e+"✓ Document verification successful\n"),d(t=>t+"  Name: ".concat(e.name,"\n")),d(t=>t+"  Email: ".concat(e.email,"\n")),d(t=>t+"  Wallet: ".concat(e.wallet,"\n")),d(t=>t+"  Plan: ".concat(e.plan,"\n"))}else d(e=>e+"✗ Document not found after creation\n");d(e=>e+"Cleaning up...\n"),await t.delete(),d(e=>e+"✓ Test user deleted\n"),d(e=>e+"\n\uD83C\uDF89 Registration flow test completed successfully!")}catch(e){console.error("Registration test error:",e),d(t=>t+"\n❌ Registration Error: ".concat(e.message,"\n")),d(t=>t+"Error code: ".concat(e.code,"\n")),e.stack&&d(t=>t+"Stack: ".concat(e.stack,"\n"))}finally{m(!1)}};return(0,n.jsx)("div",{className:"min-h-screen p-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Firebase Connection Test"}),(0,n.jsxs)("div",{className:"flex gap-4 mb-6",children:[(0,n.jsx)("button",{onClick:b,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Firebase Connection"}),(0,n.jsx)("button",{onClick:g,disabled:u,className:"btn-secondary",children:u?"Testing...":"Test Registration Flow"})]}),(0,n.jsxs)("div",{className:"glass-card p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Test Results:"}),(0,n.jsx)("pre",{className:"text-white/80 whitespace-pre-wrap font-mono text-sm",children:e||"Click the button above to test Firebase connection"})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,8441,1684,7358],()=>t(454)),_N_E=e.O()}]);