(()=>{var e={};e.id=4831,e.ids=[4831],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1506:(e,r,t)=>{Promise.resolve().then(t.bind(t,55795))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>c,j2:()=>l});var s=t(67989),i=t(63385),n=t(75535),o=t(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,i.xI)(a),c=(0,n.aU)(a);(0,o.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55795:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),i=t(43210),n=t(63385),o=t(75535),a=t(33784),l=t(3582);function c(){let[e,r]=(0,i.useState)(""),[t,c]=(0,i.useState)(!1),u=async()=>{r(""),c(!0);try{r(e=>e+"Testing referral code generation...\n");let e=await (0,l.pu)();r(r=>r+`✓ Referral code generated: ${e}
`);for(let e=0;e<3;e++){let t=await (0,l.pu)();r(r=>r+`✓ Code ${e+2}: ${t}
`)}}catch(e){r(r=>r+`✗ Error: ${e.message}
`),console.error("Test error:",e)}finally{c(!1)}},d=async()=>{r(""),c(!0);try{r(e=>e+"Testing Firestore write...\n");let e={name:"Test User",email:"<EMAIL>",mobile:"9876543210",referralCode:"TEST001",referredBy:"",plan:"Trial",planExpiry:null,activeDays:2,joinedDate:o.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,status:"active"},t=`test_${Date.now()}`,s=(0,o.H9)(a.db,l.COLLECTIONS.users,t);r(e=>e+`Creating document: ${s.path}
`),await (0,o.BN)(s,e),r(e=>e+"✓ Document created successfully\n");let i=await (0,o.x7)(s);i.exists()?(r(e=>e+"✓ Document verified successfully\n"),r(e=>e+`Data: ${JSON.stringify(i.data(),null,2)}
`)):r(e=>e+"✗ Document not found after creation\n")}catch(e){r(r=>r+`✗ Error: ${e.message}
`),console.error("Test error:",e)}finally{c(!1)}},p=async()=>{r(""),c(!0);try{r(e=>e+"Testing full registration flow...\n");let e=`test_${Date.now()}@example.com`;r(r=>r+`Creating auth user: ${e}
`);let t=(await (0,n.eJ)(a.j2,e,"test123456")).user;r(e=>e+`✓ Auth user created: ${t.uid}
`),r(e=>e+"Generating referral code...\n");let s=await (0,l.pu)();r(e=>e+`✓ Referral code: ${s}
`);let i={[l.FIELD_NAMES.name]:"Test Registration User",[l.FIELD_NAMES.email]:e,[l.FIELD_NAMES.mobile]:"9876543210",[l.FIELD_NAMES.referralCode]:s,[l.FIELD_NAMES.referredBy]:"",[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:1,[l.FIELD_NAMES.joinedDate]:o.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalTranslations]:0,[l.FIELD_NAMES.todayTranslations]:0,[l.FIELD_NAMES.lastTranslationDate]:null,status:"active"};r(e=>e+"Creating Firestore document...\n");let c=(0,o.H9)(a.db,l.COLLECTIONS.users,t.uid);await (0,o.BN)(c,i),r(e=>e+"✓ Firestore document created\n"),(await (0,o.x7)(c)).exists()?(r(e=>e+"✓ Document verified successfully\n"),r(e=>e+`✓ Full registration test completed successfully!
`)):r(e=>e+"✗ Document verification failed\n"),r(e=>e+"Cleaning up test user...\n"),await t.delete(),r(e=>e+"✓ Test user deleted\n")}catch(e){r(r=>r+`✗ Error: ${e.message}
`),console.error("Test error:",e)}finally{c(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Registration Debug Tests"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:u,disabled:t,className:"btn-primary",children:"Test Referral Code Generation"}),(0,s.jsx)("button",{onClick:d,disabled:t,className:"btn-primary",children:"Test Firestore Write"}),(0,s.jsx)("button",{onClick:p,disabled:t,className:"btn-primary",children:"Test Full Registration"})]}),t&&(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"spinner mr-2"}),(0,s.jsx)("span",{className:"text-white",children:"Running test..."})]}),(0,s.jsx)("div",{className:"bg-black/50 rounded-lg p-4 min-h-[200px]",children:(0,s.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono",children:e||"Click a test button to start..."})})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Debug Information"}),(0,s.jsxs)("div",{className:"text-white/80 space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Firebase Project:"})," ","instra-global"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Auth Domain:"})," ","instra-global.firebaseapp.com"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Collections.users:"})," ",l.COLLECTIONS.users]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Field Names:"})," ",JSON.stringify(l.FIELD_NAMES,null,2)]})]})]})]})})}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58148:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=t(65239),i=t(48088),n=t(88170),o=t.n(n),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let c={children:["",{children:["test-registration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,88433)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-registration\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-registration\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-registration/page",pathname:"/test-registration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},61338:(e,r,t)=>{Promise.resolve().then(t.bind(t,88433))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88433:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-registration\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-registration\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6204,2756,5901,3582],()=>t(58148));module.exports=s})();