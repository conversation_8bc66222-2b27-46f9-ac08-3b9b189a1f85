'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRequireAdmin } from '@/hooks/useAuth'
import { getAdminDashboardStats } from '@/lib/adminDataService'
import { handleUserLogout } from '@/lib/authUtils'

interface DashboardStats {
  totalUsers: number
  totalTranslations: number
  totalEarnings: number
  pendingWithdrawals: number
  todayUsers: number
  todayTranslations: number
  todayEarnings: number
  todayWithdrawals: number
}

export default function AdminDashboardPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [statsLoading, setStatsLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(false)

  useEffect(() => {
    if (isAdmin) {
      loadDashboardStats()
    }
  }, [isAdmin])

  const loadDashboardStats = async () => {
    try {
      setStatsLoading(true)
      const dashboardStats = await getAdminDashboardStats()
      setStats(dashboardStats)
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  const handleLogout = () => {
    handleUserLogout(user?.uid, '/admin/login')
  }

  if (loading || statsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Sidebar */}
      <aside className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-center h-16 bg-gray-900">
          <Image
            src="/img/instra-logo.svg"
            alt="Instra Global Logo"
            width={32}
            height={32}
            className="mr-2"
          />
          <span className="text-white text-xl font-bold">Instra Global Admin</span>
        </div>
        
        <nav className="mt-8">
          <div className="px-4 space-y-2">
            <Link
              href="/admin"
              className="flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg"
            >
              <i className="fas fa-tachometer-alt mr-3"></i>
              Dashboard
            </Link>
            <Link
              href="/admin/users"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-users mr-3"></i>
              Users
            </Link>
            <Link
              href="/admin/simple-upload"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-file-csv mr-3"></i>
              Simple Upload
            </Link>
            <Link
              href="/admin/transactions"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-exchange-alt mr-3"></i>
              Transactions
            </Link>
            <Link
              href="/admin/withdrawals"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-money-bill-wave mr-3"></i>
              Withdrawals
            </Link>
            <Link
              href="/admin/notifications"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-bell mr-3"></i>
              Notifications
            </Link>
            <Link
              href="/admin/leaves"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-calendar-times mr-3"></i>
              Leave Management
            </Link>
            <Link
              href="/admin/settings"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-cog mr-3"></i>
              Settings
            </Link>
            <Link
              href="/admin/fix-active-days"
              className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <i className="fas fa-tools mr-3"></i>
              Fix Active Days
            </Link>
          </div>
        </nav>

        <div className="absolute bottom-4 left-4 right-4">
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
          >
            <i className="fas fa-sign-out-alt mr-3"></i>
            Logout
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="lg:ml-64">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-6 py-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-bars text-xl"></i>
            </button>
            
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">Welcome, Admin</span>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <i className="fas fa-user-shield text-gray-600"></i>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6">
          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <i className="fas fa-users text-blue-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.totalUsers?.toLocaleString() || '0'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <i className="fas fa-language text-green-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Translations</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.totalTranslations?.toLocaleString() || '0'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <i className="fas fa-rupee-sign text-yellow-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ₹{stats?.totalEarnings?.toLocaleString() || '0'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <i className="fas fa-clock text-red-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Withdrawals</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.pendingWithdrawals || '0'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Today's Stats */}
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Today's Activity</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <p className="text-3xl font-bold text-blue-600">
                    {stats?.todayUsers || '0'}
                  </p>
                  <p className="text-gray-600">New Users</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-green-600">
                    {stats?.todayTranslations?.toLocaleString() || '0'}
                  </p>
                  <p className="text-gray-600">Translations Completed</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-yellow-600">
                    ₹{stats?.todayEarnings?.toLocaleString() || '0'}
                  </p>
                  <p className="text-gray-600">Earnings Paid</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-red-600">
                    {stats?.todayWithdrawals || '0'}
                  </p>
                  <p className="text-gray-600">Withdrawals</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link
              href="/admin/users"
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <i className="fas fa-users text-blue-600 text-2xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Manage Users</h3>
                  <p className="text-gray-600">View and manage user accounts</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/withdrawals"
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <i className="fas fa-money-bill-wave text-green-600 text-2xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Process Withdrawals</h3>
                  <p className="text-gray-600">Review and approve withdrawals</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/notifications"
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <i className="fas fa-bell text-yellow-600 text-2xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Send Notifications</h3>
                  <p className="text-gray-600">Notify users about updates</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/settings"
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <i className="fas fa-cog text-purple-600 text-2xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">System Settings</h3>
                  <p className="text-gray-600">Configure platform settings</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/fix-active-days"
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className="p-3 bg-red-100 rounded-lg">
                  <i className="fas fa-tools text-red-600 text-2xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Fix Active Days</h3>
                  <p className="text-gray-600">Fix daily counts and active days</p>
                </div>
              </div>
            </Link>

            <Link
              href="/admin/simple-upload"
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <i className="fas fa-file-csv text-green-600 text-2xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Simple Upload</h3>
                  <p className="text-gray-600">Update translations, wallet & active days via CSV</p>
                </div>
              </div>
            </Link>
          </div>
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  )
}
