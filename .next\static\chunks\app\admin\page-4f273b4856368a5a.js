(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698,6779],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>o,_f:()=>n});var a=s(6104),r=s(4752),l=s.n(r);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await l().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await a.j2.signOut(),l().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),l().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return n}});let a=s(8229),r=s(8883),l=s(3063),i=a._(s(1193));function n(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=l.Image},5487:(e,t,s)=>{Promise.resolve().then(s.bind(s,7220))},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>d,j2:()=>o});var a=s(3915),r=s(3004),l=s(5317),i=s(858);let n=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),o=(0,r.xI)(n),d=(0,l.aU)(n);(0,i.c7)(n)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>o,hD:()=>n,wC:()=>d});var a=s(2115),r=s(3004),l=s(6104),i=s(12);function n(){let[e,t]=(0,a.useState)(null),[s,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,r.hg)(l.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let o=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:o}}function o(){let{user:e,loading:t}=n();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function d(){let{user:e,loading:t}=n(),[s,r]=(0,a.useState)(!1),[l,i]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||l,isAdmin:s}}},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var a=s(1469),r=s.n(a)},6779:(e,t,s)=>{"use strict";s.d(t,{CF:()=>c,Pn:()=>n,TK:()=>g,getWithdrawals:()=>x,hG:()=>m,lo:()=>o,nQ:()=>h,updateWithdrawalStatus:()=>u,x5:()=>d});var a=s(5317),r=s(6104),l=s(3592);let i=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=a.Dc.fromDate(t),n=await (0,a.getDocs)((0,a.collection)(r.db,l.COLLECTIONS.users)),o=n.size,d=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.users),(0,a._M)(l.FIELD_NAMES.joinedDate,">=",s)),c=(await (0,a.getDocs)(d)).size,h=0,x=0,g=0,m=0;n.forEach(e=>{var s;let a=e.data();h+=a[l.FIELD_NAMES.totalTranslations]||0,x+=a[l.FIELD_NAMES.wallet]||0;let r=null==(s=a[l.FIELD_NAMES.lastTranslationDate])?void 0:s.toDate();r&&r.toDateString()===t.toDateString()&&(g+=a[l.FIELD_NAMES.todayTranslations]||0)});try{let e=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.transactions),(0,a._M)(l.FIELD_NAMES.type,"==","translation_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{var s;let a=e.data(),r=null==(s=a[l.FIELD_NAMES.date])?void 0:s.toDate();r&&r>=t&&(m+=a[l.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let u=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),f=(await (0,a.getDocs)(u)).size,N=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.withdrawals),(0,a._M)("date",">=",s)),w=(await (0,a.getDocs)(N)).size,j={totalUsers:o,totalTranslations:h,totalEarnings:x,pendingWithdrawals:f,todayUsers:c,todayTranslations:g,todayEarnings:m,todayWithdrawals:w};return i.set(e,{data:j,timestamp:Date.now()}),j}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.users),(0,a.My)(l.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.users),(0,a.My)(l.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let i=await (0,a.getDocs)(s);return{users:i.docs.map(e=>{var t,s;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(s=e.data()[l.FIELD_NAMES.planExpiry])?void 0:s.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.users),(0,a.My)(l.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(s)).docs.map(e=>{var t,s;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(s=e.data()[l.FIELD_NAMES.planExpiry])?void 0:s.toDate()}}).filter(e=>{let s=String(e[l.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[l.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[l.FIELD_NAMES.mobile]||"").toLowerCase(),i=String(e[l.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||a.includes(t)||r.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.users),(0,a.My)(l.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>{var t,s;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[l.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(s=e.data()[l.FIELD_NAMES.planExpiry])?void 0:s.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function h(){try{let e=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.withdrawals),(0,a.My)("date","desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(r.db,l.COLLECTIONS.withdrawals),(0,a.My)("date","desc"),(0,a.HM)(t),(0,a.AB)(e)));let i=await (0,a.getDocs)(s);return{withdrawals:i.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function g(e,t){try{await (0,a.mZ)((0,a.H9)(r.db,l.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function m(e){try{await (0,a.kd)((0,a.H9)(r.db,l.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function u(e,t,n){try{let o=await (0,a.x7)((0,a.H9)(r.db,l.COLLECTIONS.withdrawals,e));if(!o.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:h}=o.data(),x={status:t,updatedAt:a.Dc.now()};if(n&&(x.adminNotes=n),await (0,a.mZ)((0,a.H9)(r.db,l.COLLECTIONS.withdrawals,e),x),"approved"===t&&"approved"!==h){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3592));await e(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===t&&"rejected"!==h){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3592));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7220:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(5155),r=s(2115),l=s(6874),i=s.n(l),n=s(6766),o=s(6681),d=s(6779),c=s(12);function h(){var e,t,s,l,h;let{user:x,loading:g,isAdmin:m}=(0,o.wC)(),[u,f]=(0,r.useState)(null),[N,w]=(0,r.useState)(!0),[j,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{m&&v()},[m]);let v=async()=>{try{w(!0);let e=await (0,d.Pn)();f(e)}catch(e){console.error("Error loading dashboard stats:",e)}finally{w(!1)}};return g||N?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ".concat(j?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,a.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:32,height:32,className:"mr-2"}),(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:"Instra Global Admin"})]}),(0,a.jsx)("nav",{className:"mt-8",children:(0,a.jsxs)("div",{className:"px-4 space-y-2",children:[(0,a.jsxs)(i(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,a.jsxs)(i(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,a.jsxs)(i(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,a.jsxs)(i(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,a.jsxs)(i(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,a.jsxs)(i(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,a.jsxs)(i(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,a.jsxs)(i(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,a.jsxs)(i(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]})]})}),(0,a.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,a.jsxs)("button",{onClick:()=>{(0,c._f)(null==x?void 0:x.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,a.jsxs)("div",{className:"lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsx)("button",{onClick:()=>p(!j),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,a.jsxs)("main",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u||null==(e=u.totalUsers)?void 0:e.toLocaleString())||"0"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-language text-green-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Translations"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u||null==(t=u.totalTranslations)?void 0:t.toLocaleString())||"0"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",(null==u||null==(s=u.totalEarnings)?void 0:s.toLocaleString())||"0"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u?void 0:u.pendingWithdrawals)||"0"})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:(null==u?void 0:u.todayUsers)||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600",children:(null==u||null==(l=u.todayTranslations)?void 0:l.toLocaleString())||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Translations Completed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",(null==u||null==(h=u.todayEarnings)?void 0:h.toLocaleString())||"0"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-red-600",children:(null==u?void 0:u.todayWithdrawals)||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(i(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,a.jsx)(i(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,a.jsx)(i(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,a.jsx)(i(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,a.jsx)(i(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,a.jsx)(i(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Update translations, wallet & active days via CSV"})]})]})})]})]})]}),j&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>p(!1)})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,3592,8441,1684,7358],()=>t(5487)),_N_E=e.O()}]);