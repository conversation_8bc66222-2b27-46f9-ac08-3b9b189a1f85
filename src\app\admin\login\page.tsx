'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { signInWithEmailAndPassword } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { useAuthState } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

export default function AdminLoginPage() {
  const { user, loading } = useAuthState()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  useEffect(() => {
    if (user && !loading) {
      // Check if user is admin
      const adminEmails = ['<EMAIL>', '<EMAIL>']
      if (adminEmails.includes(user.email || '')) {
        window.location.href = '/admin'
      } else {
        // Not an admin, sign out and show error
        auth.signOut()
        Swal.fire({
          icon: 'error',
          title: 'Access Denied',
          text: 'You do not have admin privileges',
        })
      }
    }
  }, [user, loading])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please fill in all fields',
      })
      return
    }

    setIsLoading(true)

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const user = userCredential.user
      
      // Check if user is admin
      const adminEmails = ['<EMAIL>', '<EMAIL>']
      if (!adminEmails.includes(user.email || '')) {
        await auth.signOut()
        throw new Error('Access denied. Admin privileges required.')
      }

      // Redirect will be handled by useEffect
    } catch (error: any) {
      console.error('Admin login error:', error)
      
      let message = 'An error occurred during login'
      
      if (error.message.includes('Access denied')) {
        message = 'Access denied. Admin privileges required.'
      } else {
        switch (error.code) {
          case 'auth/user-not-found':
            message = 'No admin account found with this email address'
            break
          case 'auth/wrong-password':
            message = 'Incorrect password'
            break
          case 'auth/invalid-email':
            message = 'Invalid email address'
            break
          case 'auth/user-disabled':
            message = 'This admin account has been disabled'
            break
          case 'auth/too-many-requests':
            message = 'Too many failed attempts. Please try again later'
            break
          default:
            message = error.message || 'Admin login failed'
        }
      }

      Swal.fire({
        icon: 'error',
        title: 'Admin Login Failed',
        text: message,
      })
      
      setPassword('')
    } finally {
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="glass-card w-full max-w-md p-8">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/img/instra-logo.svg"
              alt="Instra Global Logo"
              width={50}
              height={50}
              className="mr-3"
            />
            <span className="text-2xl font-bold text-white">Instra Global</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Admin Panel</h1>
          <p className="text-white/80">Sign in to access admin dashboard</p>
        </div>

        {/* Admin Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-white font-medium mb-2">
              <i className="fas fa-user-shield mr-2"></i>
              Admin Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="form-input"
              placeholder="Enter admin email"
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-white font-medium mb-2">
              <i className="fas fa-lock mr-2"></i>
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="form-input pr-12"
                placeholder="Enter admin password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
              >
                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full btn-primary flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Signing in...
              </>
            ) : (
              <>
                <i className="fas fa-sign-in-alt mr-2"></i>
                Admin Login
              </>
            )}
          </button>
        </form>

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30">
          <div className="flex items-center text-red-300">
            <i className="fas fa-shield-alt mr-2"></i>
            <span className="text-sm">
              This is a secure admin area. Only authorized personnel can access this panel.
            </span>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="text-white/80 hover:text-white transition-colors inline-flex items-center"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
