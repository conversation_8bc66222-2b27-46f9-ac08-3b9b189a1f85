'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword, signInWithEmailAndPassword, deleteUser } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp, collection, getDocs } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'

export default function DebugFirestorePage() {
  const [result, setResult] = useState('')
  const [isRunning, setIsRunning] = useState(false)

  const addToResult = (message: string) => {
    setResult(prev => prev + message + '\n')
    console.log(message)
  }

  const runDiagnostics = async () => {
    setIsRunning(true)
    setResult('')
    
    try {
      addToResult('🔍 Starting Firestore Diagnostics...')
      addToResult('=' .repeat(50))
      
      // Test 1: Check Firebase Configuration
      addToResult('\n📋 Test 1: Firebase Configuration')
      addToResult(`Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`)
      addToResult(`Auth Domain: ${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}`)
      addToResult(`API Key: ${process.env.NEXT_PUBLIC_FIREBASE_API_KEY?.substring(0, 10)}...`)
      
      // Test 2: Check Firestore Connection
      addToResult('\n🔗 Test 2: Firestore Connection')
      try {
        const testCollection = collection(db, 'test')
        addToResult('✅ Firestore connection established')
      } catch (error: any) {
        addToResult(`❌ Firestore connection failed: ${error.message}`)
        return
      }
      
      // Test 3: Check Authentication
      addToResult('\n🔐 Test 3: Authentication Test')
      const testEmail = `test-${Date.now()}@example.com`
      const testPassword = 'test123456'
      
      let testUser: any = null
      try {
        const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
        testUser = userCredential.user
        addToResult(`✅ Test user created: ${testUser.uid}`)
      } catch (error: any) {
        addToResult(`❌ User creation failed: ${error.message}`)
        return
      }
      
      // Test 4: Check Firestore Write Permissions
      addToResult('\n📝 Test 4: Firestore Write Test')
      const userData = {
        [FIELD_NAMES.name]: 'Debug Test User',
        [FIELD_NAMES.email]: testEmail,
        [FIELD_NAMES.mobile]: '9999999999',
        [FIELD_NAMES.referralCode]: 'DEBUG001',
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 2,
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalTranslations]: 0,
        [FIELD_NAMES.todayTranslations]: 0,
        [FIELD_NAMES.lastTranslationDate]: null,
        status: 'active'
      }
      
      try {
        const userDoc = doc(db, COLLECTIONS.users, testUser.uid)
        addToResult(`📍 Document path: ${userDoc.path}`)
        addToResult(`📊 Data to write: ${JSON.stringify(userData, null, 2)}`)
        
        await setDoc(userDoc, userData)
        addToResult('✅ Document created successfully')
      } catch (error: any) {
        addToResult(`❌ Document creation failed: ${error.message}`)
        addToResult(`❌ Error code: ${error.code}`)
        addToResult(`❌ Full error: ${JSON.stringify(error, null, 2)}`)
      }
      
      // Test 5: Check Firestore Read Permissions
      addToResult('\n📖 Test 5: Firestore Read Test')
      try {
        const userDoc = doc(db, COLLECTIONS.users, testUser.uid)
        const docSnap = await getDoc(userDoc)
        
        if (docSnap.exists()) {
          addToResult('✅ Document read successfully')
          addToResult(`📄 Document data: ${JSON.stringify(docSnap.data(), null, 2)}`)
        } else {
          addToResult('❌ Document does not exist after creation')
        }
      } catch (error: any) {
        addToResult(`❌ Document read failed: ${error.message}`)
      }
      
      // Test 6: Check Collection Access
      addToResult('\n📚 Test 6: Collection Access Test')
      try {
        const usersCollection = collection(db, COLLECTIONS.users)
        const snapshot = await getDocs(usersCollection)
        addToResult(`✅ Collection accessible, found ${snapshot.size} documents`)
      } catch (error: any) {
        addToResult(`❌ Collection access failed: ${error.message}`)
      }
      
      // Test 7: Check Field Names
      addToResult('\n🏷️ Test 7: Field Names Check')
      addToResult(`COLLECTIONS.users: ${COLLECTIONS.users}`)
      Object.entries(FIELD_NAMES).forEach(([key, value]) => {
        addToResult(`FIELD_NAMES.${key}: ${value}`)
      })

      // Test 8: Simple Document Test
      addToResult('\n📝 Test 8: Simple Document Test')
      try {
        const simpleData = {
          name: 'Simple Test',
          email: testEmail,
          created: new Date().toISOString()
        }

        const simpleDoc = doc(db, 'test-collection', testUser.uid)
        await setDoc(simpleDoc, simpleData)
        addToResult('✅ Simple document created successfully')

        const simpleSnap = await getDoc(simpleDoc)
        if (simpleSnap.exists()) {
          addToResult('✅ Simple document verified')
        } else {
          addToResult('❌ Simple document not found')
        }
      } catch (error: any) {
        addToResult(`❌ Simple document test failed: ${error.message}`)
      }
      
      // Cleanup
      addToResult('\n🧹 Cleanup: Deleting test user')
      try {
        await deleteUser(testUser)
        addToResult('✅ Test user deleted successfully')
      } catch (error: any) {
        addToResult(`⚠️ Test user deletion failed: ${error.message}`)
      }
      
      addToResult('\n🎉 Diagnostics completed!')
      
    } catch (error: any) {
      addToResult(`💥 Unexpected error: ${error.message}`)
      addToResult(`💥 Stack trace: ${error.stack}`)
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <div className="glass-card p-6 mb-6">
          <h1 className="text-2xl font-bold text-white mb-4">
            🔍 Firestore Debug Tool
          </h1>
          <p className="text-white/80 mb-6">
            This tool will run comprehensive diagnostics to identify why Firestore user creation is failing.
          </p>
          
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className="btn-primary mb-6"
          >
            {isRunning ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Running Diagnostics...
              </>
            ) : (
              <>
                <i className="fas fa-bug mr-2"></i>
                Run Firestore Diagnostics
              </>
            )}
          </button>
          
          {result && (
            <div className="bg-black/50 p-4 rounded-lg">
              <h3 className="text-white font-semibold mb-2">Diagnostic Results:</h3>
              <pre className="text-green-400 text-sm whitespace-pre-wrap font-mono overflow-x-auto">
                {result}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
