'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'

export default function TestFirestorePage() {
  const [output, setOutput] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addOutput = (message: string) => {
    setOutput(prev => prev + message + '\n')
    console.log(message)
  }

  const testFirestoreRules = async () => {
    setIsLoading(true)
    setOutput('')
    
    try {
      addOutput('🧪 Starting Firestore Rules Test...')
      
      // Test 1: Create a test user
      const testEmail = `test${Date.now()}@example.com`
      const testPassword = 'test123456'
      
      addOutput(`📧 Creating test user: ${testEmail}`)
      
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      const user = userCredential.user
      
      addOutput(`✅ Firebase Auth user created: ${user.uid}`)
      
      // Test 2: Try to create Firestore document
      const userData = {
        [FIELD_NAMES.name]: 'Test User',
        [FIELD_NAMES.email]: testEmail,
        [FIELD_NAMES.mobile]: '9876543210',
        [FIELD_NAMES.referralCode]: `TEST${Date.now()}`,
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 0,
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalTranslations]: 0,
        [FIELD_NAMES.todayTranslations]: 0,
        [FIELD_NAMES.lastTranslationDate]: null,
        status: 'active'
      }
      
      addOutput('📝 Creating Firestore document...')
      addOutput(`User UID: ${user.uid}`)
      addOutput(`Auth state: ${user.email} (verified: ${user.emailVerified})`)
      
      const userDocRef = doc(db, COLLECTIONS.users, user.uid)
      await setDoc(userDocRef, userData)
      
      addOutput('✅ Firestore document created successfully!')
      
      // Test 3: Verify document exists
      const verifyDoc = await getDoc(userDocRef)
      if (verifyDoc.exists()) {
        addOutput('✅ Document verification successful!')
        addOutput(`Document data: ${JSON.stringify(verifyDoc.data(), null, 2)}`)
      } else {
        addOutput('❌ Document verification failed!')
      }
      
      addOutput('🎉 All tests passed!')
      
    } catch (error: any) {
      addOutput(`❌ Test failed: ${error.message}`)
      addOutput(`Error code: ${error.code}`)
      addOutput(`Full error: ${JSON.stringify(error, null, 2)}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testAdminLogin = async () => {
    setIsLoading(true)
    setOutput('')
    
    try {
      addOutput('🔐 Testing admin login...')
      
      const adminCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', '123456')
      const adminUser = adminCredential.user
      
      addOutput(`✅ Admin logged in: ${adminUser.email}`)
      addOutput(`Admin UID: ${adminUser.uid}`)
      addOutput(`Email verified: ${adminUser.emailVerified}`)
      
      // Test admin document creation
      const testData = {
        test: true,
        timestamp: Timestamp.now(),
        adminEmail: adminUser.email
      }
      
      const testDocRef = doc(db, 'test_collection', 'admin_test')
      await setDoc(testDocRef, testData)
      
      addOutput('✅ Admin can create documents!')
      
    } catch (error: any) {
      addOutput(`❌ Admin test failed: ${error.message}`)
      addOutput(`Error code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Firestore Rules Test</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <button
            onClick={testFirestoreRules}
            disabled={isLoading}
            className="btn-primary"
          >
            {isLoading ? 'Testing...' : 'Test User Registration'}
          </button>
          
          <button
            onClick={testAdminLogin}
            disabled={isLoading}
            className="btn-secondary"
          >
            {isLoading ? 'Testing...' : 'Test Admin Login'}
          </button>
        </div>
        
        <div className="glass-card p-4">
          <h2 className="text-lg font-semibold text-white mb-4">Test Output:</h2>
          <pre className="text-white/80 text-sm whitespace-pre-wrap bg-black/20 p-4 rounded max-h-96 overflow-y-auto">
            {output || 'Click a test button to start...'}
          </pre>
        </div>
        
        <div className="mt-6">
          <a href="/register" className="btn-primary mr-4">
            Go to Registration
          </a>
          <a href="/admin/login" className="btn-secondary">
            Go to Admin Login
          </a>
        </div>
      </div>
    </div>
  )
}
